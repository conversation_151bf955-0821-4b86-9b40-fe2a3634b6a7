"""
🔧 AZR INTEGRATION MANAGER - INTÉGRATION COMPLÈTE DU SYSTÈME UNIFIÉ
===================================================================

Gestionnaire d'intégration qui remplace tous les paramètres dispersés
par le système unifié AZR dans tous les fichiers du projet.

INTÉGRATION COMPLÈTE :
- azr_core.py → Paramètres AZR unifiés
- conditions_wait_strategiques_azr.py → Paramètres WAIT unifiés
- models.py → Paramètres modèles unifiés
- pattern_proposer.py → Paramètres patterns unifiés
- main_interface.py → Interface unifiée
- parameters.py → REMPLACÉ par système unifié

STRUCTURE D'INTÉGRATION :
1. Remplacement des imports
2. Injection des paramètres unifiés
3. Synchronisation de l'état partagé
4. Maintien de la compatibilité
"""

import logging
from typing import Dict, Any, Optional, List
from azr_unified_parameters import (
    get_azr_orchestrator, 
    update_azr_state,
    get_wait_params,
    get_confidence_params,
    get_streak_params,
    get_pattern_params
)

logger = logging.getLogger(__name__)

class AZRIntegrationManager:
    """
    🔧 GESTIONNAIRE D'INTÉGRATION AZR
    
    Coordonne l'intégration du système unifié dans tous les composants.
    Assure la synchronisation et la compatibilité.
    """
    
    def __init__(self):
        """Initialise le gestionnaire d'intégration"""
        self.orchestrator = get_azr_orchestrator()
        self.integrated_components = set()
        self.compatibility_layer = {}
        
        logger.info("🔧 AZR Integration Manager initialisé")
    
    def integrate_azr_core(self) -> Dict[str, Any]:
        """
        🎯 INTÉGRATION AZR CORE
        Remplace tous les paramètres codés en dur dans azr_core.py
        """
        # Récupération paramètres unifiés
        all_params = self.orchestrator.get_all_parameters()
        
        # Paramètres spécifiques pour AZR Core
        azr_core_params = {
            # Apprentissage
            'learning_rate': all_params['learning']['learning_rate'],
            'adaptation_rate': all_params['learning']['adaptation_rate'],
            'exploration_rate': all_params['learning']['exploration_rate'],
            'performance_target': all_params['learning']['performance_target'],
            
            # Confiance
            'confidence_threshold': all_params['confidence']['base_confidence_threshold'],
            'confidence_adjustment_limit': all_params['confidence']['adjustment_limit'],
            'min_confidence': all_params['confidence']['min_confidence'],
            'max_confidence': all_params['confidence']['max_confidence'],
            
            # Streaks et Patterns
            'significant_streak_length': all_params['streaks']['significant_streak_length'],
            'alternation_threshold': all_params['patterns']['alternation_threshold'],
            'anti_streak_factor': all_params['streaks']['anti_streak_factor'],
            
            # Phase et état
            'current_phase': all_params['shared_state']['phase'],
            'coherence_factor': all_params['foundation']['golden_ratio'],
            'volatility_sensitivity': all_params['foundation']['adaptation_speed']
        }
        
        self.integrated_components.add('azr_core')
        logger.info("✅ AZR Core intégré avec paramètres unifiés")
        
        return azr_core_params
    
    def integrate_wait_system(self) -> Dict[str, Any]:
        """
        🚫 INTÉGRATION SYSTÈME WAIT
        Remplace tous les paramètres WAIT par le système adaptatif
        """
        wait_params = self.orchestrator.get_wait_parameters()
        
        # Paramètres spécifiques pour système WAIT
        wait_integration = {
            **wait_params,
            
            # Conditions WAIT adaptatives
            'poor_performance_threshold': wait_params['performance_target'] - 0.05,
            'volatility_threshold': 0.3,  # Basé sur volatility_factor
            'confidence_threshold': wait_params['base_wait_threshold'],
            
            # Poids adaptatifs des conditions
            'performance_weight': 0.40,  # Poids principal
            'volatility_weight': 0.25,
            'confidence_weight': 0.20,
            'pattern_weight': 0.15,
            
            # Adaptation dynamique
            'adaptation_enabled': True,
            'real_time_adjustment': True
        }
        
        self.integrated_components.add('wait_system')
        logger.info("✅ Système WAIT intégré avec adaptation dynamique")
        
        return wait_integration
    
    def integrate_models(self) -> Dict[str, Any]:
        """
        🤖 INTÉGRATION MODÈLES
        Paramètres unifiés pour LSTM, LGBM, Markov, Ensemble
        """
        model_params = self.orchestrator.get_model_parameters()
        
        # Configuration modèles intégrée
        models_integration = {
            # LSTM unifié
            'lstm': {
                'sequence_length': model_params['lstm_sequence_length'],
                'hidden_size': model_params['lstm_hidden_size'],
                'learning_rate': model_params['lstm_learning_rate'],
                'dropout': 0.1,  # Basé sur coherence_factor
                'batch_size': 32
            },
            
            # LGBM unifié
            'lgbm': {
                'n_estimators': model_params['lgbm_n_estimators'],
                'max_depth': model_params['lgbm_max_depth'],
                'learning_rate': model_params['lgbm_learning_rate'],
                'subsample': 0.8,
                'colsample_bytree': 0.8
            },
            
            # Markov unifié
            'markov': {
                'order': model_params['markov_order'],
                'smoothing': model_params['markov_smoothing'],
                'memory_length': 50
            },
            
            # Ensemble unifié
            'ensemble': {
                'weights': model_params['ensemble_weights'],
                'rebalancing_frequency': model_params['rebalancing_frequency'],
                'confidence_aggregation': 'weighted_average',
                'uncertainty_estimation': 'variance_based'
            }
        }
        
        self.integrated_components.add('models')
        logger.info("✅ Modèles intégrés avec paramètres adaptatifs")
        
        return models_integration
    
    def integrate_patterns(self) -> Dict[str, Any]:
        """
        🔍 INTÉGRATION DÉTECTION PATTERNS
        Paramètres unifiés pour détection de motifs
        """
        pattern_params = self.orchestrator.get_pattern_parameters()
        streak_params = self.orchestrator.get_streak_parameters()
        
        # Configuration patterns intégrée
        patterns_integration = {
            # Streaks
            'streaks': {
                'significant_length': streak_params['significant_streak_length'],
                'alternation_length': streak_params['significant_alternation_length'],
                'anti_streak_factor': streak_params['anti_streak_factor'],
                'continuation_probability': streak_params['continuation_base_prob'],
                'length_penalty': streak_params['length_penalty_rate']
            },
            
            # Patterns généraux
            'patterns': {
                'min_length': pattern_params['min_pattern_length'],
                'max_length': pattern_params['max_pattern_length'],
                'strength_factor': pattern_params['pattern_strength_factor'],
                'validation_threshold': pattern_params['validation_threshold'],
                'confidence_boost': pattern_params['confidence_boost']
            },
            
            # Cycles et répétitions
            'cycles': {
                'detection_enabled': True,
                'min_cycle_length': 3,
                'max_cycle_length': 12,
                'confidence_threshold': 0.7
            }
        }
        
        self.integrated_components.add('patterns')
        logger.info("✅ Détection patterns intégrée avec paramètres unifiés")
        
        return patterns_integration
    
    def create_compatibility_layer(self) -> Dict[str, Any]:
        """
        🔌 COUCHE DE COMPATIBILITÉ
        Maintient la compatibilité avec l'ancien système
        """
        all_params = self.orchestrator.get_all_parameters()
        
        # Mapping de compatibilité
        compatibility = {
            # Anciens noms → Nouveaux paramètres
            'confidence_threshold': all_params['confidence']['base_confidence_threshold'],
            'learning_rate': all_params['learning']['learning_rate'],
            'adaptation_rate': all_params['learning']['adaptation_rate'],
            'exploration_rate': all_params['learning']['exploration_rate'],
            
            # Paramètres patterns (anciens noms)
            'min_pattern_length': all_params['patterns']['min_pattern_length'],
            'max_pattern_length': all_params['patterns']['max_pattern_length'],
            'validation_window': 30,  # Calculé dynamiquement
            
            # Paramètres système
            'max_session_rounds': 60,
            'max_history_length': 100,
            'buffer_size': 5,
            
            # Seuils adaptatifs
            'min_confidence': all_params['confidence']['min_confidence'],
            'max_confidence': all_params['confidence']['max_confidence'],
            'unlimited_performance': True
        }
        
        self.compatibility_layer = compatibility
        logger.info("✅ Couche de compatibilité créée")
        
        return compatibility
    
    def get_integrated_config(self, component: str) -> Dict[str, Any]:
        """
        📋 RÉCUPÈRE CONFIGURATION INTÉGRÉE
        Retourne la configuration unifiée pour un composant
        """
        if component == 'azr_core':
            return self.integrate_azr_core()
        elif component == 'wait_system':
            return self.integrate_wait_system()
        elif component == 'models':
            return self.integrate_models()
        elif component == 'patterns':
            return self.integrate_patterns()
        elif component == 'compatibility':
            return self.create_compatibility_layer()
        else:
            logger.warning(f"Composant inconnu: {component}")
            return {}
    
    def sync_all_components(self, prediction_result: Dict[str, Any], actual_outcome: Optional[int] = None):
        """
        🔄 SYNCHRONISE TOUS LES COMPOSANTS
        Met à jour l'état partagé et synchronise tous les composants
        """
        # Mise à jour état partagé
        update_azr_state(prediction_result, actual_outcome)
        
        # Notification aux composants intégrés
        for component in self.integrated_components:
            logger.debug(f"🔄 Synchronisation {component}")
        
        logger.debug("🔄 Tous les composants synchronisés")
    
    def get_integration_status(self) -> Dict[str, Any]:
        """
        📊 STATUT D'INTÉGRATION
        Retourne l'état de l'intégration
        """
        return {
            'integrated_components': list(self.integrated_components),
            'orchestrator_status': self.orchestrator.get_diagnostic_info(),
            'compatibility_layer_active': bool(self.compatibility_layer),
            'total_components': len(self.integrated_components),
            'integration_complete': len(self.integrated_components) >= 4
        }


# ═══════════════════════════════════════════════════════════════════
# INSTANCE GLOBALE ET FONCTIONS D'ACCÈS
# ═══════════════════════════════════════════════════════════════════

_integration_manager = None

def get_integration_manager() -> AZRIntegrationManager:
    """Retourne l'instance globale du gestionnaire d'intégration"""
    global _integration_manager
    if _integration_manager is None:
        _integration_manager = AZRIntegrationManager()
    return _integration_manager

def get_unified_config(component: str) -> Dict[str, Any]:
    """Fonction d'accès rapide à la configuration unifiée"""
    return get_integration_manager().get_integrated_config(component)

def sync_azr_system(prediction_result: Dict[str, Any], actual_outcome: Optional[int] = None):
    """Fonction de synchronisation globale"""
    return get_integration_manager().sync_all_components(prediction_result, actual_outcome)

# Export des fonctions principales
__all__ = [
    'AZRIntegrationManager',
    'get_integration_manager',
    'get_unified_config',
    'sync_azr_system'
]
