# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 231 à 247
# Type: Méthode de la classe AZRBaccaratPredictor

    def get_learning_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques d'apprentissage"""
        base_stats = {
            'predictor_initialized': self.is_initialized,
            'total_sessions': len(self.prediction_history),
            'performance_metrics': self.performance_metrics.copy()
        }

        # Statistiques du raisonneur adaptatif
        reasoner_stats = self.adaptive_reasoner.get_reasoning_statistics()
        base_stats['reasoner_statistics'] = reasoner_stats

        # Historique d'apprentissage du moteur self-play
        learning_history = self.self_play_engine.get_learning_history()
        base_stats['learning_history'] = learning_history

        return base_stats