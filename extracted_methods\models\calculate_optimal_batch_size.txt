# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 824 à 836
# Type: Méthode

def calculate_optimal_batch_size(sample_size: int, config_batch_size: Optional[int] = None) -> int:
    """
    Fonction de compatibilité - AZR n'utilise pas de batch size

    Args:
        sample_size: Taille de l'échantillon (ignoré par AZR)
        config_batch_size: Taille de batch configurée (ignoré par AZR)

    Returns:
        Valeur par défaut pour compatibilité
    """
    logger.info("AZR ne nécessite pas de batch size - Fonction de compatibilité")
    return 32  # Valeur par défaut pour compatibilité