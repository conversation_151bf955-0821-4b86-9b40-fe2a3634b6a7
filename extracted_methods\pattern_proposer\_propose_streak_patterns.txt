# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 249 à 275
# Type: Méthode de la classe BaccaratPatternProposer

    def _propose_streak_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns de séries"""
        patterns = []

        if len(results) < 3:
            return patterns

        # Détection séries actuelles et récentes
        current_streak = self._get_current_streak(results)
        recent_streaks = self._analyze_recent_streaks(results)

        if current_streak['length'] >= 2:
            # Prédiction continuation ou rupture
            continuation_prob = self._calculate_streak_continuation_probability(
                current_streak, recent_streaks
            )

            patterns.append({
                'type': 'streak',
                'pattern': 'continuation' if continuation_prob > 0.5 else 'break',
                'current_streak': current_streak,
                'continuation_probability': continuation_prob,
                'confidence': abs(continuation_prob - 0.5) * 2,
                'predicted_outcome': current_streak['outcome'] if continuation_prob > 0.5 else 1 - current_streak['outcome']
            })

        return patterns