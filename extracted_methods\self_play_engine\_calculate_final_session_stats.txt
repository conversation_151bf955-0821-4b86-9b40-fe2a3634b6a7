# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 418 à 461
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def _calculate_final_session_stats(self) -> Dict[str, Any]:
        """Calcule statistiques finales de session"""
        if not self.current_session:
            return {}

        session = self.current_session
        performance = session['session_performance']

        if not performance:
            return {'accuracy': 0.0}

        # Statistiques de base
        total_rounds = len(performance)
        correct_predictions = sum(p['correct'] for p in performance)
        accuracy = correct_predictions / total_rounds if total_rounds > 0 else 0.0

        # Métriques avancées
        avg_confidence = np.mean([p['confidence'] for p in performance])
        avg_uncertainty = np.mean([p['uncertainty'] for p in performance])
        confidence_variance = np.var([p['confidence'] for p in performance])

        # Analyse tendances
        if total_rounds >= 10:
            first_half = performance[:total_rounds//2]
            second_half = performance[total_rounds//2:]

            first_half_accuracy = np.mean([p['correct'] for p in first_half])
            second_half_accuracy = np.mean([p['correct'] for p in second_half])
            improvement = second_half_accuracy - first_half_accuracy
        else:
            improvement = 0.0

        return {
            'total_rounds': total_rounds,
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'avg_confidence': avg_confidence,
            'avg_uncertainty': avg_uncertainty,
            'confidence_variance': confidence_variance,
            'improvement': improvement,
            'adaptations_made': session['adaptations_made'],
            'patterns_discovered': session['patterns_discovered'],
            'duration': session.get('duration', 0.0)
        }