# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 459 à 480
# Type: Méthode de la classe PredictionCalculator

    def _determine_recommendation(self, prediction: Dict[str, float], confidence: float) -> str:
        """Détermine recommandation basée sur prédiction et confiance"""
        try:
            default_prob = global_config.calculations.max_prob_center
            player_prob = prediction.get('player', default_prob)
            banker_prob = prediction.get('banker', default_prob)

            # Seuil de confiance minimum
            if confidence < self.confidence_calc.confidence_threshold:
                return global_config.calculations.wait_recommendation

            # Recommandation basée sur probabilité la plus élevée
            if player_prob > banker_prob:
                return global_config.calculations.player_recommendation
            elif banker_prob > player_prob:
                return global_config.calculations.banker_recommendation
            else:
                return global_config.calculations.wait_recommendation

        except Exception as e:
            logger.error(f"Erreur recommandation: {e}")
            return global_config.calculations.wait_recommendation