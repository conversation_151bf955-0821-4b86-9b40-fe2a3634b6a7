# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 353 à 362
# Type: Méthode de la classe NewGameHandler

    def get_game_status(self) -> Dict[str, Any]:
        """Obtient statut partie actuelle"""
        return {
            'game_active': self.game_active,
            'game_round': self.game_round,
            'game_phase': self.game_phase,
            'calibration_in_progress': self.calibration_in_progress,
            'calibration_complete': self.game_calibrator.calibration_complete,
            'ready_for_game': self.game_calibrator.is_ready_for_game()
        }