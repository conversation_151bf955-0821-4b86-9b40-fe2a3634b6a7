# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 1613 à 1651
# Type: Méthode de la classe AZRSystem

    def _calculate_progressive_reward(self) -> float:
        """Calcule récompense progressive avec pénalités pour séquences d'échecs"""
        try:
            if len(self.training_history['prediction_accuracy']) < 2:
                return 0.0

            # Calcul streak actuelle (correcte)
            current_correct_streak = 0
            for i in range(len(self.training_history['prediction_accuracy']) - 1, -1, -1):
                if self.training_history['prediction_accuracy'][i] == 1.0:
                    current_correct_streak += 1
                else:
                    break

            # Calcul streak d'échecs actuelle
            current_failure_streak = 0
            for i in range(len(self.training_history['prediction_accuracy']) - 1, -1, -1):
                if self.training_history['prediction_accuracy'][i] == 0.0:
                    current_failure_streak += 1
                else:
                    break

            # Bonus pour séquences correctes
            streak_bonus = min(1.0, current_correct_streak * self.streak_bonus_factor)

            # PÉNALITÉ FORTE pour séquences d'échecs (escalade)
            failure_penalty = min(1.5, current_failure_streak * 0.3)

            # Malus pour mauvaise calibration récente
            recent_window = min(10, len(self.training_history['prediction_accuracy']))
            recent_accuracy = np.mean(self.training_history['prediction_accuracy'][-recent_window:])
            calibration_penalty = abs(recent_accuracy - 0.6) * self.calibration_penalty_factor

            progressive_reward = streak_bonus - failure_penalty - calibration_penalty
            return max(-1.5, min(1.5, progressive_reward))  # ✅ PERMET RÉCOMPENSES NÉGATIVES

        except Exception as e:
            logger.error(f"Erreur calcul récompense progressive: {e}")
            return -0.2