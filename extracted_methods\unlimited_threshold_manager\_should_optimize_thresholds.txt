# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 166 à 180
# Type: Méthode de la classe UnlimitedThresholdManager

    def _should_optimize_thresholds(self, round_number: int) -> bool:
        """Détermine si optimisation seuils nécessaire"""
        rounds_since_last = round_number - self.last_optimization_round
        
        # Optimisation fréquente en mode illimité
        if rounds_since_last >= self.optimization_frequency:
            return True
        
        # Optimisation immédiate si performance dégradée
        if len(self.performance_history) >= 5:
            recent_accuracy = self._calculate_recent_accuracy(5)
            if recent_accuracy < self.best_performance_achieved * 0.9:  # Dégradation 10%+
                return True
        
        return False