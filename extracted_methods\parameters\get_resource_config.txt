# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\parameters.py
# Lignes: 280 à 288
# Type: Méthode de la classe GlobalConfig

    def get_resource_config(self) -> dict:
        """Obtient configuration ressources système"""
        return {
            'max_ram_gb': self.azr.max_ram_usage_gb,
            'use_all_cores': self.azr.use_all_cpu_cores,
            'high_performance': self.azr.high_performance_mode,
            'pattern_cache_size': self.azr.pattern_cache_size,
            'performance_cache_size': self.azr.performance_cache_size
        }