# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 659 à 705
# Type: Méthode de la classe BaccaratPredictorApp

    def _show_prediction_details(self, prediction):
        """Affiche détails prédiction intelligente dans interface"""
        try:
            if not prediction:
                return

            # Affichage méthode utilisée
            method = prediction.get('method', 'unknown')
            self.method_label.config(text=f"Méthode: {method}")

            # Affichage analyse intelligente si disponible
            if 'analysis_details' in prediction:
                analysis = prediction['analysis_details']
                pattern_adj = analysis.get('pattern_adjustment', 0.0)
                streak_adj = analysis.get('streak_adjustment', 0.0)
                alternation_adj = analysis.get('alternation_adjustment', 0.0)

                # Affichage détaillé de l'analyse
                analysis_text = f"Patterns: {pattern_adj:+.3f} | Streaks: {streak_adj:+.3f} | Alt: {alternation_adj:+.3f}"
                self.azr_info_label.config(text=f"Analyse: {analysis_text}")

                # Ajout détails dans zone info
                details_text = f"📊 Analyse intelligente:\n"
                details_text += f"   • Ajustement patterns: {pattern_adj:+.3f}\n"
                details_text += f"   • Ajustement streaks: {streak_adj:+.3f}\n"
                details_text += f"   • Ajustement alternance: {alternation_adj:+.3f}\n"

                if 'azr_improvements' in prediction:
                    azr_info = prediction['azr_improvements']
                    azr_bias = azr_info.get('prediction_bias', 0.0)
                    confidence_boost = azr_info.get('confidence_adjustment', 0.0)
                    details_text += f"   • Biais AZR appris: {azr_bias:+.3f}\n"
                    details_text += f"   • Boost confiance AZR: {confidence_boost:+.3f}\n"

                self.info_text.insert(tk.END, details_text)
                self.info_text.see(tk.END)

            elif 'azr_improvements' in prediction:
                # Fallback pour affichage AZR basique
                azr_info = prediction['azr_improvements']
                confidence_adj = azr_info.get('confidence_adjustment', 0.0)
                self.azr_info_label.config(
                    text=f"AZR: Ajust. confiance {confidence_adj:+.3f}"
                )

        except Exception as e:
            logger.error(f"Erreur affichage détails: {e}")