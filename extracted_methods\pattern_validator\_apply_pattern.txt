# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 103 à 120
# Type: Méthode de la classe BaccaratPatternValidator

    def _apply_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique un pattern pour générer une prédiction"""
        pattern_type = pattern['type']

        if pattern_type == 'sequence':
            return self._apply_sequence_pattern(pattern, test_data)
        elif pattern_type == 'frequency':
            return self._apply_frequency_pattern(pattern, test_data)
        elif pattern_type == 'trend':
            return self._apply_trend_pattern(pattern, test_data)
        elif pattern_type == 'alternation':
            return self._apply_alternation_pattern(pattern, test_data)
        elif pattern_type == 'streak':
            return self._apply_streak_pattern(pattern, test_data)
        elif pattern_type == 'cycle':
            return self._apply_cycle_pattern(pattern, test_data)
        else:
            return self._apply_default_pattern(pattern, test_data)