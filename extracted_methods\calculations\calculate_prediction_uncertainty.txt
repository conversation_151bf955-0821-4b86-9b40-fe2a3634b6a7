# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 503 à 549
# Type: Méthode de la classe UncertaintyCalculator

    def calculate_prediction_uncertainty(self, predictions: Dict[str, Dict[str, float]],
                                       individual_uncertainties: Dict[str, float] = None,
                                       weights: Dict[str, float] = None) -> float:
        """
        FORMULE UNIFIÉE D'INCERTITUDE ENSEMBLE - Basée sur recherches internationales

        Formule : Incertitude = α × Incertitude_épistémique + β × Incertitude_aléatoire
        """
        try:
            default_prob = global_config.calculations.max_prob_center
            if not predictions or len(predictions) < global_config.calculations.min_predictions_for_uncertainty:
                return default_prob

            # 1. INCERTITUDE ÉPISTÉMIQUE (variance entre modèles - connaissance)
            epistemic_uncertainty = self._calculate_epistemic_uncertainty(predictions, weights)

            # 2. INCERTITUDE ALÉATOIRE (moyenne des incertitudes individuelles - données)
            if individual_uncertainties:
                aleatoric_uncertainty = self._calculate_aleatoric_uncertainty(individual_uncertainties, weights)
            else:
                # Fallback : utiliser variance intra-prédictions
                aleatoric_uncertainty = self._calculate_fallback_aleatoric_uncertainty(predictions)

            # ✅ 3. COEFFICIENTS ADAPTATIFS (basés sur nombre de modèles et consensus)
            num_models = len(predictions)

            # Alpha adaptatif : plus de modèles = plus d'importance à l'épistémique
            alpha = 0.3 + min(0.4, num_models * 0.1)  # 0.3 à 0.7

            # Beta adaptatif : inversement proportionnel au consensus
            model_consensus = 1.0 - epistemic_uncertainty  # Plus de consensus = moins d'aléatoire
            beta = 0.5 + (1.0 - model_consensus) * 0.3  # 0.5 à 0.8

            # ✅ 4. FORMULE UNIFIÉE STABILISÉE
            total_uncertainty = alpha * epistemic_uncertainty + beta * aleatoric_uncertainty

            # Normalisation adaptative (évite division par zéro)
            total_weight = alpha + beta
            normalized_uncertainty = total_uncertainty / max(total_weight, 0.1)

            return np.clip(normalized_uncertainty,
                          global_config.calculations.confidence_min,
                          global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur calcul incertitude unifiée: {e}")
            return global_config.calculations.max_prob_center