# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 265 à 291
# Type: Méthode de la classe AZRBaccaratPredictor

    def _update_performance_metrics(self, round_result: Dict[str, Any]):
        """Met à jour métriques de performance"""
        self.performance_metrics['total_predictions'] += 1

        if round_result.get('prediction_correct', False):
            self.performance_metrics['correct_predictions'] += 1

        # Calcul accuracy
        total = self.performance_metrics['total_predictions']
        correct = self.performance_metrics['correct_predictions']
        self.performance_metrics['accuracy'] = correct / total if total > 0 else 0.0

        # Historique confiance et incertitude
        confidence = round_result.get('confidence', 0.5)
        uncertainty = round_result.get('uncertainty', 0.5)

        self.performance_metrics['confidence_history'].append(confidence)
        self.performance_metrics['uncertainty_history'].append(uncertainty)

        # Limitation taille historiques
        max_history = 100
        if len(self.performance_metrics['confidence_history']) > max_history:
            self.performance_metrics['confidence_history'] = \
                self.performance_metrics['confidence_history'][-max_history:]
        if len(self.performance_metrics['uncertainty_history']) > max_history:
            self.performance_metrics['uncertainty_history'] = \
                self.performance_metrics['uncertainty_history'][-max_history:]