# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 631 à 646
# Type: Méthode de la classe BaccaratPatternProposer

    def _update_cycle_length_performance(self, length: int, success: bool):
        """Met à jour performance d'une longueur de cycle"""
        if length not in self.cycle_length_performance:
            self.cycle_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }

        if success:
            self.cycle_length_performance[length]['successes'] += 1

        # Recalcul accuracy
        perf = self.cycle_length_performance[length]
        if perf['attempts'] > 0:
            perf['accuracy'] = perf['successes'] / perf['attempts']