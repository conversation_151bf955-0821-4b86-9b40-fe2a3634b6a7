# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 200 à 216
# Type: Méthode de la classe NewGameHandler

    def _update_interface_status(self, status: str):
        """Met à jour statut dans interface"""
        try:
            # Mise à jour statut calibration AZR
            if hasattr(self.main_interface, 'azr_calibration_status'):
                self.main_interface.azr_calibration_status.set(status)

            # Mise à jour dans logs si disponible
            if hasattr(self.main_interface, 'log_message'):
                self.main_interface.log_message(status)

            # Force mise à jour interface
            if hasattr(self.main_interface, 'root'):
                self.main_interface.root.update_idletasks()

        except Exception as e:
            logger.error(f"Erreur mise à jour interface: {e}")