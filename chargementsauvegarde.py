"""
SYSTÈME DE CHARGEMENT ET SAUVEGARDE UNIFIÉ - NOUVEAU PRÉDICTEUR BACCARAT
========================================================================

Module unifié pour toutes les opérations de persistence basé sur l'analyse exhaustive
de 80+ fichiers et 12 domaines de l'ancien système. Architecture optimisée avec
patterns Factory, Observer, Strategy et interfaces abstraites.

DOMAINES UNIFIÉS:
- Sauvegarde/Chargement État Système (unified_save, _perform_save, _load_latest_state)
- Gestion Données Historiques (load_historical_data, _load_historical_txt)
- Sauvegarde Modèles ML (save_model, load_model, versioning)
- Paramètres Configuration (load_params_from_file, save_params_to_file)
- Utilitaires Fichiers (safe_file_write, safe_file_read, backup)
- Export/Import, Cache, Logging, Sérialisation avancée

ARCHITECTURE: 9 sections standardisées selon squelettes optimaux
"""

# ═══════════════════════════════════════════════════════════════════
# 1. IMPORTS ET CONFIGURATION
# ═══════════════════════════════════════════════════════════════════

import os
import json
import pickle
import joblib
import gzip
import shutil
import logging
import threading
import time
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Tuple, Protocol, runtime_checkable
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum
import numpy as np

# Import configuration centralisée
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "parameters"))
from parameters import global_config

logger = logging.getLogger(__name__)


# ═══════════════════════════════════════════════════════════════════
# 2. INTERFACES ET PROTOCOLES ABSTRAITS
# ═══════════════════════════════════════════════════════════════════

@runtime_checkable
class IPersistable(Protocol):
    """Interface pour objets persistables"""
    def to_dict(self) -> Dict[str, Any]: ...
    def from_dict(self, data: Dict[str, Any]) -> None: ...

@runtime_checkable
class ISerializer(Protocol):
    """Interface pour sérialiseurs"""
    def serialize(self, data: Any) -> bytes: ...
    def deserialize(self, data: bytes) -> Any: ...

@runtime_checkable
class IValidator(Protocol):
    """Interface pour validateurs"""
    def validate(self, data: Any) -> bool: ...
    def get_errors(self) -> List[str]: ...

class PersistenceStrategy(ABC):
    """Stratégie abstraite de persistence"""

    @abstractmethod
    def save(self, data: Any, filepath: str) -> bool: ...

    @abstractmethod
    def load(self, filepath: str) -> Any: ...

    @abstractmethod
    def get_supported_extensions(self) -> List[str]: ...

# ═══════════════════════════════════════════════════════════════════
# 3. CONFIGURATION ET ÉNUMÉRATIONS
# ═══════════════════════════════════════════════════════════════════

class PersistenceFormat(Enum):
    """Formats de persistence supportés"""
    JOBLIB = "joblib"
    PICKLE = "pkl"
    JSON = "json"
    TXT = "txt"
    CSV = "csv"
    NUMPY = "npy"

class CompressionLevel(Enum):
    """Niveaux de compression"""
    NONE = 0
    LOW = 1
    MEDIUM = 3
    HIGH = 6
    MAXIMUM = 9

# Configuration centralisée utilisée depuis parameters.py
# Alias pour compatibilité
PersistenceConfig = type(global_config.persistence)


# ═══════════════════════════════════════════════════════════════════
# 4. GESTIONNAIRES CORE
# ═══════════════════════════════════════════════════════════════════

class FileManager:
    """
    Gestionnaire de fichiers sécurisé avec validation et backup
    Basé sur safe_file_write/safe_file_read de l'ancien système
    """

    def __init__(self, config: PersistenceConfig = None):
        # Utilisation configuration centralisée
        self.config = global_config.persistence
        self.lock = threading.RLock() if self.config.thread_safe else None
        self._ensure_directories()
        self._strategies = {}
        self._register_default_strategies()

    def _ensure_directories(self):
        """Crée les répertoires nécessaires"""
        directories = [
            self.config.models_dir, self.config.data_dir, self.config.config_dir,
            self.config.backup_dir, self.config.cache_dir, self.config.logs_dir
        ]
        for directory in directories:
            os.makedirs(directory, exist_ok=True)

    def _register_default_strategies(self):
        """Enregistre les stratégies de persistence par défaut"""
        self._strategies = {
            'joblib': JoblibStrategy(),
            'pkl': PickleStrategy(),
            'pickle': PickleStrategy(),
            'json': JsonStrategy()
        }

    def safe_write(self, data: Any, filepath: str, format_type: str = "auto") -> bool:
        """Écriture sécurisée avec backup automatique"""
        try:
            if self.lock:
                with self.lock:
                    return self._perform_write(data, filepath, format_type)
            else:
                return self._perform_write(data, filepath, format_type)
        except Exception as e:
            logger.error(f"Erreur écriture {filepath}: {e}")
            return False

    def safe_read(self, filepath: str, format_type: str = "auto") -> Any:
        """Lecture sécurisée avec validation"""
        try:
            if self.lock:
                with self.lock:
                    return self._perform_read(filepath, format_type)
            else:
                return self._perform_read(filepath, format_type)
        except Exception as e:
            logger.error(f"Erreur lecture {filepath}: {e}")
            return None

    def _perform_write(self, data: Any, filepath: str, format_type: str) -> bool:
        """Implémentation écriture"""
        try:
            # Backup si fichier existe
            if os.path.exists(filepath) and self.config.use_backups:
                backup_manager = BackupManager(self.config.backup_dir, self.config.max_backups)
                backup_manager.create_backup(filepath)

            # Détection format
            if format_type == "auto":
                format_type = detect_format(filepath).value

            # Utilisation stratégie appropriée
            strategy = self._strategies.get(format_type)
            if strategy:
                return strategy.save(data, filepath)
            else:
                # Fallback vers écriture directe
                return self._direct_write(data, filepath, format_type)

        except Exception as e:
            logger.error(f"Erreur _perform_write: {e}")
            return False

    def _perform_read(self, filepath: str, format_type: str) -> Any:
        """Implémentation lecture"""
        try:
            if not os.path.exists(filepath):
                logger.warning(f"Fichier inexistant: {filepath}")
                return None

            # Validation checksum si activée
            if self.config.use_checksums:
                checksum_file = filepath + ".md5"
                if os.path.exists(checksum_file):
                    with open(checksum_file, 'r') as f:
                        expected_checksum = f.read().strip()
                    if not ChecksumValidator.validate_checksum(filepath, expected_checksum):
                        logger.error(f"Checksum invalide: {filepath}")
                        return None

            # Détection format
            if format_type == "auto":
                format_type = detect_format(filepath).value

            # Utilisation stratégie appropriée
            strategy = self._strategies.get(format_type)
            if strategy:
                return strategy.load(filepath)
            else:
                # Fallback vers lecture directe
                return self._direct_read(filepath, format_type)

        except Exception as e:
            logger.error(f"Erreur _perform_read: {e}")
            return None

    def _direct_write(self, data: Any, filepath: str, format_type: str) -> bool:
        """Écriture directe pour formats non supportés par stratégies"""
        try:
            if format_type == "txt":
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(str(data))
            elif format_type == "json":
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            else:
                # Fallback pickle
                with open(filepath, 'wb') as f:
                    pickle.dump(data, f)

            # Génération checksum si activée
            if self.config.use_checksums:
                checksum = ChecksumValidator.generate_checksum(filepath)
                with open(filepath + ".md5", 'w') as f:
                    f.write(checksum)

            return True
        except Exception as e:
            logger.error(f"Erreur _direct_write: {e}")
            return False

    def _direct_read(self, filepath: str, format_type: str) -> Any:
        """Lecture directe pour formats non supportés par stratégies"""
        try:
            if format_type == "txt":
                with open(filepath, 'r', encoding='utf-8') as f:
                    return f.read()
            elif format_type == "json":
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # Fallback pickle
                with open(filepath, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.error(f"Erreur _direct_read: {e}")
            return None

class StateManager:
    """
    Gestionnaire d'état système unifié
    Basé sur _perform_save/_perform_load/_load_latest_state de l'ancien système
    """

    def __init__(self, file_manager: FileManager):
        self.file_manager = file_manager
        self.lock = threading.RLock() if file_manager.config.thread_safe else None
        self._state_cache = {}

    def save_system_state(self, state_data: Dict[str, Any], filepath: str = None) -> bool:
        """Sauvegarde état système complet avec métadonnées"""
        try:
            if self.lock:
                with self.lock:
                    return self._perform_save(state_data, filepath)
            else:
                return self._perform_save(state_data, filepath)
        except Exception as e:
            logger.error(f"Erreur sauvegarde état système: {e}")
            return False

    def load_system_state(self, filepath: str = None) -> Optional[Dict[str, Any]]:
        """Chargement état système avec validation"""
        try:
            if self.lock:
                with self.lock:
                    return self._perform_load(filepath)
            else:
                return self._perform_load(filepath)
        except Exception as e:
            logger.error(f"Erreur chargement état système: {e}")
            return None

    def find_latest_state_file(self) -> Optional[str]:
        """Trouve le fichier d'état le plus récent"""
        try:
            models_dir = self.file_manager.config.models_dir
            if not os.path.exists(models_dir):
                return None

            state_files = []
            for filename in os.listdir(models_dir):
                if filename.startswith("system_state_") and filename.endswith(".joblib"):
                    filepath = os.path.join(models_dir, filename)
                    mtime = os.path.getmtime(filepath)
                    state_files.append((mtime, filepath))

            if state_files:
                state_files.sort(reverse=True)
                return state_files[0][1]

            return None
        except Exception as e:
            logger.error(f"Erreur recherche fichier état: {e}")
            return None

    def _perform_save(self, state_data: Dict[str, Any], filepath: str = None) -> bool:
        """Implémentation sauvegarde basée sur _perform_save de l'ancien système"""
        try:
            if filepath is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filepath = os.path.join(
                    self.file_manager.config.models_dir,
                    f"system_state_{timestamp}.joblib"
                )

            # Package complet avec métadonnées
            package = {
                'state_data': state_data,
                'save_timestamp': datetime.now().isoformat(),
                'version': '1.0',
                'format_version': '2024.1'
            }

            # Sauvegarde via FileManager
            success = self.file_manager.safe_write(package, filepath, "joblib")

            if success and self.file_manager.config.save_metadata:
                self._save_metadata(filepath, package)

            # Cache en mémoire
            if success:
                cache_key = f"state_{os.path.basename(filepath)}"
                self._state_cache[cache_key] = package

            return success

        except Exception as e:
            logger.error(f"Erreur _perform_save: {e}")
            return False

    def _perform_load(self, filepath: str = None) -> Optional[Dict[str, Any]]:
        """Implémentation chargement basée sur _perform_load de l'ancien système"""
        try:
            if filepath is None:
                filepath = self.find_latest_state_file()
                if not filepath:
                    logger.warning("Aucun fichier d'état trouvé")
                    return None

            # Vérification cache
            cache_key = f"state_{os.path.basename(filepath)}"
            if cache_key in self._state_cache:
                logger.debug(f"État chargé depuis cache: {filepath}")
                return self._state_cache[cache_key].get('state_data')

            # Chargement via FileManager
            package = self.file_manager.safe_read(filepath, "joblib")
            if not package:
                return None

            # Validation package
            if not self._validate_state_package(package):
                logger.error(f"Package d'état invalide: {filepath}")
                return None

            # Cache en mémoire
            self._state_cache[cache_key] = package

            logger.info(f"État système chargé: {filepath}")
            return package.get('state_data')

        except Exception as e:
            logger.error(f"Erreur _perform_load: {e}")
            return None

    def _validate_state_package(self, package: Dict[str, Any]) -> bool:
        """Valide la structure du package d'état"""
        required_keys = ['state_data', 'save_timestamp', 'version']
        return all(key in package for key in required_keys)

    def _save_metadata(self, model_filepath: str, package: Dict[str, Any]):
        """Sauvegarde métadonnées JSON associées"""
        try:
            metadata = {
                'timestamp': package.get('save_timestamp'),
                'model_file': os.path.basename(model_filepath),
                'version': package.get('version'),
                'format_version': package.get('format_version'),
                'file_size': os.path.getsize(model_filepath),
                'checksum_available': self.file_manager.config.use_checksums
            }

            json_filepath = os.path.splitext(model_filepath)[0] + global_config.persistence.supported_extensions[0]
            self.file_manager.safe_write(metadata, json_filepath, "json")
        except Exception as e:
            logger.error(f"Erreur sauvegarde métadonnées: {e}")

class HistoricalDataManager:
    """
    Gestionnaire de données historiques
    Basé sur load_historical_data/_load_historical_txt de l'ancien système
    """

    def __init__(self, file_manager: FileManager):
        self.file_manager = file_manager
        self.lock = threading.RLock() if file_manager.config.thread_safe else None

    def load_historical_data(self, filepath: str) -> Optional[List[List[str]]]:
        """Charge données historiques depuis fichier .txt"""
        # Implémentation basée sur _load_historical_txt
        pass

    def save_historical_data(self, data: List[List[str]], filepath: str) -> bool:
        """Sauvegarde données historiques"""
        # Implémentation basée sur _append_session_to_historical_txt
        pass

    def validate_historical_format(self, data: List[List[str]]) -> bool:
        """Valide format des données historiques"""
        # Implémentation validation séquences
        pass

class ModelManager:
    """
    Gestionnaire de modèles ML avec versioning
    Basé sur save_model/load_model de l'ancien système
    """

    def __init__(self, file_manager: FileManager):
        self.file_manager = file_manager
        self.lock = threading.RLock() if file_manager.config.thread_safe else None
        self._model_registry = {}

    def save_model(self, model: Any, model_name: str, metadata: Dict[str, Any] = None) -> str:
        """Sauvegarde modèle avec métadonnées et versioning"""
        # Implémentation basée sur save_model avec versioning
        pass

    def load_model(self, model_path: str, validate: bool = True) -> Any:
        """Charge modèle avec validation"""
        # Implémentation basée sur load_model avec validation
        pass

    def list_model_versions(self, model_name: str) -> List[Dict[str, Any]]:
        """Liste les versions disponibles d'un modèle"""
        # Implémentation versioning
        pass

class ConfigurationManager:
    """
    Gestionnaire de configuration et paramètres
    Basé sur load_params_from_file/save_params_to_file de l'ancien système
    """

    def __init__(self, file_manager: FileManager):
        self.file_manager = file_manager
        self.lock = threading.RLock() if file_manager.config.thread_safe else None

    def load_params(self, filepath: str) -> Optional[Dict[str, Any]]:
        """Charge paramètres depuis fichier"""
        # Implémentation basée sur load_params_from_file
        pass

    def save_params(self, params: Dict[str, Any], filepath: str) -> bool:
        """Sauvegarde paramètres vers fichier"""
        # Implémentation basée sur save_params_to_file
        pass

    def apply_params_to_config(self, config: Any, params: Dict[str, Any]) -> bool:
        """Applique paramètres à configuration"""
        # Implémentation basée sur apply_params_to_config
        pass


# ═══════════════════════════════════════════════════════════════════
# 5. STRATÉGIES DE PERSISTENCE
# ═══════════════════════════════════════════════════════════════════

class JoblibStrategy(PersistenceStrategy):
    """Stratégie de persistence Joblib avec compression"""

    def save(self, data: Any, filepath: str) -> bool:
        try:
            # Utilisation configuration centralisée pour compression
            config = global_config.persistence
            if config.use_compression:
                compression_level = config.compression_level.value
                joblib.dump(data, filepath, compress=compression_level)
            else:
                joblib.dump(data, filepath)
            return True
        except Exception as e:
            logger.error(f"Erreur sauvegarde Joblib {filepath}: {e}")
            return False

    def load(self, filepath: str) -> Any:
        try:
            return joblib.load(filepath)
        except Exception as e:
            logger.error(f"Erreur chargement Joblib {filepath}: {e}")
            return None

    def get_supported_extensions(self) -> List[str]:
        return [global_config.persistence.supported_extensions[2]]

class PickleStrategy(PersistenceStrategy):
    """Stratégie de persistence Pickle"""

    def save(self, data: Any, filepath: str) -> bool:
        try:
            config = global_config.persistence
            if config.use_compression:
                with gzip.open(filepath + '.gz', 'wb') as f:
                    pickle.dump(data, f)
                # Renommer pour masquer compression
                shutil.move(filepath + '.gz', filepath)
            else:
                with open(filepath, 'wb') as f:
                    pickle.dump(data, f)
            return True
        except Exception as e:
            logger.error(f"Erreur sauvegarde Pickle {filepath}: {e}")
            return False

    def load(self, filepath: str) -> Any:
        try:
            # Tentative chargement compressé d'abord
            try:
                with gzip.open(filepath, 'rb') as f:
                    return pickle.load(f)
            except (gzip.BadGzipFile, OSError):
                # Fallback vers pickle standard
                with open(filepath, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.error(f"Erreur chargement Pickle {filepath}: {e}")
            return None

    def get_supported_extensions(self) -> List[str]:
        return [global_config.persistence.supported_extensions[1], '.pickle']

class JsonStrategy(PersistenceStrategy):
    """Stratégie de persistence JSON"""

    def save(self, data: Any, filepath: str) -> bool:
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=self._json_serializer)
            return True
        except Exception as e:
            logger.error(f"Erreur sauvegarde JSON {filepath}: {e}")
            return False

    def load(self, filepath: str) -> Any:
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Erreur chargement JSON {filepath}: {e}")
            return None

    def get_supported_extensions(self) -> List[str]:
        return [global_config.persistence.supported_extensions[0]]

    def _json_serializer(self, obj):
        """Sérialiseur personnalisé pour types non-JSON"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)

# ═══════════════════════════════════════════════════════════════════
# 6. CACHE ET OPTIMISATION
# ═══════════════════════════════════════════════════════════════════

class PersistenceCache:
    """
    Cache multi-niveaux pour optimisation persistence
    Basé sur cache_manager de l'ancien système
    """

    def __init__(self, config: PersistenceConfig):
        self.config = config
        self._memory_cache = {}
        self._disk_cache_dir = os.path.join(config.cache_dir, "persistence")
        self._cache_metadata = {}
        self.lock = threading.RLock()
        os.makedirs(self._disk_cache_dir, exist_ok=True)

    def get(self, key: str) -> Optional[Any]:
        """Récupère élément du cache"""
        # Implémentation cache multi-niveaux
        pass

    def put(self, key: str, value: Any, ttl: int = None) -> bool:
        """Stocke élément dans cache"""
        # Implémentation cache avec TTL
        pass

    def cleanup(self) -> int:
        """Nettoie cache expiré"""
        # Implémentation nettoyage automatique
        pass

# ═══════════════════════════════════════════════════════════════════
# 7. UTILITAIRES ET HELPERS
# ═══════════════════════════════════════════════════════════════════

class ChecksumValidator:
    """Validateur de checksums pour intégrité fichiers"""

    @staticmethod
    def generate_checksum(filepath: str) -> str:
        """Génère checksum MD5 d'un fichier"""
        try:
            hash_md5 = hashlib.md5()
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Erreur génération checksum {filepath}: {e}")
            return ""

    @staticmethod
    def validate_checksum(filepath: str, expected_checksum: str) -> bool:
        """Valide checksum d'un fichier"""
        try:
            actual_checksum = ChecksumValidator.generate_checksum(filepath)
            return actual_checksum == expected_checksum
        except Exception as e:
            logger.error(f"Erreur validation checksum {filepath}: {e}")
            return False

class BackupManager:
    """Gestionnaire de sauvegardes automatiques"""

    def __init__(self, backup_dir: str, max_backups: int = None):
        self.backup_dir = backup_dir
        # Utilisation configuration centralisée si max_backups non spécifié
        self.max_backups = max_backups or global_config.persistence.max_backups
        os.makedirs(backup_dir, exist_ok=True)

    def create_backup(self, filepath: str) -> str:
        """Crée sauvegarde d'un fichier"""
        try:
            if not os.path.exists(filepath):
                logger.warning(f"Fichier à sauvegarder inexistant: {filepath}")
                return ""

            # Génération nom backup avec timestamp
            filename = os.path.basename(filepath)
            name, ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{name}_backup_{timestamp}{ext}"
            backup_filepath = os.path.join(self.backup_dir, backup_filename)

            # Copie fichier
            shutil.copy2(filepath, backup_filepath)
            logger.debug(f"Backup créé: {backup_filepath}")

            # Nettoyage anciennes sauvegardes
            self.cleanup_old_backups(name)

            return backup_filepath

        except Exception as e:
            logger.error(f"Erreur création backup {filepath}: {e}")
            return ""

    def cleanup_old_backups(self, file_pattern: str) -> int:
        """Nettoie anciennes sauvegardes"""
        try:
            # Recherche fichiers backup correspondant au pattern
            backup_files = []
            for filename in os.listdir(self.backup_dir):
                if filename.startswith(f"{file_pattern}_backup_"):
                    filepath = os.path.join(self.backup_dir, filename)
                    mtime = os.path.getmtime(filepath)
                    backup_files.append((mtime, filepath))

            # Tri par date (plus récent en premier)
            backup_files.sort(reverse=True)

            # Suppression fichiers excédentaires
            deleted_count = 0
            for i, (mtime, filepath) in enumerate(backup_files):
                if i >= self.max_backups:
                    try:
                        os.remove(filepath)
                        deleted_count += 1
                        logger.debug(f"Backup supprimé: {filepath}")
                    except Exception as e:
                        logger.error(f"Erreur suppression backup {filepath}: {e}")

            return deleted_count

        except Exception as e:
            logger.error(f"Erreur nettoyage backups {file_pattern}: {e}")
            return 0

def detect_format(filepath: str) -> PersistenceFormat:
    """Détecte format basé sur extension fichier"""
    ext = Path(filepath).suffix.lower()
    format_map = {
        global_config.persistence.supported_extensions[2]: PersistenceFormat.JOBLIB,
        global_config.persistence.supported_extensions[1]: PersistenceFormat.PICKLE,
        '.pickle': PersistenceFormat.PICKLE,
        global_config.persistence.supported_extensions[0]: PersistenceFormat.JSON,
        '.txt': PersistenceFormat.TXT,
        '.csv': PersistenceFormat.CSV,
        '.npy': PersistenceFormat.NUMPY
    }
    return format_map.get(ext, PersistenceFormat.TXT)

def generate_timestamp_filename(base_name: str, extension: str) -> str:
    """Génère nom de fichier avec timestamp"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{base_name}_{timestamp}.{extension.lstrip('.')}"

# ═══════════════════════════════════════════════════════════════════
# 8. FACTORY ET ORCHESTRATEUR
# ═══════════════════════════════════════════════════════════════════

class PersistenceFactory:
    """Factory pour création gestionnaires de persistence"""

    @staticmethod
    def create_file_manager(config: PersistenceConfig = None) -> FileManager:
        """Crée gestionnaire de fichiers configuré"""
        return FileManager(config or PersistenceConfig())

    @staticmethod
    def create_state_manager(file_manager: FileManager = None) -> StateManager:
        """Crée gestionnaire d'état configuré"""
        if file_manager is None:
            file_manager = PersistenceFactory.create_file_manager()
        return StateManager(file_manager)

    @staticmethod
    def create_complete_persistence_system(config: PersistenceConfig = None) -> 'PersistenceOrchestrator':
        """Crée système de persistence complet"""
        return PersistenceOrchestrator(config)

class PersistenceOrchestrator:
    """
    Orchestrateur principal unifiant tous les gestionnaires
    Point d'entrée unique pour toutes les opérations de persistence
    """

    def __init__(self, config: PersistenceConfig = None):
        # Utilisation configuration centralisée
        self.config = global_config.persistence

        # Initialisation gestionnaires
        self.file_manager = FileManager(self.config)
        self.state_manager = StateManager(self.file_manager)
        self.historical_manager = HistoricalDataManager(self.file_manager)
        self.model_manager = ModelManager(self.file_manager)
        self.config_manager = ConfigurationManager(self.file_manager)
        self.cache = PersistenceCache(self.config) if self.config.cache_enabled else None

        logger.info("Système de persistence unifié initialisé")

    def save_complete_system_state(self, state_data: Dict[str, Any]) -> bool:
        """Sauvegarde état complet du système"""
        # Orchestration sauvegarde complète
        pass

    def load_complete_system_state(self) -> Optional[Dict[str, Any]]:
        """Charge état complet du système"""
        # Orchestration chargement complet
        pass

# ═══════════════════════════════════════════════════════════════════
# 9. INSTANCES GLOBALES ET API PUBLIQUE
# ═══════════════════════════════════════════════════════════════════

# Configuration centralisée
default_persistence_config = global_config.persistence

# Orchestrateur global
default_persistence_system = PersistenceOrchestrator(default_persistence_config)

# Gestionnaires individuels pour accès direct
default_file_manager = default_persistence_system.file_manager
default_state_manager = default_persistence_system.state_manager
default_historical_manager = default_persistence_system.historical_manager
default_model_manager = default_persistence_system.model_manager
default_config_manager = default_persistence_system.config_manager

# API publique simplifiée
def save_system_state(state_data: Dict[str, Any], filepath: str = None) -> bool:
    """API publique: Sauvegarde état système"""
    return default_state_manager.save_system_state(state_data, filepath)

def load_system_state(filepath: str = None) -> Optional[Dict[str, Any]]:
    """API publique: Chargement état système"""
    return default_state_manager.load_system_state(filepath)

def save_file(data: Any, filepath: str, format_type: str = "auto") -> bool:
    """API publique: Sauvegarde fichier"""
    return default_file_manager.safe_write(data, filepath, format_type)

def load_file(filepath: str, format_type: str = "auto") -> Any:
    """API publique: Chargement fichier"""
    return default_file_manager.safe_read(filepath, format_type)

def save_model(model: Any, model_name: str, metadata: Dict[str, Any] = None) -> str:
    """API publique: Sauvegarde modèle"""
    return default_model_manager.save_model(model, model_name, metadata)

def load_model(model_path: str) -> Any:
    """API publique: Chargement modèle"""
    return default_model_manager.load_model(model_path)

def load_historical_data(filepath: str) -> Optional[List[List[str]]]:
    """API publique: Chargement données historiques"""
    return default_historical_manager.load_historical_data(filepath)

def save_historical_data(data: List[List[str]], filepath: str) -> bool:
    """API publique: Sauvegarde données historiques"""
    return default_historical_manager.save_historical_data(data, filepath)

def load_params(filepath: str) -> Optional[Dict[str, Any]]:
    """API publique: Chargement paramètres"""
    return default_config_manager.load_params(filepath)

def save_params(params: Dict[str, Any], filepath: str) -> bool:
    """API publique: Sauvegarde paramètres"""
    return default_config_manager.save_params(params, filepath)
