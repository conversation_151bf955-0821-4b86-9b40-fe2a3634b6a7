# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 785 à 792
# Type: Méthode de la classe EnsembleModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def fit(self, X: np.n<PERSON>ray, y: np.n<PERSON><PERSON>):
        """Entraînement ensemble"""
        logger.info("Ensemble: Entraînement des modèles base")
        self.lstm_model.fit(X, y)
        self.lgbm_model.fit(X, y)
        self.markov_model.fit(X, y)
        logger.info("Ensemble: Tous les modèles entraînés")
        return self