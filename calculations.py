"""
CALCULS POUR LE NOUVEAU PRÉDICTEUR BACCARAT
===========================================

Formules de confiance, métriques et calculs optimisés.
Basé sur l'analyse de 220+ fichiers de l'ancien système.

STRUCTURE DU MODULE:
===================
1. IMPORTS ET DÉPENDANCES
2. CALCULATEUR DE CONFIANCE
3. CALCULATEUR DE MÉTRIQUES
4. CALCULATEUR DE PRÉDICTIONS
5. CALCULATEUR D'INCERTITUDE
6. CALCULATEUR D'ENSEMBLE
7. UTILITAIRES MATHÉMATIQUES
8. FONCTIONS DE VALIDATION
9. INSTANCES GLOBALES
"""

# ═══════════════════════════════════════════════════════════════════
# 1. IMPORTS ET DÉPENDANCES
# ═══════════════════════════════════════════════════════════════════

import logging
import numpy as np
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
import math

# Fallback pour sklearn si non disponible
try:
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    # Implémentations simples de fallback
    def accuracy_score(y_true, y_pred):
        return sum(1 for t, p in zip(y_true, y_pred) if t == p) / len(y_true) if y_true else 0.5

    def precision_score(y_true, y_pred, average='binary', zero_division=0):
        if not y_true or not y_pred:
            return 0.5
        tp = sum(1 for t, p in zip(y_true, y_pred) if t == 1 and p == 1)
        fp = sum(1 for t, p in zip(y_true, y_pred) if t == 0 and p == 1)
        return tp / (tp + fp) if (tp + fp) > 0 else zero_division

    def recall_score(y_true, y_pred, average='binary', zero_division=0):
        if not y_true or not y_pred:
            return 0.5
        tp = sum(1 for t, p in zip(y_true, y_pred) if t == 1 and p == 1)
        fn = sum(1 for t, p in zip(y_true, y_pred) if t == 1 and p == 0)
        return tp / (tp + fn) if (tp + fn) > 0 else zero_division

    def f1_score(y_true, y_pred, average='binary', zero_division=0):
        prec = precision_score(y_true, y_pred, average, zero_division)
        rec = recall_score(y_true, y_pred, average, zero_division)
        return 2 * (prec * rec) / (prec + rec) if (prec + rec) > 0 else zero_division

# Import configuration centralisée
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "parameters"))
from parameters import global_config

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════
# 2. CALCULATEUR DE CONFIANCE
# ═══════════════════════════════════════════════════════════════════

class ConfidenceCalculator:
    """
    Calculateur de confiance unifié
    Basé sur l'analyse des formules de l'ancien système
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        # Utilisation configuration centralisée
        calc_config = global_config.calculations
        self.confidence_threshold = calc_config.confidence_threshold
        self.uncertainty_threshold = calc_config.uncertainty_threshold
        self.prediction_threshold = calc_config.prediction_threshold
        self.confidence_base_factor = calc_config.confidence_base_factor
        self.confidence_diversity_factor = calc_config.confidence_diversity_factor
        self.max_prob_center = calc_config.max_prob_center
        self.confidence_multiplier = calc_config.confidence_multiplier

        # Nouveaux paramètres exploitation non-indépendance selon recherches
        self.finite_state_exploitation = global_config.azr.finite_state_exploitation
        self.sequence_dependency_modeling = global_config.azr.sequence_dependency_modeling
        self.conditional_probability_chains = global_config.azr.conditional_probability_chains

        # Cache pour probabilités conditionnelles
        self.conditional_prob_cache = {}
        self.sequence_state_cache = {}

        logger.info("ConfidenceCalculator initialisé avec exploitation non-indépendance Baccarat")

    def calculate_prediction_confidence(self, probabilities: Dict[str, float],
                                      model_weights: Dict[str, float] = None) -> float:
        """
        Calcule confiance prédiction (adaptée de l'ancien système)
        """
        try:
            if not probabilities:
                return global_config.calculations.default_confidence

            # Probabilités principales
            player_prob = probabilities.get('player', self.max_prob_center)
            banker_prob = probabilities.get('banker', self.max_prob_center)

            # ✅ CONFIANCE STABILISÉE (sans amplification excessive)
            max_prob = max(player_prob, banker_prob)
            # Utilise fonction sigmoïde pour éviter amplification linéaire du bruit
            raw_confidence = abs(max_prob - self.max_prob_center) * 2.0  # Facteur réduit de 2.0 au lieu de multiplier

            # Fonction sigmoïde pour stabiliser : f(x) = 1 / (1 + e^(-k*(x-0.5)))
            k = 6.0  # Paramètre de pente
            confidence = 1.0 / (1.0 + math.exp(-k * (raw_confidence - 0.5)))

            # Ajustement selon poids modèles si disponibles (limité pour éviter bruit)
            if model_weights:
                weight_factor = self._calculate_weight_factor(model_weights)
                confidence = confidence * 0.8 + weight_factor * 0.2  # Mélange conservateur

            # NOUVEAU: Ajustement confiance selon exploitation non-indépendance
            if self.finite_state_exploitation:
                non_independence_boost = self._calculate_non_independence_confidence_boost(probabilities)
                confidence = confidence * (1.0 + non_independence_boost)

            return np.clip(confidence, global_config.calculations.confidence_min, global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur calcul confiance: {e}")
            return global_config.calculations.default_confidence

    def calculate_ensemble_confidence(self, predictions: Dict[str, Dict[str, float]],
                                    weights: Dict[str, float]) -> float:
        """
        FORMULE UNIFIÉE DE CONFIANCE ENSEMBLE - Basée sur recherches internationales

        Formule : Confiance = (Performance_globale × Consensus) / (1 + Variance_pondérée)
        """
        try:
            if not predictions or not weights:
                return global_config.calculations.default_confidence

            # 1. Calcul du consensus (accord entre modèles)
            consensus = self._calculate_model_consensus(predictions)

            # 2. Performance globale (moyenne des poids normalisés)
            total_weight = sum(weights.values())
            if total_weight > 0:
                global_performance = total_weight / len(weights)  # Performance moyenne
            else:
                global_performance = global_config.calculations.default_confidence

            # 3. Variance pondérée des prédictions
            weighted_variance = self._calculate_weighted_variance(predictions, weights)

            # 4. FORMULE UNIFIÉE FINALE
            confidence = (global_performance * consensus) / (1.0 + weighted_variance)

            return np.clip(confidence,
                          global_config.calculations.confidence_min,
                          global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur confiance ensemble unifiée: {e}")
            return global_config.calculations.default_confidence

    def _calculate_model_consensus(self, predictions: Dict[str, Dict[str, float]]) -> float:
        """Calcule le consensus entre modèles (1 = accord parfait, 0 = désaccord total)"""
        try:
            if len(predictions) < 2:
                return 1.0  # Consensus parfait avec un seul modèle

            # Extraire probabilités Banker de chaque modèle
            banker_probs = []
            for pred in predictions.values():
                banker_probs.append(pred.get('banker', 0.5))

            # Consensus = 1 - variance normalisée
            variance = np.var(banker_probs)
            max_variance = 0.25  # Variance maximale pour probabilités entre 0 et 1
            consensus = 1.0 - min(variance / max_variance, 1.0)

            return consensus

        except Exception as e:
            logger.error(f"Erreur calcul consensus: {e}")
            return 0.5

    def calculate_lstm_confidence(self, probas: np.ndarray) -> np.ndarray:
        """Calcule confiance LSTM basée sur distance à 0.5"""
        try:
            banker_probs = probas[:, 1] if probas.ndim > 1 else probas

            # Confiance basée sur distance à 0.5
            base_confidence = np.abs(banker_probs - 0.5) * 2.0

            # Appliquer facteurs de configuration
            confidence = base_confidence * global_config.calculations.confidence_multiplier

            return np.clip(confidence,
                          global_config.calculations.confidence_min,
                          global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur confiance LSTM: {e}")
            return np.full(len(probas), global_config.calculations.default_confidence)

    def calculate_lgbm_confidence(self, probas: np.ndarray, feature_importance: Dict[str, float] = None) -> np.ndarray:
        """Calcule confiance LGBM basée sur distance à 0.5 et importance features"""
        try:
            banker_probs = probas[:, 1] if probas.ndim > 1 else probas

            # Confiance de base (distance à 0.5)
            base_confidence = np.abs(banker_probs - 0.5) * 2.0

            # Facteur d'importance des features si disponible
            importance_factor = 1.0
            if feature_importance:
                # Moyenne des importances comme facteur de confiance
                importance_factor = min(2.0, np.mean(list(feature_importance.values())) / 100.0 + 0.5)

            confidence = base_confidence * importance_factor

            return np.clip(confidence,
                          global_config.calculations.confidence_min,
                          global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur confiance LGBM: {e}")
            return np.full(len(probas), global_config.calculations.default_confidence)

    def calculate_markov_confidence(self, model, X: np.ndarray) -> np.ndarray:
        """Calcule confiance Markov basée sur fréquence des transitions"""
        try:
            if not hasattr(model, 'is_trained') or not model.is_trained:
                return np.full(len(X), global_config.calculations.default_confidence)

            confidences = []

            for sequence in X:
                sequence_flat = sequence.flatten() if sequence.ndim > 1 else sequence
                confidence = self._calculate_markov_sequence_confidence(model, sequence_flat)
                confidences.append(confidence)

            return np.array(confidences)

        except Exception as e:
            logger.error(f"Erreur confiance Markov: {e}")
            return np.full(len(X), global_config.calculations.default_confidence)

    def _calculate_markov_sequence_confidence(self, model, sequence: np.ndarray) -> float:
        """Calcule confiance pour une séquence Markov unique"""
        try:
            with model.lock:
                max_confidence = 0.0

                # Essayer différents ordres pour trouver le plus confiant
                for order in range(min(model.max_order, len(sequence)), 0, -1):
                    if len(sequence) >= order:
                        state = tuple(sequence[-order:])

                        if state in model.models[order]:
                            total_transitions = sum(model.models[order][state].values())

                            # Confiance basée sur nombre d'observations
                            confidence = min(1.0, total_transitions / 10.0)  # 10 transitions = confiance max

                            # Bonus pour ordres plus élevés (plus spécifiques)
                            order_bonus = 1.0 + (order - 1) * 0.1
                            confidence *= order_bonus

                            max_confidence = max(max_confidence, confidence)

                return np.clip(max_confidence,
                              global_config.calculations.confidence_min,
                              global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur confiance séquence Markov: {e}")
            return global_config.calculations.default_confidence

    def _calculate_weight_factor(self, weights: Dict[str, float]) -> float:
        """Calcule facteur de pondération"""
        try:
            # Entropie des poids (diversité)
            entropy = -sum(w * math.log(w + global_config.calculations.log_epsilon) for w in weights.values() if w > 0)
            max_entropy = math.log(len(weights))

            # Facteur basé sur diversité
            diversity_factor = entropy / max_entropy if max_entropy > 0 else global_config.calculations.default_diversity_factor

            return self.confidence_base_factor + self.confidence_diversity_factor * diversity_factor

        except Exception as e:
            logger.error(f"Erreur facteur poids: {e}")
            return global_config.calculations.default_weight_factor

    def _calculate_weighted_variance(self, predictions: Dict[str, Dict[str, float]],
                                   weights: Dict[str, float]) -> float:
        """Calcule variance pondérée des prédictions"""
        try:
            # Prédiction moyenne pondérée
            avg_player = sum(pred.get('player', self.max_prob_center) * weights.get(model, 0)
                           for model, pred in predictions.items())
            avg_banker = sum(pred.get('banker', self.max_prob_center) * weights.get(model, 0)
                           for model, pred in predictions.items())

            # Variance pondérée
            var_player = sum(weights.get(model, 0) * (pred.get('player', self.max_prob_center) - avg_player)**2
                           for model, pred in predictions.items())
            var_banker = sum(weights.get(model, 0) * (pred.get('banker', self.max_prob_center) - avg_banker)**2
                           for model, pred in predictions.items())

            return (var_player + var_banker) / global_config.calculations.variance_divisor

        except Exception as e:
            logger.error(f"Erreur variance pondérée: {e}")
            return global_config.calculations.default_confidence

    # ═══════════════════════════════════════════════════════════════════
    # MÉTHODES EXPLOITATION NON-INDÉPENDANCE BACCARAT
    # ═══════════════════════════════════════════════════════════════════

    def _calculate_non_independence_confidence_boost(self, probabilities: Dict[str, float]) -> float:
        """
        Calcule boost de confiance basé sur exploitation non-indépendance des coups

        Selon recherches: les coups de Baccarat ne sont PAS indépendants sur 60 manches.
        L'espace d'états fini permet d'exploiter des dépendances pour améliorer confiance.
        """
        try:
            if not self.sequence_dependency_modeling:
                return 0.0

            player_prob = probabilities.get('player', 0.5)
            banker_prob = probabilities.get('banker', 0.5)

            # Calcul état de séquence pour cache
            sequence_state = self._calculate_sequence_state(probabilities)

            # Vérification cache probabilités conditionnelles
            if sequence_state in self.conditional_prob_cache:
                cached_boost = self.conditional_prob_cache[sequence_state]
                return cached_boost

            # Calcul boost basé sur dépendances d'états finis
            finite_state_boost = self._calculate_finite_state_boost(player_prob, banker_prob)

            # Calcul boost chaînes probabilités conditionnelles
            conditional_boost = 0.0
            if self.conditional_probability_chains:
                conditional_boost = self._calculate_conditional_probability_boost(probabilities)

            # Combinaison des boosts
            total_boost = (finite_state_boost * 0.7 + conditional_boost * 0.3)

            # Limitation boost pour éviter sur-confiance
            total_boost = min(0.15, max(0.0, total_boost))  # Max 15% boost

            # Cache du résultat
            self.conditional_prob_cache[sequence_state] = total_boost

            return total_boost

        except Exception as e:
            logger.error(f"Erreur calcul boost non-indépendance: {e}")
            return 0.0

    def _calculate_sequence_state(self, probabilities: Dict[str, float]) -> str:
        """Calcule état de séquence pour cache"""
        try:
            player_prob = probabilities.get('player', 0.5)
            banker_prob = probabilities.get('banker', 0.5)

            # Discrétisation pour cache (précision 0.01)
            player_discrete = round(player_prob, 2)
            banker_discrete = round(banker_prob, 2)

            return f"P{player_discrete}_B{banker_discrete}"

        except Exception:
            return "default_state"

    def _calculate_finite_state_boost(self, player_prob: float, banker_prob: float) -> float:
        """
        Calcule boost basé sur exploitation espace d'états fini

        Principe: Sur 60 manches, seulement 2^60 séquences possibles.
        Certaines configurations sont plus probables que d'autres.
        """
        try:
            # Distance à l'équilibre 50/50
            player_deviation = abs(player_prob - 0.5)
            banker_deviation = abs(banker_prob - 0.5)

            max_deviation = max(player_deviation, banker_deviation)

            # Boost basé sur déviation (plus de déviation = plus de structure)
            if max_deviation > 0.1:  # Déviation significative
                # Fonction logarithmique pour éviter amplification excessive
                boost = math.log1p(max_deviation * 10.0) / math.log(6.0)  # Normalisation
                return min(0.1, boost)  # Max 10% boost
            else:
                return 0.0

        except Exception as e:
            logger.error(f"Erreur boost états finis: {e}")
            return 0.0

    def _calculate_conditional_probability_boost(self, probabilities: Dict[str, float]) -> float:
        """
        Calcule boost basé sur chaînes de probabilités conditionnelles

        Exploite P(X_n | X_{n-1}, X_{n-2}, ...) ≠ P(X_n) pour Baccarat
        """
        try:
            player_prob = probabilities.get('player', 0.5)
            banker_prob = probabilities.get('banker', 0.5)

            # Simulation chaîne conditionnelle simple
            # Dans un vrai système, ceci utiliserait l'historique réel

            # Entropie de la distribution
            entropy = -(player_prob * math.log2(player_prob + 1e-10) +
                       banker_prob * math.log2(banker_prob + 1e-10))

            # Boost inversement proportionnel à l'entropie
            # Faible entropie = distribution concentrée = plus de structure
            max_entropy = 1.0  # Entropie max pour distribution binaire
            structure_score = 1.0 - (entropy / max_entropy)

            # Boost conditionnel
            conditional_boost = structure_score * 0.05  # Max 5% boost

            return max(0.0, conditional_boost)

        except Exception as e:
            logger.error(f"Erreur boost probabilités conditionnelles: {e}")
            return 0.0

    def calculate_sequence_dependency_confidence(self, sequence_history: List[int]) -> float:
        """
        Calcule confiance basée sur dépendances de séquence

        Analyse l'historique pour détecter patterns non-indépendants
        """
        try:
            if not sequence_history or len(sequence_history) < 3:
                return 0.5  # Confiance neutre

            # Analyse transitions
            transition_confidence = self._analyze_sequence_transitions(sequence_history)

            # Analyse patterns récurrents
            pattern_confidence = self._analyze_recurring_patterns(sequence_history)

            # Analyse dépendances d'ordre supérieur
            higher_order_confidence = self._analyze_higher_order_dependencies(sequence_history)

            # Combinaison pondérée
            combined_confidence = (
                transition_confidence * 0.4 +
                pattern_confidence * 0.4 +
                higher_order_confidence * 0.2
            )

            return np.clip(combined_confidence, 0.1, 0.9)

        except Exception as e:
            logger.error(f"Erreur confiance dépendances séquence: {e}")
            return 0.5

    def _analyze_sequence_transitions(self, sequence: List[int]) -> float:
        """Analyse transitions dans la séquence"""
        try:
            if len(sequence) < 2:
                return 0.5

            # Comptage transitions
            transitions = {'00': 0, '01': 0, '10': 0, '11': 0}

            for i in range(len(sequence) - 1):
                transition = f"{sequence[i]}{sequence[i+1]}"
                if transition in transitions:
                    transitions[transition] += 1

            total_transitions = sum(transitions.values())
            if total_transitions == 0:
                return 0.5

            # Calcul entropie des transitions
            entropy = 0.0
            for count in transitions.values():
                if count > 0:
                    prob = count / total_transitions
                    entropy -= prob * math.log2(prob)

            # Confiance inversement proportionnelle à l'entropie
            max_entropy = 2.0  # Entropie max pour 4 transitions
            structure_score = 1.0 - (entropy / max_entropy)

            return 0.3 + structure_score * 0.4  # [0.3, 0.7]

        except Exception as e:
            logger.error(f"Erreur analyse transitions: {e}")
            return 0.5

    def _analyze_recurring_patterns(self, sequence: List[int]) -> float:
        """Analyse patterns récurrents dans la séquence"""
        try:
            if len(sequence) < 4:
                return 0.5

            # Recherche patterns de longueur 2-4
            pattern_counts = {}

            for length in range(2, min(5, len(sequence))):
                for i in range(len(sequence) - length + 1):
                    pattern = tuple(sequence[i:i+length])
                    pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1

            # Calcul score récurrence
            total_patterns = sum(pattern_counts.values())
            recurring_patterns = sum(1 for count in pattern_counts.values() if count > 1)

            if total_patterns == 0:
                return 0.5

            recurrence_score = recurring_patterns / len(pattern_counts)

            return 0.3 + recurrence_score * 0.4  # [0.3, 0.7]

        except Exception as e:
            logger.error(f"Erreur analyse patterns récurrents: {e}")
            return 0.5

    def _analyze_higher_order_dependencies(self, sequence: List[int]) -> float:
        """Analyse dépendances d'ordre supérieur"""
        try:
            if len(sequence) < 5:
                return 0.5

            # Test dépendance ordre 2: P(X_n | X_{n-1}, X_{n-2})
            dependencies = []

            for i in range(2, len(sequence)):
                context = (sequence[i-2], sequence[i-1])
                outcome = sequence[i]
                dependencies.append((context, outcome))

            # Groupement par contexte
            context_outcomes = {}
            for context, outcome in dependencies:
                if context not in context_outcomes:
                    context_outcomes[context] = []
                context_outcomes[context].append(outcome)

            # Calcul variance par contexte
            total_variance = 0.0
            valid_contexts = 0

            for outcomes in context_outcomes.values():
                if len(outcomes) > 1:
                    variance = np.var(outcomes)
                    total_variance += variance
                    valid_contexts += 1

            if valid_contexts == 0:
                return 0.5

            avg_variance = total_variance / valid_contexts

            # Confiance basée sur variance (faible variance = plus de structure)
            structure_score = 1.0 - min(1.0, avg_variance * 4.0)

            return 0.3 + structure_score * 0.4  # [0.3, 0.7]

        except Exception as e:
            logger.error(f"Erreur analyse dépendances ordre supérieur: {e}")
            return 0.5

# ═══════════════════════════════════════════════════════════════════
# 3. CALCULATEUR DE MÉTRIQUES
# ═══════════════════════════════════════════════════════════════════

class MetricsCalculator:
    """
    Calculateur de métriques de performance
    """

    def __init__(self):
        self.metrics_history = []

    def calculate_accuracy(self, y_true: List[int], y_pred: List[int]) -> float:
        """Calcule précision"""
        try:
            if not y_true or not y_pred or len(y_true) != len(y_pred):
                return global_config.calculations.default_accuracy
            return accuracy_score(y_true, y_pred)
        except Exception as e:
            logger.error(f"Erreur calcul accuracy: {e}")
            return global_config.calculations.default_accuracy

    def calculate_comprehensive_metrics(self, y_true: List[int], y_pred: List[int],
                                      y_proba: List[float] = None) -> Dict[str, float]:
        """Calcule métriques complètes"""
        try:
            if not y_true or not y_pred:
                return {}

            metrics = {
                global_config.calculations.metric_names[0]: accuracy_score(y_true, y_pred),
                global_config.calculations.metric_names[1]: precision_score(y_true, y_pred, average=global_config.calculations.score_average_method, zero_division=0),
                global_config.calculations.metric_names[2]: recall_score(y_true, y_pred, average=global_config.calculations.score_average_method, zero_division=0),
                global_config.calculations.metric_names[3]: f1_score(y_true, y_pred, average=global_config.calculations.score_average_method, zero_division=0)
            }

            # Métriques probabilistes si disponibles
            if y_proba:
                metrics.update(self._calculate_probabilistic_metrics(y_true, y_proba))

            return metrics

        except Exception as e:
            logger.error(f"Erreur métriques complètes: {e}")
            return {}

    def _calculate_probabilistic_metrics(self, y_true: List[int], y_proba: List[float]) -> Dict[str, float]:
        """Calcule métriques probabilistes"""
        try:
            # Log-loss
            epsilon = global_config.calculations.epsilon_value
            y_proba_clipped = np.clip(y_proba, epsilon, global_config.calculations.uncertainty_base - epsilon)
            log_loss = -np.mean(y_true * np.log(y_proba_clipped) +
                               (1 - y_true) * np.log(1 - y_proba_clipped))

            # Brier Score
            brier_score = np.mean((y_proba - y_true) ** global_config.calculations.brier_score_exponent)

            return {
                global_config.calculations.probabilistic_metric_names[0]: log_loss,
                global_config.calculations.probabilistic_metric_names[1]: brier_score
            }

        except Exception as e:
            logger.error(f"Erreur métriques probabilistes: {e}")
            return {}

# ═══════════════════════════════════════════════════════════════════
# 4. CALCULATEUR DE PRÉDICTIONS
# ═══════════════════════════════════════════════════════════════════

class PredictionCalculator:
    """
    Calculateur de prédictions finales
    """

    def __init__(self, confidence_calculator: ConfidenceCalculator):
        self.confidence_calc = confidence_calculator

    def calculate_final_prediction(self, model_predictions: Dict[str, Dict[str, float]],
                                 model_weights: Dict[str, float]) -> Dict[str, Any]:
        """
        Calcule prédiction finale ensemble
        """
        try:
            if not model_predictions or not model_weights:
                return self._default_prediction()

            # Prédiction pondérée
            weighted_pred = self._calculate_weighted_prediction(model_predictions, model_weights)

            # Confiance
            confidence = self.confidence_calc.calculate_ensemble_confidence(
                model_predictions, model_weights
            )

            # Recommandation
            recommendation = self._determine_recommendation(weighted_pred, confidence)

            return {
                'player': weighted_pred.get('player', global_config.calculations.default_probability),
                'banker': weighted_pred.get('banker', global_config.calculations.default_probability),
                'confidence': confidence,
                'recommendation': recommendation,
                'model_predictions': model_predictions,
                'model_weights': model_weights
            }

        except Exception as e:
            logger.error(f"Erreur prédiction finale: {e}")
            return self._default_prediction()

    def _calculate_weighted_prediction(self, predictions: Dict[str, Dict[str, float]],
                                     weights: Dict[str, float]) -> Dict[str, float]:
        """Calcule prédiction pondérée"""
        try:
            calc_config = global_config.calculations
            default_prob = calc_config.max_prob_center

            total_weight = sum(weights.values())
            if total_weight == global_config.calculations.min_total_weight:
                return {'player': default_prob, 'banker': default_prob}

            weighted_player = sum(
                pred.get('player', default_prob) * weights.get(model, 0)
                for model, pred in predictions.items()
            ) / total_weight

            weighted_banker = sum(
                pred.get('banker', default_prob) * weights.get(model, 0)
                for model, pred in predictions.items()
            ) / total_weight

            # Normalisation pour assurer player + banker = 1
            total = weighted_player + weighted_banker
            if total > global_config.calculations.min_normalization_total:
                weighted_player /= total
                weighted_banker /= total

            return {
                'player': weighted_player,
                'banker': weighted_banker
            }

        except Exception as e:
            logger.error(f"Erreur prédiction pondérée: {e}")
            default_prob = global_config.calculations.max_prob_center
            return {'player': default_prob, 'banker': default_prob}

    def _determine_recommendation(self, prediction: Dict[str, float], confidence: float) -> str:
        """Détermine recommandation basée sur prédiction et confiance"""
        try:
            default_prob = global_config.calculations.max_prob_center
            player_prob = prediction.get('player', default_prob)
            banker_prob = prediction.get('banker', default_prob)

            # Seuil de confiance minimum
            if confidence < self.confidence_calc.confidence_threshold:
                return global_config.calculations.wait_recommendation

            # Recommandation basée sur probabilité la plus élevée
            if player_prob > banker_prob:
                return global_config.calculations.player_recommendation
            elif banker_prob > player_prob:
                return global_config.calculations.banker_recommendation
            else:
                return global_config.calculations.wait_recommendation

        except Exception as e:
            logger.error(f"Erreur recommandation: {e}")
            return global_config.calculations.wait_recommendation

    def _default_prediction(self) -> Dict[str, Any]:
        """Prédiction par défaut"""
        default_prob = global_config.calculations.max_prob_center
        return {
            'player': default_prob,
            'banker': default_prob,
            'confidence': default_prob,
            'recommendation': global_config.calculations.wait_recommendation,
            'model_predictions': {},
            'model_weights': {}
        }

# ═══════════════════════════════════════════════════════════════════
# 5. CALCULATEUR D'INCERTITUDE
# ═══════════════════════════════════════════════════════════════════

class UncertaintyCalculator:
    """
    Calculateur d'incertitude des prédictions - FICHIER UNIQUE POUR TOUS LES CALCULS
    """

    def calculate_prediction_uncertainty(self, predictions: Dict[str, Dict[str, float]],
                                       individual_uncertainties: Dict[str, float] = None,
                                       weights: Dict[str, float] = None) -> float:
        """
        FORMULE UNIFIÉE D'INCERTITUDE ENSEMBLE - Basée sur recherches internationales

        Formule : Incertitude = α × Incertitude_épistémique + β × Incertitude_aléatoire
        """
        try:
            default_prob = global_config.calculations.max_prob_center
            if not predictions or len(predictions) < global_config.calculations.min_predictions_for_uncertainty:
                return default_prob

            # 1. INCERTITUDE ÉPISTÉMIQUE (variance entre modèles - connaissance)
            epistemic_uncertainty = self._calculate_epistemic_uncertainty(predictions, weights)

            # 2. INCERTITUDE ALÉATOIRE (moyenne des incertitudes individuelles - données)
            if individual_uncertainties:
                aleatoric_uncertainty = self._calculate_aleatoric_uncertainty(individual_uncertainties, weights)
            else:
                # Fallback : utiliser variance intra-prédictions
                aleatoric_uncertainty = self._calculate_fallback_aleatoric_uncertainty(predictions)

            # ✅ 3. COEFFICIENTS ADAPTATIFS (basés sur nombre de modèles et consensus)
            num_models = len(predictions)

            # Alpha adaptatif : plus de modèles = plus d'importance à l'épistémique
            alpha = 0.3 + min(0.4, num_models * 0.1)  # 0.3 à 0.7

            # Beta adaptatif : inversement proportionnel au consensus
            model_consensus = 1.0 - epistemic_uncertainty  # Plus de consensus = moins d'aléatoire
            beta = 0.5 + (1.0 - model_consensus) * 0.3  # 0.5 à 0.8

            # ✅ 4. FORMULE UNIFIÉE STABILISÉE
            total_uncertainty = alpha * epistemic_uncertainty + beta * aleatoric_uncertainty

            # Normalisation adaptative (évite division par zéro)
            total_weight = alpha + beta
            normalized_uncertainty = total_uncertainty / max(total_weight, 0.1)

            return np.clip(normalized_uncertainty,
                          global_config.calculations.confidence_min,
                          global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur calcul incertitude unifiée: {e}")
            return global_config.calculations.max_prob_center

    def _calculate_epistemic_uncertainty(self, predictions: Dict[str, Dict[str, float]],
                                       weights: Dict[str, float] = None) -> float:
        """Calcule incertitude épistémique (variance entre modèles)"""
        try:
            # Extraire probabilités Banker de chaque modèle
            banker_probs = [pred.get('banker', 0.5) for pred in predictions.values()]

            if weights and len(weights) == len(banker_probs):
                # Variance pondérée
                weight_values = list(weights.values())
                weighted_mean = np.average(banker_probs, weights=weight_values)
                weighted_variance = np.average((np.array(banker_probs) - weighted_mean)**2, weights=weight_values)
                epistemic = weighted_variance
            else:
                # Variance simple
                epistemic = np.var(banker_probs)

            # ✅ Normalisation stabilisée (évite amplification excessive)
            # Utilise fonction racine carrée pour réduire l'amplification du bruit
            normalized_epistemic = math.sqrt(epistemic * 4.0) / 2.0  # Racine carrée pour stabiliser
            return min(normalized_epistemic, 1.0)

        except Exception as e:
            logger.error(f"Erreur incertitude épistémique: {e}")
            return 0.5

    def _calculate_aleatoric_uncertainty(self, individual_uncertainties: Dict[str, float],
                                       weights: Dict[str, float] = None) -> float:
        """Calcule incertitude aléatoire (moyenne pondérée des incertitudes individuelles)"""
        try:
            uncertainty_values = list(individual_uncertainties.values())

            if weights and len(weights) == len(uncertainty_values):
                # Moyenne pondérée
                weight_values = list(weights.values())
                aleatoric = np.average(uncertainty_values, weights=weight_values)
            else:
                # Moyenne simple
                aleatoric = np.mean(uncertainty_values)

            return np.clip(aleatoric, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Erreur incertitude aléatoire: {e}")
            return 0.5

    def _calculate_fallback_aleatoric_uncertainty(self, predictions: Dict[str, Dict[str, float]]) -> float:
        """Fallback pour incertitude aléatoire basée sur entropie moyenne"""
        try:
            entropies = []

            for pred in predictions.values():
                banker_prob = pred.get('banker', 0.5)
                player_prob = pred.get('player', 0.5)

                # Entropie binaire
                entropy = -(banker_prob * np.log2(banker_prob + 1e-10) +
                           player_prob * np.log2(player_prob + 1e-10))
                entropies.append(entropy)

            # Moyenne des entropies normalisée
            mean_entropy = np.mean(entropies)
            max_entropy = 1.0  # Entropie max pour distribution binaire

            return min(mean_entropy / max_entropy, 1.0)

        except Exception as e:
            logger.error(f"Erreur fallback incertitude aléatoire: {e}")
            return 0.5

    def calculate_lstm_uncertainty_dropout(self, model, X: np.ndarray, n_samples: int = 10) -> np.ndarray:
        """Calcule incertitude LSTM via Dropout Monte Carlo"""
        try:
            if not hasattr(model, 'is_trained') or not model.is_trained:
                return np.full(len(X), global_config.calculations.default_confidence)

            # Activer dropout pour Monte Carlo
            model.train()
            predictions = []

            import torch
            for _ in range(n_samples):
                with torch.no_grad():
                    X_tensor = torch.FloatTensor(X)
                    outputs = model(X_tensor)
                    probas = torch.softmax(outputs, dim=1)
                    predictions.append(probas[:, 1].numpy())  # Probabilité Banker

            model.eval()  # Remettre en mode évaluation

            # Calculer variance des prédictions
            predictions = np.array(predictions)
            uncertainty = np.var(predictions, axis=0)

            # ✅ Normalisation stabilisée pour LSTM
            # Utilise fonction logarithmique pour éviter amplification excessive
            uncertainty = np.clip(np.log1p(uncertainty * 10.0) / np.log(3.0), 0.0, 1.0)

            return uncertainty

        except Exception as e:
            logger.error(f"Erreur incertitude LSTM dropout: {e}")
            return np.full(len(X), global_config.calculations.default_confidence)

    def calculate_lgbm_uncertainty_bagging(self, model, X: np.ndarray) -> np.ndarray:
        """Calcule incertitude LGBM via variance des estimateurs"""
        try:
            if not hasattr(model, 'is_trained') or not model.is_trained:
                return np.full(len(X), global_config.calculations.default_confidence)

            # Si modèle calibré avec estimateurs multiples
            if hasattr(model, 'calibrator') and model.calibrator is not None:
                if hasattr(model.calibrator, 'calibrated_classifiers_'):
                    estimator_probas = []
                    for clf in model.calibrator.calibrated_classifiers_:
                        probas = clf.predict_proba(X)
                        estimator_probas.append(probas[:, 1])  # Probabilité Banker

                    if estimator_probas:
                        estimator_probas = np.array(estimator_probas)
                        uncertainty = np.var(estimator_probas, axis=0)
                        # ✅ Normalisation stabilisée pour LGBM
                        uncertainty = np.clip(np.sqrt(uncertainty * 8.0) / 2.0, 0.0, 1.0)
                        return uncertainty

            # Fallback : incertitude basée sur distance à 0.5
            probas = model.predict_proba(X)
            banker_probs = probas[:, 1]
            uncertainty = 1.0 - np.abs(banker_probs - 0.5) * 2.0

            return np.clip(uncertainty, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Erreur incertitude LGBM bagging: {e}")
            return np.full(len(X), global_config.calculations.default_confidence)

    def calculate_markov_uncertainty_entropy(self, model, X: np.ndarray) -> np.ndarray:
        """Calcule incertitude Markov via entropie des transitions"""
        try:
            if not hasattr(model, 'is_trained') or not model.is_trained:
                return np.full(len(X), 1.0)

            uncertainties = []

            for sequence in X:
                sequence_flat = sequence.flatten() if sequence.ndim > 1 else sequence
                uncertainty = self._calculate_markov_sequence_uncertainty(model, sequence_flat)
                uncertainties.append(uncertainty)

            return np.array(uncertainties)

        except Exception as e:
            logger.error(f"Erreur incertitude Markov entropie: {e}")
            return np.full(len(X), 1.0)

    def _calculate_markov_sequence_uncertainty(self, model, sequence: np.ndarray) -> float:
        """Calcule incertitude pour une séquence Markov unique"""
        try:
            with model.lock:
                # Collecter prédictions de tous les ordres disponibles
                predictions = []

                for order in range(1, min(model.max_order, len(sequence)) + 1):
                    if len(sequence) >= order:
                        state = tuple(sequence[-order:])

                        if state in model.models[order]:
                            total = sum(model.models[order][state].values())
                            count_1 = model.models[order][state].get(1, 0)
                            prob = (count_1 + model.smoothing) / (total + 2 * model.smoothing)
                            predictions.append(prob)

                if not predictions:
                    return 1.0  # Incertitude maximale si aucune prédiction

                # ✅ Incertitude stabilisée pour Markov
                if len(predictions) > 1:
                    # Utilise écart-type au lieu de variance pour réduire amplification
                    uncertainty = np.std(predictions) * 2.0  # Facteur réduit
                else:
                    # Pour un seul ordre, incertitude basée sur entropie
                    prob = predictions[0]
                    entropy = -(prob * np.log2(prob + 1e-10) + (1-prob) * np.log2(1-prob + 1e-10))
                    uncertainty = entropy  # Entropie normalisée pour binaire

                return np.clip(uncertainty, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Erreur incertitude séquence Markov: {e}")
            return 1.0

    def calculate_model_uncertainty(self, model_performance: Dict[str, float]) -> Dict[str, float]:
        """Calcule incertitude par modèle basée sur performance"""
        try:
            uncertainties = {}

            for model, accuracy in model_performance.items():
                # Incertitude inversement proportionnelle à la performance
                uncertainty = global_config.calculations.uncertainty_base - accuracy
                uncertainties[model] = min(global_config.calculations.confidence_max, max(global_config.calculations.confidence_min, uncertainty))

            return uncertainties

        except Exception as e:
            logger.error(f"Erreur incertitude modèles: {e}")
            return {}

# ═══════════════════════════════════════════════════════════════════
# 6. CALCULATEUR D'ENSEMBLE
# ═══════════════════════════════════════════════════════════════════

class EnsembleCalculator:
    """
    Calculateur pour méthodes d'ensemble
    """

    def __init__(self):
        self.confidence_calc = ConfidenceCalculator()
        self.prediction_calc = PredictionCalculator(self.confidence_calc)
        self.uncertainty_calc = UncertaintyCalculator()
        self.metrics_calc = MetricsCalculator()

    def calculate_ensemble_prediction(self, model_outputs: Dict[str, Any],
                                    ensemble_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calcule prédiction d'ensemble complète
        """
        try:
            # Extraction prédictions et poids
            predictions = model_outputs.get('predictions', {})
            weights = ensemble_config.get('weights', {})

            # Prédiction finale
            final_prediction = self.prediction_calc.calculate_final_prediction(predictions, weights)

            # Incertitude
            uncertainty = self.uncertainty_calc.calculate_prediction_uncertainty(predictions)
            final_prediction['uncertainty'] = uncertainty

            # Métriques additionnelles
            final_prediction['ensemble_method'] = ensemble_config.get('method', 'weighted_average')
            final_prediction['num_models'] = len(predictions)

            return final_prediction

        except Exception as e:
            logger.error(f"Erreur ensemble: {e}")
            return self.prediction_calc._default_prediction()

# ═══════════════════════════════════════════════════════════════════
# 7. UTILITAIRES MATHÉMATIQUES
# ═══════════════════════════════════════════════════════════════════

def normalize_probabilities(probabilities: Dict[str, float]) -> Dict[str, float]:
    """Normalise probabilités pour qu'elles somment à 1"""
    try:
        total = sum(probabilities.values())
        if total == global_config.calculations.min_total_weight:
            return {k: global_config.calculations.default_probability/len(probabilities) for k in probabilities.keys()}
        return {k: v/total for k, v in probabilities.items()}
    except Exception as e:
        logger.error(f"Erreur normalisation: {e}")
        return probabilities

def calculate_entropy(probabilities: List[float]) -> float:
    """Calcule entropie d'une distribution"""
    try:
        entropy = -sum(p * math.log(p + global_config.calculations.log_epsilon) for p in probabilities if p > 0)
        return entropy
    except Exception as e:
        logger.error(f"Erreur entropie: {e}")
        return global_config.calculations.default_accuracy

def smooth_probabilities(probabilities: Dict[str, float], alpha: float = None) -> Dict[str, float]:
    """Lissage Laplace des probabilités"""
    try:
        if alpha is None:
            alpha = global_config.calculations.laplace_smoothing_alpha

        n_classes = len(probabilities)
        smoothed = {}

        for k, v in probabilities.items():
            smoothed[k] = (v + alpha) / (1 + alpha * n_classes)

        return normalize_probabilities(smoothed)
    except Exception as e:
        logger.error(f"Erreur lissage: {e}")
        return probabilities

# ═══════════════════════════════════════════════════════════════════
# 8. FONCTIONS DE VALIDATION
# ═══════════════════════════════════════════════════════════════════

def validate_prediction_format(prediction: Dict[str, Any]) -> bool:
    """Valide format d'une prédiction"""
    required_keys = global_config.calculations.required_prediction_keys
    return all(key in prediction for key in required_keys)

def validate_probabilities(probabilities: Dict[str, float]) -> bool:
    """Valide que les probabilités sont cohérentes"""
    try:
        # Vérification valeurs [0,1]
        for prob in probabilities.values():
            if not (0 <= prob <= 1):
                return False

        # Vérification somme proche de 1 (tolérance)
        total = sum(probabilities.values())
        tolerance = global_config.calculations.probability_tolerance
        return abs(total - 1.0) < tolerance

    except Exception:
        return False

# ═══════════════════════════════════════════════════════════════════
# 9. INSTANCES GLOBALES
# ═══════════════════════════════════════════════════════════════════

# Calculateurs globaux
global_confidence_calculator = ConfidenceCalculator()
global_metrics_calculator = MetricsCalculator()
global_uncertainty_calculator = UncertaintyCalculator()
global_ensemble_calculator = EnsembleCalculator()

# Export des classes principales
__all__ = [
    'ConfidenceCalculator', 'MetricsCalculator', 'PredictionCalculator',
    'UncertaintyCalculator', 'EnsembleCalculator',
    'normalize_probabilities', 'calculate_entropy', 'smooth_probabilities',
    'validate_prediction_format', 'validate_probabilities',
    'global_confidence_calculator', 'global_metrics_calculator',
    'global_uncertainty_calculator', 'global_ensemble_calculator'
]
