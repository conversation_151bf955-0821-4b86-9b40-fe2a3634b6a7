# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 524 à 528
# Type: Méthode de la classe LGBMModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def fit(self, X: np.ndarray, y: np.ndarray):
        """Entraînement LGBM (simulation)"""
        logger.info("LGBM: Entraînement simulé - Modèle prêt")
        self.is_trained = True
        return self