# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 211 à 233
# Type: Méthode de la classe RealtimeCalibrator

    def get_calibration_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques de calibration"""
        return {
            'current_round': self.current_round,
            'current_phase': self.current_phase,
            'warmup_complete': self.warmup_complete,
            'calibration_active': self.calibration_active,
            'last_calibration_round': self.last_calibration_round,
            'calibrated_parameters': self.calibrated_parameters.copy(),
            'realtime_metrics': self._get_realtime_metrics(),
            'system_resources': {
                'ram_usage_gb': psutil.virtual_memory().used / (1024**3),
                'cpu_usage_percent': psutil.cpu_percent(),
                'available_cores': self.cpu_cores
            },
            'performance_history_size': len(self.performance_history),
            'calibration_history_size': len(self.calibration_history),
            'cache_sizes': {
                'pattern_performance': len(self.pattern_performance_cache),
                'parameter_combinations': len(self.parameter_combination_cache),
                'calibration_results': len(self.calibration_results_cache)
            }
        }