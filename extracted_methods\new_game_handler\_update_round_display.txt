# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 335 à 351
# Type: Méthode de la classe NewGameHandler

    def _update_round_display(self):
        """Met à jour affichage manche"""
        try:
            # Mise à jour compteur manches
            round_message = f"Manche: {self.game_round}/60"

            if hasattr(self.main_interface, 'stats_vars') and 'game_stats' in self.main_interface.stats_vars:
                current_stats = self.main_interface.stats_vars['game_stats'].get()
                # Mise à jour avec nouveau compteur
                updated_stats = f"{round_message} | " + current_stats.split('|', 1)[-1] if '|' in current_stats else round_message
                self.main_interface.stats_vars['game_stats'].set(updated_stats)

            # Mise à jour phase
            self._update_game_phase_display()

        except Exception as e:
            logger.error(f"Erreur mise à jour affichage: {e}")