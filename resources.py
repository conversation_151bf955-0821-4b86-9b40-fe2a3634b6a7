"""
GESTION RESSOURCES POUR LE NOUVEAU PRÉDICTEUR BACCARAT
======================================================

Gestion mémoire, cache et détection ressources optimisées.
Basé sur l'analyse de 200+ fichiers de l'ancien système.

STRUCTURE DU MODULE:
===================
1. IMPORTS ET DÉPENDANCES
2. GESTIONNAIRE MÉMOIRE
3. SYSTÈME DE CACHE
4. DÉTECTEUR RESSOURCES
5. MON<PERSON>EUR PERFORMANCE
6. GESTIONNAIRE FICHIERS
7. OPTIMISEUR RESSOURCES
8. UTILITAIRES SYSTÈME
9. INSTANCES GLOBALES
"""

# ═══════════════════════════════════════════════════════════════════
# 1. IMPORTS ET DÉPENDANCES
# ═══════════════════════════════════════════════════════════════════

import logging
import gc
import threading
import time
import os
import sys
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass
from pathlib import Path
import json
import pickle
from datetime import datetime, timedelta

# Import configuration centralisée
sys.path.insert(0, str(Path(__file__).parent.parent / "parameters"))
from parameters import global_config

# Imports conditionnels
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

logger = logging.getLogger(__name__)

# ═══════════════════════════════════════════════════════════════════
# 2. GESTIONNAIRE MÉMOIRE
# ═══════════════════════════════════════════════════════════════════

class MemoryManager:
    """
    Gestionnaire mémoire unifié
    Basé sur l'analyse des optimisations de l'ancien système
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        # Utilisation configuration centralisée (adaptée pour version flat)
        self.max_memory_gb = global_config.azr.max_ram_usage_gb
        self.cleanup_interval = 60  # 60 secondes par défaut
        self.alert_threshold = 85.0  # 85% par défaut

        self._last_cleanup = time.time()
        self._memory_stats = {}
        self.lock = threading.Lock()

    def get_memory_usage(self) -> Dict[str, float]:
        """Obtient utilisation mémoire actuelle"""
        try:
            memory_info = {}

            if PSUTIL_AVAILABLE:
                # Mémoire système
                system_memory = psutil.virtual_memory()
                memory_info.update({
                    'system_total_gb': system_memory.total / (1024**3),
                    'system_used_gb': system_memory.used / (1024**3),
                    'system_percent': system_memory.percent,
                    'system_available_gb': system_memory.available / (1024**3)
                })

                # Mémoire processus
                process = psutil.Process()
                process_memory = process.memory_info()
                memory_info.update({
                    'process_rss_gb': process_memory.rss / (1024**3),
                    'process_vms_gb': process_memory.vms / (1024**3)
                })

            # Mémoire PyTorch si disponible
            if TORCH_AVAILABLE and torch.cuda.is_available():
                memory_info.update({
                    'gpu_allocated_gb': torch.cuda.memory_allocated() / (1024**3),
                    'gpu_reserved_gb': torch.cuda.memory_reserved() / (1024**3)
                })

            return memory_info

        except Exception as e:
            logger.error(f"Erreur lecture mémoire: {e}")
            return {}

    def cleanup_memory(self, force: bool = False) -> bool:
        """Nettoyage mémoire intelligent"""
        try:
            current_time = time.time()

            # Vérification intervalle
            if not force and (current_time - self._last_cleanup) < self.cleanup_interval:
                return False

            with self.lock:
                # Garbage collection Python
                collected = gc.collect()

                # Nettoyage PyTorch si disponible
                if TORCH_AVAILABLE and torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()

                self._last_cleanup = current_time

                logger.debug(f"Nettoyage mémoire: {collected} objets collectés")
                return True

        except Exception as e:
            logger.error(f"Erreur nettoyage mémoire: {e}")
            return False

    def check_memory_pressure(self) -> bool:
        """Vérifie pression mémoire"""
        try:
            memory_info = self.get_memory_usage()

            # Vérification mémoire système
            system_percent = memory_info.get('system_percent', 0)
            if system_percent > self.alert_threshold:
                logger.warning(f"Pression mémoire système: {system_percent:.1f}%")
                return True

            # Vérification mémoire processus
            process_gb = memory_info.get('process_rss_gb', 0)
            if process_gb > self.max_memory_gb:
                logger.warning(f"Limite mémoire processus dépassée: {process_gb:.1f}GB")
                return True

            return False

        except Exception as e:
            logger.error(f"Erreur vérification mémoire: {e}")
            return False

# ═══════════════════════════════════════════════════════════════════
# 3. SYSTÈME DE CACHE
# ═══════════════════════════════════════════════════════════════════

class Cache:
    """
    Système de cache intelligent avec TTL
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        # Utilisation configuration centralisée (adaptée pour version flat)
        self.max_size_mb = global_config.azr.pattern_cache_size // 1000  # Conversion en MB
        self.default_ttl = 300  # 5 minutes par défaut
        self.enable_cache = global_config.azr.memory_optimization
        self.cache_items_multiplier = 100  # 100 items par MB

        self._cache = {}
        self._timestamps = {}
        self._access_count = {}
        self.lock = threading.Lock()

    def get(self, key: str) -> Optional[Any]:
        """Récupère valeur du cache"""
        if not self.enable_cache:
            return None

        try:
            with self.lock:
                if key not in self._cache:
                    return None

                # Vérification TTL
                if self._is_expired(key):
                    self._remove_key(key)
                    return None

                # Mise à jour statistiques accès
                self._access_count[key] = self._access_count.get(key, 0) + 1

                return self._cache[key]

        except Exception as e:
            logger.error(f"Erreur lecture cache {key}: {e}")
            return None

    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Stocke valeur dans le cache"""
        if not self.enable_cache:
            return False

        try:
            with self.lock:
                # Vérification espace disponible
                if self._should_evict():
                    self._evict_lru()

                # Stockage
                self._cache[key] = value
                self._timestamps[key] = time.time()
                self._access_count[key] = 1

                return True

        except Exception as e:
            logger.error(f"Erreur écriture cache {key}: {e}")
            return False

    def clear(self) -> bool:
        """Vide le cache"""
        try:
            with self.lock:
                self._cache.clear()
                self._timestamps.clear()
                self._access_count.clear()
                return True
        except Exception as e:
            logger.error(f"Erreur vidage cache: {e}")
            return False

    def _is_expired(self, key: str) -> bool:
        """Vérifie si clé expirée"""
        if key not in self._timestamps:
            return True

        age = time.time() - self._timestamps[key]
        return age > self.default_ttl

    def _should_evict(self) -> bool:
        """Détermine si éviction nécessaire"""
        # Éviction basée sur nombre d'éléments (approximation)
        max_items = self.max_size_mb * self.cache_items_multiplier
        return len(self._cache) >= max_items

    def _evict_lru(self) -> None:
        """Éviction LRU (Least Recently Used)"""
        if not self._access_count:
            return

        # Trouve clé la moins utilisée
        lru_key = min(self._access_count.keys(), key=lambda k: self._access_count[k])
        self._remove_key(lru_key)

    def _remove_key(self, key: str) -> None:
        """Supprime clé du cache"""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)
        self._access_count.pop(key, None)

# ═══════════════════════════════════════════════════════════════════
# 4. DÉTECTEUR RESSOURCES
# ═══════════════════════════════════════════════════════════════════

class ResourceDetector:
    """
    Détecteur de ressources système
    """

    def __init__(self):
        self._system_info = None
        self._detection_time = None

    def detect_system_resources(self) -> Dict[str, Any]:
        """Détecte ressources système disponibles"""
        try:
            # Cache résultats selon configuration (adaptée pour version flat)
            cache_duration = 300  # 5 minutes par défaut
            if (self._system_info and self._detection_time and
                time.time() - self._detection_time < cache_duration):
                return self._system_info

            system_info = {}

            # CPU
            system_info['cpu_count'] = os.cpu_count() or 1
            system_info['cpu_count_logical'] = system_info['cpu_count']

            if PSUTIL_AVAILABLE:
                system_info['cpu_count_physical'] = psutil.cpu_count(logical=False) or 1
                system_info['cpu_freq'] = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {}

            # Mémoire
            if PSUTIL_AVAILABLE:
                memory = psutil.virtual_memory()
                system_info['memory'] = {
                    'total_gb': memory.total / (1024**3),
                    'available_gb': memory.available / (1024**3),
                    'percent_used': memory.percent
                }

            # GPU
            system_info['gpu'] = self._detect_gpu()

            # Stockage
            if PSUTIL_AVAILABLE:
                disk = psutil.disk_usage('/')
                system_info['disk'] = {
                    'total_gb': disk.total / (1024**3),
                    'free_gb': disk.free / (1024**3),
                    'percent_used': (disk.used / disk.total) * 100
                }

            # Plateforme
            system_info['platform'] = {
                'system': sys.platform,
                'python_version': sys.version,
                'architecture': os.name
            }

            self._system_info = system_info
            self._detection_time = time.time()

            return system_info

        except Exception as e:
            logger.error(f"Erreur détection ressources: {e}")
            return {}

    def _detect_gpu(self) -> Dict[str, Any]:
        """Détecte GPU disponibles"""
        gpu_info = {'available': False, 'devices': []}

        try:
            if TORCH_AVAILABLE and torch.cuda.is_available():
                gpu_info['available'] = True
                gpu_info['device_count'] = torch.cuda.device_count()

                for i in range(torch.cuda.device_count()):
                    device_props = torch.cuda.get_device_properties(i)
                    gpu_info['devices'].append({
                        'id': i,
                        'name': device_props.name,
                        'memory_gb': device_props.total_memory / (1024**3),
                        'compute_capability': f"{device_props.major}.{device_props.minor}"
                    })
        except Exception as e:
            logger.debug(f"Détection GPU échouée: {e}")

        return gpu_info

# ═══════════════════════════════════════════════════════════════════
# 5. MONITEUR PERFORMANCE
# ═══════════════════════════════════════════════════════════════════

class PerformanceMonitor:
    """
    Moniteur de performance système
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        # Utilisation configuration centralisée (adaptée pour version flat)
        self.monitor_interval = 5  # 5 secondes par défaut
        self.alert_cpu_threshold = 80.0  # 80% par défaut
        self.alert_memory_threshold = 85.0  # 85% par défaut
        self.performance_history_max_items = global_config.azr.performance_cache_size

        self._monitoring = False
        self._monitor_thread = None
        self._performance_history = []
        self.lock = threading.Lock()

    def start_monitoring(self) -> bool:
        """Démarre monitoring"""
        try:
            if self._monitoring:
                return True

            self._monitoring = True
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()

            logger.info("Monitoring performance démarré")
            return True

        except Exception as e:
            logger.error(f"Erreur démarrage monitoring: {e}")
            return False

    def stop_monitoring(self) -> bool:
        """Arrête monitoring"""
        try:
            self._monitoring = False
            if self._monitor_thread:
                self._monitor_thread.join(timeout=5)

            logger.info("Monitoring performance arrêté")
            return True

        except Exception as e:
            logger.error(f"Erreur arrêt monitoring: {e}")
            return False

    def get_current_performance(self) -> Dict[str, Any]:
        """Obtient performance actuelle"""
        try:
            perf_data = {
                'timestamp': time.time(),
                'cpu_percent': 0.0,
                'memory_percent': 0.0,
                'disk_io': {},
                'network_io': {}
            }

            if PSUTIL_AVAILABLE:
                perf_data['cpu_percent'] = psutil.cpu_percent(interval=1)
                perf_data['memory_percent'] = psutil.virtual_memory().percent

                # I/O disque
                disk_io = psutil.disk_io_counters()
                if disk_io:
                    perf_data['disk_io'] = {
                        'read_mb': disk_io.read_bytes / (1024*1024),
                        'write_mb': disk_io.write_bytes / (1024*1024)
                    }

                # I/O réseau
                net_io = psutil.net_io_counters()
                if net_io:
                    perf_data['network_io'] = {
                        'sent_mb': net_io.bytes_sent / (1024*1024),
                        'recv_mb': net_io.bytes_recv / (1024*1024)
                    }

            return perf_data

        except Exception as e:
            logger.error(f"Erreur lecture performance: {e}")
            return {}

    def _monitor_loop(self):
        """Boucle de monitoring"""
        while self._monitoring:
            try:
                perf_data = self.get_current_performance()

                with self.lock:
                    self._performance_history.append(perf_data)

                    # Garder seulement dernières mesures selon configuration
                    if len(self._performance_history) > self.performance_history_max_items:
                        self._performance_history.pop(0)

                # Vérification alertes
                self._check_alerts(perf_data)

                time.sleep(self.monitor_interval)

            except Exception as e:
                logger.error(f"Erreur boucle monitoring: {e}")
                time.sleep(self.monitor_interval)

    def _check_alerts(self, perf_data: Dict[str, Any]):
        """Vérifie seuils d'alerte"""
        try:
            cpu_percent = perf_data.get('cpu_percent', 0)
            memory_percent = perf_data.get('memory_percent', 0)

            if cpu_percent > self.alert_cpu_threshold:
                logger.warning(f"Alerte CPU: {cpu_percent:.1f}%")

            if memory_percent > self.alert_memory_threshold:
                logger.warning(f"Alerte mémoire: {memory_percent:.1f}%")

        except Exception as e:
            logger.error(f"Erreur vérification alertes: {e}")

# ═══════════════════════════════════════════════════════════════════
# 6. GESTIONNAIRE FICHIERS
# ═══════════════════════════════════════════════════════════════════

class FileManager:
    """
    Gestionnaire de fichiers optimisé
    """

    def __init__(self, base_dir: str = "data"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)

    def save_data(self, data: Any, filename: str, format: str = "json") -> bool:
        """Sauvegarde données"""
        try:
            filepath = self.base_dir / filename

            if format == "json":
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            elif format == "pickle":
                with open(filepath, 'wb') as f:
                    pickle.dump(data, f)
            else:
                raise ValueError(f"Format non supporté: {format}")

            logger.debug(f"Données sauvegardées: {filepath}")
            return True

        except Exception as e:
            logger.error(f"Erreur sauvegarde {filename}: {e}")
            return False

    def load_data(self, filename: str, format: str = "json") -> Optional[Any]:
        """Charge données"""
        try:
            filepath = self.base_dir / filename

            if not filepath.exists():
                return None

            if format == "json":
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
            elif format == "pickle":
                with open(filepath, 'rb') as f:
                    return pickle.load(f)
            else:
                raise ValueError(f"Format non supporté: {format}")

        except Exception as e:
            logger.error(f"Erreur chargement {filename}: {e}")
            return None

# ═══════════════════════════════════════════════════════════════════
# 7. OPTIMISEUR RESSOURCES
# ═══════════════════════════════════════════════════════════════════

class ResourceOptimizer:
    """
    Optimiseur de ressources global
    """

    def __init__(self):
        self.memory_manager = MemoryManager()
        self.cache = Cache()
        self.detector = ResourceDetector()
        self.monitor = PerformanceMonitor()
        self.file_manager = FileManager()

    def optimize_all_resources(self) -> Dict[str, Any]:
        """Optimise toutes les ressources"""
        try:
            results = {}

            # Optimisation mémoire
            results['memory'] = self.memory_manager.cleanup_memory(force=True)

            # Nettoyage cache
            cache_cleared = self.cache.clear()
            results['cache'] = {'cleared': cache_cleared}

            # Détection ressources
            results['resources'] = self.detector.detect_system_resources()

            # Performance actuelle
            results['performance'] = self.monitor.get_current_performance()

            return results

        except Exception as e:
            logger.error(f"Erreur optimisation ressources: {e}")
            return {'success': False, 'error': str(e)}

# ═══════════════════════════════════════════════════════════════════
# 8. UTILITAIRES SYSTÈME
# ═══════════════════════════════════════════════════════════════════

def get_system_info() -> Dict[str, Any]:
    """Informations système rapides"""
    detector = ResourceDetector()
    return detector.detect_system_resources()

def cleanup_system_resources() -> bool:
    """Nettoyage rapide ressources"""
    try:
        memory_manager = MemoryManager()
        return memory_manager.cleanup_memory(force=True)
    except Exception as e:
        logger.error(f"Erreur nettoyage: {e}")
        return False

def check_resource_availability() -> Dict[str, bool]:
    """Vérifie disponibilité ressources"""
    return {
        'psutil': PSUTIL_AVAILABLE,
        'torch': TORCH_AVAILABLE,
        'gpu': TORCH_AVAILABLE and torch.cuda.is_available() if TORCH_AVAILABLE else False
    }

# ═══════════════════════════════════════════════════════════════════
# 9. INSTANCES GLOBALES
# ═══════════════════════════════════════════════════════════════════

# Gestionnaires globaux
global_memory_manager = MemoryManager()
global_cache = Cache()
global_resource_detector = ResourceDetector()
global_performance_monitor = PerformanceMonitor()
global_file_manager = FileManager()
global_resource_optimizer = ResourceOptimizer()

# Export des classes principales
__all__ = [
    'MemoryManager', 'Cache', 'ResourceDetector',
    'PerformanceMonitor', 'FileManager', 'ResourceOptimizer',
    'get_system_info', 'cleanup_system_resources', 'check_resource_availability',
    'global_memory_manager', 'global_cache', 'global_resource_detector',
    'global_performance_monitor', 'global_file_manager', 'global_resource_optimizer'
]
