# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 266 à 275
# Type: Méthode de la classe RealtimeCalibrator

    def _should_trigger_calibration(self) -> bool:
        """Détermine si calibration doit être déclenchée"""
        rounds_since_last = self.current_round - self.last_calibration_round

        if self.current_phase == 'warmup':
            return rounds_since_last >= self.warmup_calibration_freq
        elif self.current_phase == 'optimal':
            return rounds_since_last >= self.optimal_calibration_freq
        else:  # post_optimal
            return rounds_since_last >= self.post_optimal_calibration_freq