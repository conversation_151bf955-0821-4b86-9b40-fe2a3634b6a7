# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 122 à 150
# Type: Méthode de la classe BaccaratPatternValidator

    def _apply_sequence_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern de séquence"""
        sequence = pattern['pattern']
        seq_len = len(sequence)

        if len(test_data) < seq_len:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        # Vérification si fin de test_data correspond au pattern
        recent_sequence = test_data[-seq_len:]

        if recent_sequence == sequence:
            # Pattern détecté, prédire continuation ou rupture
            # Logique basée sur fréquence historique du pattern
            next_outcome = self._predict_sequence_continuation(sequence, test_data)
            confidence = pattern.get('confidence', 0.5)

            return {
                'predicted_outcome': next_outcome,
                'confidence': confidence,
                'reason': 'sequence_match',
                'matched_sequence': sequence
            }
        else:
            return {
                'predicted_outcome': None,
                'confidence': 0.0,
                'reason': 'sequence_no_match'
            }