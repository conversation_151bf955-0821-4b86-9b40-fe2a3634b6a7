# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 331 à 366
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calcule métriques de performance"""
        if not self.current_session:
            return {}

        session_perf = self.current_session['session_performance']

        if not session_perf:
            return {'session_accuracy': 0.0}

        # Métriques session
        session_accuracy = np.mean([p['correct'] for p in session_perf])
        avg_confidence = np.mean([p['confidence'] for p in session_perf])
        avg_uncertainty = np.mean([p['uncertainty'] for p in session_perf])
        avg_patterns_used = np.mean([p['patterns_used'] for p in session_perf])

        # Tendances récentes (10 derniers rounds)
        recent_perf = session_perf[-10:] if len(session_perf) >= 10 else session_perf
        recent_accuracy = np.mean([p['correct'] for p in recent_perf])

        # Métriques globales
        global_accuracy = 0.0
        if self.performance_history:
            global_accuracy = np.mean([p['correct'] for p in self.performance_history])

        return {
            'session_accuracy': session_accuracy,
            'recent_accuracy': recent_accuracy,
            'global_accuracy': global_accuracy,
            'avg_confidence': avg_confidence,
            'avg_uncertainty': avg_uncertainty,
            'avg_patterns_used': avg_patterns_used,
            'total_rounds': self.current_session['rounds_played'],
            'learning_cycles': self.learning_cycles,
            'adaptations_made': self.current_session['adaptations_made']
        }