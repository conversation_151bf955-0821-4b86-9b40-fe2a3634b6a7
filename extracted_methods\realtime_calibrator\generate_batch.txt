# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 487 à 502
# Type: Méthode de la classe RealtimeCalibrator

            def generate_batch(batch_size: int) -> List[Dict[str, float]]:
                batch_combinations = []
                for _ in range(batch_size):
                    combination = {}
                    for param, (min_val, max_val) in self.parameter_ranges.items():
                        # Exploration élargie en phase warmup
                        if self.current_phase == 'warmup':
                            range_expansion = (max_val - min_val) * exploration_factor
                            expanded_min = max(min_val, min_val - range_expansion)
                            expanded_max = min(max_val, max_val + range_expansion)
                        else:
                            expanded_min, expanded_max = min_val, max_val

                        combination[param] = np.random.uniform(expanded_min, expanded_max)
                    batch_combinations.append(combination)
                return batch_combinations