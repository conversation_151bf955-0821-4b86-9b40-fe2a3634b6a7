# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 815 à 822
# Type: Méthode

def calculate_entropy(probabilities: List[float]) -> float:
    """Calcule entropie d'une distribution"""
    try:
        entropy = -sum(p * math.log(p + global_config.calculations.log_epsilon) for p in probabilities if p > 0)
        return entropy
    except Exception as e:
        logger.error(f"Erreur entropie: {e}")
        return global_config.calculations.default_accuracy