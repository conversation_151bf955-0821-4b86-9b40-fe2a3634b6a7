# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 405 à 427
# Type: Méthode de la classe AdaptiveReasoner

    def _generate_binary_prediction_result(self, prediction: Dict[str, Any],
                                         confidence_scores: Dict[str, float],
                                         uncertainty: float) -> str:
        """
        Génère résultat prédiction binaire PURE (suppression logique WAIT)

        OBJECTIF UNIQUE: Prédire 0 (Player) ou 1 (Banker) avec confiance maximale
        SUPPRESSION TOTALE: Logique WAIT/TIE/Attendre
        """
        # ═══════════════════════════════════════════════════════════════════
        # PRÉDICTION BINAIRE PURE - SUPPRESSION LOGIQUE WAIT/TIE
        # ═══════════════════════════════════════════════════════════════════

        predicted_outcome = prediction.get('predicted_outcome', 0)
        overall_confidence = confidence_scores['overall']

        # Retour TOUJOURS une prédiction binaire (jamais WAIT/TIE)
        if predicted_outcome == 0:
            confidence_level = "élevée" if overall_confidence > 0.7 else "modérée" if overall_confidence > 0.5 else "faible"
            return f"Player - Confiance {confidence_level}"
        else:
            confidence_level = "élevée" if overall_confidence > 0.7 else "modérée" if overall_confidence > 0.5 else "faible"
            return f"Banker - Confiance {confidence_level}"