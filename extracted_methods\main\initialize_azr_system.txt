# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main.py
# Lignes: 94 à 132
# Type: Méthode

def initialize_azr_system():
    """Initialise le système AZR"""
    try:
        logger.info("Initialisation système AZR Baccarat")

        # Import des modules AZR (version flat - tous dans le même dossier)
        from parameters import global_config
        logger.info("Configuration globale importée")

        # Import modules AZR principaux (version flat)
        from adaptive_reasoner import BaccaratAdaptiveReasoner
        from pattern_proposer import BaccaratPatternProposer
        from pattern_validator import BaccaratPatternValidator
        from self_play_engine import BaccaratSelfPlayEngine
        logger.info("Modules AZR core importés avec succès")

        # Import système AZR complet
        from azr_core import AZRSystem
        logger.info("Système AZR complet importé avec succès")

        logger.info("Modules AZR importés avec succès")

        # Vérification configuration (optionnelle)
        try:
            if hasattr(global_config, 'azr'):
                logger.info("Configuration AZR validée")
            else:
                logger.info("Configuration AZR par défaut utilisée")
        except:
            logger.info("Configuration AZR par défaut utilisée")

        return True

    except ImportError as e:
        logger.error(f"Erreur import modules AZR: {e}")
        return False
    except Exception as e:
        logger.error(f"Erreur initialisation AZR: {e}")
        return False