"""
PARAMÈTRES SIMPLIFIÉS AZR BACCARAT
==================================

Configuration centralisée simplifiée pour le programme AZR Baccarat.
Focus sur prédiction binaire Player/Banker avec performance optimale.
"""

from dataclasses import dataclass
from typing import List, Optional, Tuple


@dataclass
class AZRConfig:
    """Configuration AZR pour prédiction binaire Baccarat"""

    # ═══════════════════════════════════════════════════════════════════
    # PARAMÈTRES PARTIE 60 MANCHES
    # ═══════════════════════════════════════════════════════════════════

    # Structure partie
    total_rounds: int = 60
    warmup_phase_rounds: int = 30
    prediction_start_round: int = 31
    prediction_end_round: int = 60

    # ═══════════════════════════════════════════════════════════════════
    # PARAMÈTRES AZR ADAPTATIFS
    # ═══════════════════════════════════════════════════════════════════

    # Apprentissage adaptatif
    learning_rate: float = 0.15
    confidence_threshold: float = 0.35
    exploration_rate: float = 0.4
    adaptation_rate: float = 0.12
    pattern_decay_rate: float = 0.04

    # Patterns
    min_pattern_length: int = 2
    max_pattern_length: int = 8
    max_history_length: int = 100
    min_validation_samples: int = 10
    validation_window: int = 20
    reward_decay: float = 0.05
    adaptation_threshold: float = 0.45
    max_session_rounds: int = 60
    max_validation_history: int = 1000
    max_game_history: int = 200

    # Seuils adaptatifs illimités
    min_confidence: float = 0.01
    max_confidence: float = 0.99
    unlimited_performance: bool = True

    # ═══════════════════════════════════════════════════════════════════
    # RESSOURCES SYSTÈME
    # ═══════════════════════════════════════════════════════════════════

    # Haute performance
    max_ram_usage_gb: float = 28.0
    use_all_cpu_cores: bool = True
    high_performance_mode: bool = True

    # Cache et mémoire
    pattern_cache_size: int = 25000
    performance_cache_size: int = 5000
    memory_optimization: bool = True


@dataclass
class SystemConfig:
    """Configuration système"""

    # Version
    version: str = "1.0.0"
    name: str = "AZR Baccarat Predictor"

    # Logging
    log_level: str = "INFO"
    debug_mode: bool = False

    # Chemins
    data_dir: str = "data"
    models_dir: str = "models"
    logs_dir: str = "logs"


@dataclass
class PredictionConfig:
    """Configuration prédictions binaires"""

    # Objectif binaire pur
    player_outcome: int = 0
    banker_outcome: int = 1
    outcome_names: List[str] = None

    # Métriques essentielles
    track_accuracy: bool = True
    track_confidence: bool = True
    track_streaks: bool = True

    # Validation
    min_prediction_confidence: float = 0.1
    max_prediction_confidence: float = 0.9

    def __post_init__(self):
        if self.outcome_names is None:
            self.outcome_names = ['Player', 'Banker']


@dataclass
class InterfaceConfig:
    """Configuration interface graphique"""

    # Fenêtre
    window_title: str = "🎯 AZR Baccarat - Prédicteur Binaire"
    window_width: int = 1000
    window_height: int = 700

    # Thème
    theme: str = "default"
    font_family: str = "Segoe UI"

    # Couleurs
    accent_color: str = "#0066CC"
    success_color: str = "#28a745"
    warning_color: str = "#ffc107"
    error_color: str = "#dc3545"

    # Mise à jour
    ui_update_interval: int = 1000

    # Affichage
    decimal_places: int = 1
    show_confidence: bool = True
    show_phase: bool = True


@dataclass
class CalibrationConfig:
    """Configuration calibration temps réel"""

    # Fréquences calibration
    warmup_calibration_frequency: int = 3
    optimal_calibration_frequency: int = 2
    post_optimal_calibration_frequency: int = 5

    # Paramètres adaptatifs
    threshold_adaptation_rate: float = 0.1
    performance_tracking_window: int = 20

    # Auto-optimisation
    auto_optimize_thresholds: bool = True
    auto_optimize_parameters: bool = True
    continuous_learning: bool = True


@dataclass
class CalculationsConfig:
    """Configuration calculs et formules"""

    # Seuils de base
    confidence_threshold: float = 0.35
    uncertainty_threshold: float = 0.65
    prediction_threshold: float = 0.5

    # Facteurs confiance
    confidence_base_factor: float = 0.8
    confidence_diversity_factor: float = 0.4
    confidence_multiplier: float = 2.0
    max_prob_center: float = 0.5

    # Limites confiance
    confidence_min: float = 0.01
    confidence_max: float = 0.99
    default_confidence: float = 0.5

    # Métriques
    default_accuracy: float = 0.5
    score_average_method: str = "binary"
    metric_names: List[str] = None
    probabilistic_metric_names: List[str] = None

    # Calculs mathématiques
    log_epsilon: float = 1e-10
    epsilon_value: float = 1e-15
    uncertainty_base: float = 1.0
    brier_score_exponent: int = 2
    variance_divisor: float = 2.0

    # Valeurs par défaut
    default_probability: float = 0.5
    default_diversity_factor: float = 0.5
    default_weight_factor: float = 1.0
    min_total_weight: float = 0.0
    min_normalization_total: float = 0.0
    min_predictions_for_uncertainty: int = 1

    # Recommandations
    wait_recommendation: str = "Attendre"
    player_recommendation: str = "Player"
    banker_recommendation: str = "Banker"

    def __post_init__(self):
        if self.metric_names is None:
            self.metric_names = ['accuracy', 'precision', 'recall', 'f1_score']
        if self.probabilistic_metric_names is None:
            self.probabilistic_metric_names = ['log_loss', 'brier_score']


class GlobalConfig:
    """Configuration globale unifiée"""

    def __init__(self):
        # Initialisation des configurations
        self.azr = AZRConfig()
        self.system = SystemConfig()
        self.prediction = PredictionConfig()
        self.interface = InterfaceConfig()
        self.calibration = CalibrationConfig()
        self.calculations = CalculationsConfig()

        # Validation configuration
        self._validate_config()

    def _validate_config(self):
        """Valide la cohérence de la configuration"""
        try:
            # Validation AZR
            assert 0 < self.azr.learning_rate < 1, "Learning rate doit être entre 0 et 1"
            assert 0 < self.azr.confidence_threshold < 1, "Confidence threshold doit être entre 0 et 1"
            assert self.azr.total_rounds == 60, "Total rounds doit être 60"
            assert self.azr.prediction_start_round == 31, "Prédictions doivent commencer à la manche 31"

            # Validation système
            assert self.azr.max_ram_usage_gb > 0, "RAM usage doit être positif"

            # Validation prédiction
            assert self.prediction.player_outcome == 0, "Player outcome doit être 0"
            assert self.prediction.banker_outcome == 1, "Banker outcome doit être 1"

            return True

        except AssertionError as e:
            print(f"Erreur validation configuration: {e}")
            return False

    def get_phase_parameters(self, round_number: int) -> dict:
        """Obtient paramètres spécifiques à la phase"""
        if round_number <= self.azr.warmup_phase_rounds:
            return {
                'exploration_rate': 0.8,
                'learning_rate': 0.2,
                'confidence_threshold': 0.2,
                'calibration_frequency': self.calibration.warmup_calibration_frequency
            }
        elif round_number <= self.azr.prediction_end_round:
            return {
                'exploration_rate': 0.3,
                'learning_rate': 0.15,
                'confidence_threshold': 0.4,
                'calibration_frequency': self.calibration.optimal_calibration_frequency
            }
        else:
            return {
                'exploration_rate': 0.1,
                'learning_rate': 0.1,
                'confidence_threshold': 0.6,
                'calibration_frequency': self.calibration.post_optimal_calibration_frequency
            }

    def is_prediction_round(self, round_number: int) -> bool:
        """Vérifie si la manche est dans la fenêtre de prédiction"""
        return self.azr.prediction_start_round <= round_number <= self.azr.prediction_end_round

    def is_warmup_phase(self, round_number: int) -> bool:
        """Vérifie si la manche est en phase d'échauffement"""
        return round_number <= self.azr.warmup_phase_rounds

    def get_resource_config(self) -> dict:
        """Obtient configuration ressources système"""
        return {
            'max_ram_gb': self.azr.max_ram_usage_gb,
            'use_all_cores': self.azr.use_all_cpu_cores,
            'high_performance': self.azr.high_performance_mode,
            'pattern_cache_size': self.azr.pattern_cache_size,
            'performance_cache_size': self.azr.performance_cache_size
        }


# Instance globale
global_config = GlobalConfig()


# Fonctions utilitaires
def get_config() -> GlobalConfig:
    """Obtient la configuration globale"""
    return global_config


def validate_prediction(prediction: dict) -> bool:
    """Valide une prédiction"""
    required_keys = ['predicted_outcome', 'confidence', 'player_probability', 'banker_probability']

    if not all(key in prediction for key in required_keys):
        return False

    # Validation valeurs
    if prediction['predicted_outcome'] not in [0, 1]:
        return False

    if not (0 <= prediction['confidence'] <= 1):
        return False

    if not (0 <= prediction['player_probability'] <= 1):
        return False

    if not (0 <= prediction['banker_probability'] <= 1):
        return False

    # Validation somme probabilités
    prob_sum = prediction['player_probability'] + prediction['banker_probability']
    if not (0.95 <= prob_sum <= 1.05):  # Tolérance pour erreurs d'arrondi
        return False

    return True


def get_phase_name(round_number: int) -> str:
    """Obtient le nom de la phase selon la manche"""
    if round_number <= global_config.azr.warmup_phase_rounds:
        return "warmup"
    elif round_number <= global_config.azr.prediction_end_round:
        return "optimal"
    else:
        return "post_optimal"


def get_outcome_name(outcome: int) -> str:
    """Obtient le nom d'un résultat"""
    if outcome == global_config.prediction.player_outcome:
        return "Player"
    elif outcome == global_config.prediction.banker_outcome:
        return "Banker"
    else:
        return "Unknown"


# Export des éléments principaux
__all__ = [
    'global_config',
    'GlobalConfig',
    'AZRConfig',
    'SystemConfig',
    'PredictionConfig',
    'InterfaceConfig',
    'CalibrationConfig',
    'get_config',
    'validate_prediction',
    'get_phase_name',
    'get_outcome_name'
]
