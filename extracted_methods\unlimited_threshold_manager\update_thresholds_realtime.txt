# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 92 à 144
# Type: Méthode de la classe UnlimitedThresholdManager

    def update_thresholds_realtime(self, round_number: int, prediction_result: Dict[str, Any], 
                                  actual_outcome: int) -> Dict[str, Any]:
        """
        Met à jour seuils en temps réel pour performance maximale
        
        Args:
            round_number: Numéro de manche
            prediction_result: Résultat prédiction
            actual_outcome: Résultat réel
            
        Returns:
            Dict contenant nouveaux seuils optimisés
        """
        try:
            # Enregistrement performance
            performance_entry = {
                'round': round_number,
                'predicted': prediction_result.get('predicted_outcome', 0),
                'actual': actual_outcome,
                'correct': prediction_result.get('predicted_outcome', 0) == actual_outcome,
                'confidence': prediction_result.get('confidence', 0.5),
                'uncertainty': prediction_result.get('uncertainty', 0.5),
                'current_thresholds': self.current_thresholds.copy(),
                'timestamp': time.time()
            }
            
            self.performance_history.append(performance_entry)
            
            # Optimisation seuils si nécessaire
            optimization_triggered = self._should_optimize_thresholds(round_number)
            
            if optimization_triggered:
                optimization_result = self._optimize_thresholds_unlimited()
                
                return {
                    'success': True,
                    'round': round_number,
                    'optimization_triggered': True,
                    'optimization_result': optimization_result,
                    'new_thresholds': self.current_thresholds.copy(),
                    'performance_improvement': optimization_result.get('improvement', 0.0)
                }
            else:
                return {
                    'success': True,
                    'round': round_number,
                    'optimization_triggered': False,
                    'current_thresholds': self.current_thresholds.copy()
                }
                
        except Exception as e:
            logger.error(f"Erreur mise à jour seuils round {round_number}: {e}")
            return {'success': False, 'error': str(e)}