# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 317 à 333
# Type: Méthode de la classe NewGameHandler

    def _update_game_phase(self):
        """Met à jour phase de jeu selon manche"""
        try:
            if self.game_round <= 30:
                new_phase = 'warmup'
            elif self.game_round <= 60:
                new_phase = 'optimal'
            else:
                new_phase = 'post_optimal'

            if new_phase != self.game_phase:
                self.game_phase = new_phase
                self._update_game_phase_display()
                logger.info(f"Passage phase: {new_phase} (manche {self.game_round})")

        except Exception as e:
            logger.error(f"Erreur mise à jour phase: {e}")