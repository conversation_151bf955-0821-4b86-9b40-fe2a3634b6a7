"""
🎯 AZR PARAMETER MANAGER - CENTRALISATION COMPLÈTE DES PARAMÈTRES
================================================================

Gestionnaire centralisé pour tous les paramètres du modèle AZR.
Évite les valeurs codées en dur et maintient la cohérence mathématique.

Basé sur les principes du modèle "Absolute Zero Reasoner" :
- Unified LLM avec état partagé
- Orchestrateur central
- Paramètres adaptatifs et cohérents
"""

import numpy as np
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
import json
import os

logger = logging.getLogger(__name__)

@dataclass
class AZRParameterConfig:
    """Configuration centralisée des paramètres AZR"""
    
    # 🎯 PARAMÈTRES FONDAMENTAUX (Base mathématique)
    base_confidence_threshold: float = 0.50
    learning_rate: float = 0.05
    adaptation_speed: float = 0.10
    performance_target: float = 0.55
    
    # 🎯 PARAMÈTRES DÉRIVÉS (Calculés automatiquement)
    wait_threshold_min: float = field(init=False)
    wait_threshold_max: float = field(init=False)
    confidence_adjustment_limit: float = field(init=False)
    bias_adjustment_limit: float = field(init=False)
    
    # 🎯 FACTEURS DE COHÉRENCE (Relations mathématiques)
    coherence_factor: float = 0.618  # Golden ratio pour cohérence
    volatility_sensitivity: float = 1.5
    streak_sensitivity: float = 2.0
    
    def __post_init__(self):
        """Calcule les paramètres dérivés pour maintenir la cohérence"""
        # Relations mathématiques cohérentes
        self.wait_threshold_min = self.base_confidence_threshold * 0.3
        self.wait_threshold_max = self.base_confidence_threshold * 1.6
        self.confidence_adjustment_limit = self.learning_rate * 4.0
        self.bias_adjustment_limit = self.learning_rate * 2.0


class AZRParameterManager:
    """
    🎯 GESTIONNAIRE CENTRALISÉ DES PARAMÈTRES AZR
    
    Principe : Le modèle AZR fonctionne comme un chef d'orchestre
    - Coordonne tous les sous-systèmes
    - Maintient la cohérence des paramètres
    - Adapte dynamiquement selon les performances
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialise le gestionnaire avec configuration centralisée"""
        self.config = AZRParameterConfig()
        self.performance_history = []
        self.adaptation_history = []
        
        # État partagé du modèle AZR (Unified LLM)
        self.shared_state = {
            'current_performance': 0.5,
            'volatility_index': 0.0,
            'adaptation_momentum': 0.0,
            'coherence_score': 1.0
        }
        
        # Chargement configuration si fournie
        if config_path and os.path.exists(config_path):
            self.load_config(config_path)
            
        logger.info("🎯 AZR Parameter Manager initialisé avec centralisation complète")
    
    def get_wait_parameters(self) -> Dict[str, float]:
        """Retourne paramètres WAIT cohérents et adaptatifs"""
        current_perf = self.shared_state['current_performance']
        volatility = self.shared_state['volatility_index']
        
        # Calcul adaptatif du seuil WAIT
        performance_gap = self.config.performance_target - current_perf
        adaptation_factor = performance_gap * self.config.adaptation_speed
        
        # Seuil adaptatif avec cohérence mathématique
        adaptive_threshold = self.config.base_confidence_threshold + adaptation_factor
        adaptive_threshold = np.clip(
            adaptive_threshold,
            self.config.wait_threshold_min,
            self.config.wait_threshold_max
        )
        
        # Ajustement volatilité
        volatility_adjustment = volatility * self.config.volatility_sensitivity * 0.1
        final_threshold = adaptive_threshold - volatility_adjustment
        
        return {
            'base_wait_threshold': final_threshold,
            'adaptation_rate': self.config.adaptation_speed,
            'performance_window': max(3, int(10 * self.config.coherence_factor)),
            'min_wait_threshold': self.config.wait_threshold_min,
            'max_wait_threshold': self.config.wait_threshold_max,
            'volatility_factor': self.config.volatility_sensitivity,
            'streak_penalty_factor': self.config.streak_sensitivity
        }
    
    def get_confidence_parameters(self) -> Dict[str, float]:
        """Retourne paramètres de confiance cohérents"""
        momentum = self.shared_state['adaptation_momentum']
        
        return {
            'base_confidence': self.config.base_confidence_threshold,
            'adjustment_limit': self.config.confidence_adjustment_limit,
            'learning_rate': self.config.learning_rate,
            'momentum_factor': momentum,
            'coherence_factor': self.config.coherence_factor
        }
    
    def get_streak_parameters(self) -> Dict[str, Any]:
        """Retourne paramètres de détection de streaks cohérents"""
        return {
            'significant_streak_length': 4,  # Selon vos spécifications
            'anti_streak_factor': self.config.streak_sensitivity,
            'continuation_base_prob': self.config.coherence_factor,
            'length_penalty_rate': self.config.learning_rate * 2,
            'volatility_adjustment': self.config.volatility_sensitivity
        }
    
    def get_pattern_parameters(self) -> Dict[str, float]:
        """Retourne paramètres de patterns cohérents"""
        return {
            'alternation_threshold': 4,  # Selon vos spécifications
            'pattern_strength_factor': self.config.coherence_factor,
            'confidence_boost': self.config.learning_rate * 3,
            'validation_threshold': self.config.base_confidence_threshold * 0.8
        }
    
    def update_performance(self, accuracy: float, confidence: float):
        """Met à jour l'état partagé avec nouvelle performance"""
        self.performance_history.append(accuracy)
        
        # Calcul performance courante (moyenne pondérée)
        if len(self.performance_history) >= 5:
            recent_perf = np.mean(self.performance_history[-5:])
            self.shared_state['current_performance'] = (
                0.7 * recent_perf + 0.3 * self.shared_state['current_performance']
            )
        else:
            self.shared_state['current_performance'] = accuracy
        
        # Calcul volatilité
        if len(self.performance_history) >= 3:
            self.shared_state['volatility_index'] = np.std(self.performance_history[-5:])
        
        # Calcul momentum d'adaptation
        if len(self.performance_history) >= 2:
            trend = self.performance_history[-1] - self.performance_history[-2]
            self.shared_state['adaptation_momentum'] = (
                0.8 * self.shared_state['adaptation_momentum'] + 0.2 * trend
            )
        
        # Score de cohérence (qualité des prédictions)
        confidence_consistency = 1.0 - abs(confidence - accuracy)
        self.shared_state['coherence_score'] = (
            0.9 * self.shared_state['coherence_score'] + 0.1 * confidence_consistency
        )
        
        logger.info(f"🎯 État AZR mis à jour: perf={self.shared_state['current_performance']:.3f}, "
                   f"volatilité={self.shared_state['volatility_index']:.3f}")
    
    def get_all_parameters(self) -> Dict[str, Any]:
        """Retourne tous les paramètres cohérents pour l'ensemble du système"""
        return {
            'wait': self.get_wait_parameters(),
            'confidence': self.get_confidence_parameters(),
            'streaks': self.get_streak_parameters(),
            'patterns': self.get_pattern_parameters(),
            'shared_state': self.shared_state.copy(),
            'config': {
                'base_confidence_threshold': self.config.base_confidence_threshold,
                'learning_rate': self.config.learning_rate,
                'performance_target': self.config.performance_target,
                'coherence_factor': self.config.coherence_factor
            }
        }
    
    def save_config(self, path: str):
        """Sauvegarde la configuration actuelle"""
        config_data = {
            'config': self.config.__dict__,
            'shared_state': self.shared_state,
            'performance_history': self.performance_history[-50:]  # Garde les 50 dernières
        }
        
        with open(path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        logger.info(f"🎯 Configuration AZR sauvegardée: {path}")
    
    def load_config(self, path: str):
        """Charge une configuration sauvegardée"""
        try:
            with open(path, 'r') as f:
                config_data = json.load(f)
            
            # Restaure la configuration
            for key, value in config_data.get('config', {}).items():
                if hasattr(self.config, key):
                    setattr(self.config, key, value)
            
            # Restaure l'état partagé
            self.shared_state.update(config_data.get('shared_state', {}))
            
            # Restaure l'historique
            self.performance_history = config_data.get('performance_history', [])
            
            logger.info(f"🎯 Configuration AZR chargée: {path}")
            
        except Exception as e:
            logger.error(f"Erreur chargement configuration: {e}")


# Instance globale du gestionnaire (Singleton pattern)
_azr_manager = None

def get_azr_manager() -> AZRParameterManager:
    """Retourne l'instance globale du gestionnaire AZR"""
    global _azr_manager
    if _azr_manager is None:
        _azr_manager = AZRParameterManager()
    return _azr_manager
