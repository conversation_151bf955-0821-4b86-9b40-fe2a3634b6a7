# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 332 à 354
# Type: Méthode de la classe MetricsCalculator

    def calculate_comprehensive_metrics(self, y_true: List[int], y_pred: List[int],
                                      y_proba: List[float] = None) -> Dict[str, float]:
        """Calcule métriques complètes"""
        try:
            if not y_true or not y_pred:
                return {}

            metrics = {
                global_config.calculations.metric_names[0]: accuracy_score(y_true, y_pred),
                global_config.calculations.metric_names[1]: precision_score(y_true, y_pred, average=global_config.calculations.score_average_method, zero_division=0),
                global_config.calculations.metric_names[2]: recall_score(y_true, y_pred, average=global_config.calculations.score_average_method, zero_division=0),
                global_config.calculations.metric_names[3]: f1_score(y_true, y_pred, average=global_config.calculations.score_average_method, zero_division=0)
            }

            # Métriques probabilistes si disponibles
            if y_proba:
                metrics.update(self._calculate_probabilistic_metrics(y_true, y_proba))

            return metrics

        except Exception as e:
            logger.error(f"Erreur métriques complètes: {e}")
            return {}