# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 377 à 403
# Type: Méthode de la classe AdaptiveReasoner

    def _calculate_prediction_uncertainty(self, validated_patterns: List[Dict],
                                        confidence_scores: Dict[str, float]) -> float:
        """Calcule incertitude de la prédiction"""
        if not validated_patterns:
            return 0.8  # Incertitude élevée sans patterns

        # Facteurs d'incertitude
        pattern_disagreement = 1.0 - confidence_scores.get('pattern_consensus', 0.0)
        reliability_uncertainty = 1.0 - confidence_scores.get('reliability_factor', 0.5)
        confidence_uncertainty = 1.0 - confidence_scores.get('base_confidence', 0.5)

        # Incertitude basée sur variance des prédictions
        individual_confidences = confidence_scores.get('individual_confidences', [0.5])
        confidence_variance = np.var(individual_confidences) if len(individual_confidences) > 1 else 0.0

        # Utilisation formule unifiée pour incertitude (avec fallback)
        if global_uncertainty_calculator:
            unified_uncertainty = global_uncertainty_calculator.calculate_ensemble_uncertainty(
                model_uncertainties={'azr_patterns': confidence_uncertainty},
                disagreement_factor=pattern_disagreement,
                variance_factor=confidence_variance
            )
        else:
            # Fallback: calcul incertitude simple
            unified_uncertainty = (pattern_disagreement + reliability_uncertainty + confidence_uncertainty) / 3.0

        return unified_uncertainty