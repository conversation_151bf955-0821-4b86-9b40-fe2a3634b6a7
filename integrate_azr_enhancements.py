"""
Script d'Intégration des Améliorations AZR
==========================================

Ce script applique les améliorations AZR basées sur les recherches
scientifiques à notre système existant.

Usage:
    python integrate_azr_enhancements.py

Référence: recherches_azr_entrainement_hyperparametres.txt
"""

import logging
import sys
import traceback
from typing import Dict, Any

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Fonction principale d'intégration"""
    logger.info("🚀 Démarrage intégration améliorations AZR selon recherches scientifiques")
    
    try:
        # 1. Import des modules nécessaires
        logger.info("📦 Import des modules...")
        from azr_core import AZRSystem
        from azr_enhanced_adapter import AZREnhancedAdapter
        
        # 2. Initialisation système AZR existant
        logger.info("🔧 Initialisation système AZR existant...")
        azr_system = AZRSystem(use_strategic_wait=True)
        
        # 3. Création adaptateur amélioré
        logger.info("🧠 Création adaptateur AZR amélioré...")
        enhanced_adapter = AZREnhancedAdapter(azr_system)
        
        # 4. Application des améliorations
        logger.info("⚡ Application des améliorations selon recherches...")
        enhanced_adapter.adapt_existing_system()
        
        # 5. Vérification de l'intégration
        logger.info("✅ Vérification de l'intégration...")
        integration_report = verify_integration(azr_system, enhanced_adapter)
        
        # 6. Affichage du rapport
        display_integration_report(integration_report)
        
        # 7. Test de fonctionnement
        logger.info("🧪 Test de fonctionnement du système amélioré...")
        test_enhanced_system(azr_system, enhanced_adapter)
        
        logger.info("🎉 Intégration des améliorations AZR terminée avec succès!")
        
        return azr_system, enhanced_adapter
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'intégration: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return None, None

def verify_integration(azr_system, enhanced_adapter) -> Dict[str, Any]:
    """Vérifie que l'intégration s'est bien déroulée"""
    logger.info("🔍 Vérification de l'intégration...")
    
    verification_results = {
        'hyperparameters_updated': False,
        'reward_system_enhanced': False,
        'trr_plus_plus_integrated': False,
        'curriculum_activated': False,
        'metrics_enhanced': False,
        'monitoring_configured': False,
        'errors': []
    }
    
    try:
        # Vérification hyperparamètres
        if hasattr(azr_system, 'learning_rate_proposer') and azr_system.learning_rate_proposer == 0.0002:
            verification_results['hyperparameters_updated'] = True
        else:
            verification_results['errors'].append("Hyperparamètres non mis à jour")
            
        # Vérification système de récompenses
        if hasattr(azr_system, 'reward_optimal_task') and azr_system.reward_optimal_task == 1.0:
            verification_results['reward_system_enhanced'] = True
        else:
            verification_results['errors'].append("Système de récompenses non amélioré")
            
        # Vérification TRR++
        if hasattr(enhanced_adapter, 'trr_plus_plus'):
            verification_results['trr_plus_plus_integrated'] = True
        else:
            verification_results['errors'].append("TRR++ non intégré")
            
        # Vérification curriculum
        if hasattr(azr_system, 'adaptive_curriculum'):
            verification_results['curriculum_activated'] = True
        else:
            verification_results['errors'].append("Curriculum adaptatif non activé")
            
        # Vérification métriques
        if hasattr(azr_system, 'enhanced_metrics'):
            verification_results['metrics_enhanced'] = True
        else:
            verification_results['errors'].append("Métriques non améliorées")
            
        # Vérification monitoring
        if hasattr(azr_system, 'monitoring_alerts'):
            verification_results['monitoring_configured'] = True
        else:
            verification_results['errors'].append("Monitoring non configuré")
            
    except Exception as e:
        verification_results['errors'].append(f"Erreur vérification: {e}")
        
    return verification_results

def display_integration_report(report: Dict[str, Any]):
    """Affiche le rapport d'intégration"""
    logger.info("📊 RAPPORT D'INTÉGRATION AZR AMÉLIORÉ")
    logger.info("=" * 50)
    
    # Statut des composants
    components = [
        ('Hyperparamètres mis à jour', report['hyperparameters_updated']),
        ('Système de récompenses amélioré', report['reward_system_enhanced']),
        ('Task-Relative REINFORCE++ intégré', report['trr_plus_plus_integrated']),
        ('Curriculum adaptatif activé', report['curriculum_activated']),
        ('Métriques améliorées', report['metrics_enhanced']),
        ('Monitoring configuré', report['monitoring_configured'])
    ]
    
    for component, status in components:
        status_icon = "✅" if status else "❌"
        logger.info(f"{status_icon} {component}")
        
    # Erreurs
    if report['errors']:
        logger.warning("⚠️ ERREURS DÉTECTÉES:")
        for error in report['errors']:
            logger.warning(f"  - {error}")
    else:
        logger.info("🎉 Aucune erreur détectée!")
        
    # Calcul score global
    total_components = len(components)
    successful_components = sum(1 for _, status in components if status)
    success_rate = (successful_components / total_components) * 100
    
    logger.info(f"📈 Taux de réussite intégration: {success_rate:.1f}%")

def test_enhanced_system(azr_system, enhanced_adapter):
    """Teste le système AZR amélioré"""
    logger.info("🧪 Test du système AZR amélioré...")
    
    try:
        # Test 1: Itération self-play
        logger.info("Test 1: Itération self-play...")
        context = {
            'recent_results': [0, 1, 0, 1, 1],
            'current_round': 6,
            'actual_outcome': 1,
            'predicted_outcome': 1,
            'confidence': 0.7
        }
        
        iteration_result = azr_system.self_play_iteration(context)
        if iteration_result and 'learning_occurred' in iteration_result:
            logger.info("✅ Test self-play réussi")
        else:
            logger.warning("⚠️ Test self-play échoué")
            
        # Test 2: Prédiction Baccarat
        logger.info("Test 2: Prédiction Baccarat...")
        prediction = azr_system.get_baccarat_prediction(context)
        if prediction and 'predicted_outcome' in prediction:
            logger.info(f"✅ Test prédiction réussi: {prediction['predicted_outcome']}")
        else:
            logger.warning("⚠️ Test prédiction échoué")
            
        # Test 3: Métriques améliorées
        logger.info("Test 3: Métriques améliorées...")
        if hasattr(azr_system, 'get_enhanced_status'):
            status = azr_system.get_enhanced_status()
            if 'enhanced_metrics' in status:
                logger.info("✅ Test métriques améliorées réussi")
            else:
                logger.warning("⚠️ Test métriques améliorées échoué")
        else:
            logger.warning("⚠️ Méthode get_enhanced_status non disponible")
            
        # Test 4: Curriculum adaptatif
        logger.info("Test 4: Curriculum adaptatif...")
        if hasattr(azr_system, 'adaptive_curriculum'):
            difficulty = azr_system.adaptive_curriculum.get_current_difficulty()
            logger.info(f"✅ Test curriculum réussi - Difficulté: {difficulty:.3f}")
        else:
            logger.warning("⚠️ Test curriculum échoué")
            
        # Test 5: Mise à jour phase d'entraînement
        logger.info("Test 5: Mise à jour phase d'entraînement...")
        enhanced_adapter.update_training_phase()
        phase = enhanced_adapter.training_phase
        logger.info(f"✅ Test phase d'entraînement réussi - Phase: {phase}")
        
        # Test 6: Rapport d'adaptation
        logger.info("Test 6: Rapport d'adaptation...")
        adaptation_report = enhanced_adapter.get_adaptation_report()
        if adaptation_report and 'hyperparameters' in adaptation_report:
            logger.info("✅ Test rapport d'adaptation réussi")
        else:
            logger.warning("⚠️ Test rapport d'adaptation échoué")
            
        logger.info("🎉 Tous les tests terminés!")
        
    except Exception as e:
        logger.error(f"❌ Erreur lors des tests: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")

def demonstrate_enhancements(azr_system, enhanced_adapter):
    """Démontre les améliorations apportées"""
    logger.info("🎭 DÉMONSTRATION DES AMÉLIORATIONS AZR")
    logger.info("=" * 50)
    
    try:
        # 1. Hyperparamètres optimisés
        logger.info("1. 📊 HYPERPARAMÈTRES OPTIMISÉS SELON RECHERCHES:")
        logger.info(f"   Learning Rate Proposer: {enhanced_adapter.hyperparams.learning_rate_proposer}")
        logger.info(f"   Learning Rate Solver: {enhanced_adapter.hyperparams.learning_rate_solver}")
        logger.info(f"   Batch Size Proposer: {enhanced_adapter.hyperparams.batch_size_proposer}")
        logger.info(f"   Batch Size Solver: {enhanced_adapter.hyperparams.batch_size_solver}")
        
        # 2. Task-Relative REINFORCE++
        logger.info("2. 🧠 TASK-RELATIVE REINFORCE++ INTÉGRÉ:")
        logger.info(f"   Baselines par type de tâche: {len(enhanced_adapter.trr_plus_plus.baselines_by_task_type)}")
        logger.info(f"   Historique avantages: {len(enhanced_adapter.trr_plus_plus.advantage_history)}")
        
        # 3. Curriculum Learning Adaptatif
        logger.info("3. 📚 CURRICULUM LEARNING ADAPTATIF:")
        logger.info(f"   Difficulté actuelle: {enhanced_adapter.curriculum.get_current_difficulty():.3f}")
        logger.info(f"   Tendance difficulté: {enhanced_adapter.curriculum.get_difficulty_trend()}")
        
        # 4. Métriques étendues
        logger.info("4. 📈 MÉTRIQUES ÉTENDUES SELON RECHERCHES:")
        metrics_report = enhanced_adapter.metrics.get_comprehensive_report()
        logger.info(f"   Métriques primaires: {len(metrics_report['primary_metrics'])}")
        logger.info(f"   Métriques secondaires: {len(metrics_report['secondary_metrics'])}")
        logger.info(f"   Métriques robustesse: {len(metrics_report['robustness_metrics'])}")
        
        # 5. Phases d'entraînement
        logger.info("5. 🎯 PHASES D'ENTRAÎNEMENT SELON RECHERCHES:")
        logger.info(f"   Phase actuelle: {enhanced_adapter.training_phase}")
        logger.info(f"   Step actuel: {enhanced_adapter.training_step}")
        
        # 6. Monitoring avancé
        logger.info("6. 🔍 MONITORING AVANCÉ:")
        if hasattr(azr_system, 'monitoring_alerts'):
            alerts = azr_system.monitoring_alerts
            active_alerts = [k for k, v in alerts.items() if v]
            logger.info(f"   Alertes actives: {len(active_alerts)}")
            if active_alerts:
                for alert in active_alerts:
                    logger.info(f"     - {alert}")
        
        logger.info("🎉 Démonstration terminée!")
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de la démonstration: {e}")

if __name__ == "__main__":
    logger.info("🚀 Lancement du script d'intégration AZR amélioré")
    
    # Intégration principale
    azr_system, enhanced_adapter = main()
    
    if azr_system and enhanced_adapter:
        # Démonstration des améliorations
        demonstrate_enhancements(azr_system, enhanced_adapter)
        
        # Sauvegarde de l'état
        logger.info("💾 Sauvegarde de l'état du système amélioré...")
        try:
            adaptation_report = enhanced_adapter.get_adaptation_report()
            with open('azr_enhanced_state.json', 'w', encoding='utf-8') as f:
                import json
                json.dump(adaptation_report, f, indent=2, ensure_ascii=False)
            logger.info("✅ État sauvegardé dans azr_enhanced_state.json")
        except Exception as e:
            logger.error(f"❌ Erreur sauvegarde: {e}")
            
        logger.info("🎉 Script d'intégration terminé avec succès!")
        logger.info("📋 Le système AZR est maintenant amélioré selon les recherches scientifiques")
        
    else:
        logger.error("❌ Échec de l'intégration - Système non amélioré")
        sys.exit(1)
