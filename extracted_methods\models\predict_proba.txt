# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 63 à 129
# Type: Méthode de la classe AZRBaccaratPredictor

    def predict_proba(self, game_history: List[int], round_number: int = None) -> Dict[str, Any]:
        """
        Génère prédiction probabiliste pour prochaine manche

        Args:
            game_history: Historique du jeu (0=Player, 1=Banker)
            round_number: Numéro de manche optionnel

        Returns:
            Dict contenant prédictions et métriques
        """
        try:
            prediction_start = time.time()

            # Raisonnement AZR sur prochaine issue
            reasoning_result = self.adaptive_reasoner.reason_next_outcome(
                game_history, round_number
            )

            # Extraction prédictions
            prediction = reasoning_result['prediction']
            player_prob = prediction['player_probability']
            banker_prob = prediction['banker_probability']
            predicted_outcome = prediction['predicted_outcome']

            # Métriques de confiance
            confidence = reasoning_result['confidence']
            uncertainty = reasoning_result['uncertainty']
            recommendation = reasoning_result['recommendation']

            prediction_time = time.time() - prediction_start

            # Résultat formaté
            result = {
                # Prédictions principales
                'player_probability': player_prob,
                'banker_probability': banker_prob,
                'predicted_outcome': predicted_outcome,
                'prediction_strength': prediction.get('prediction_strength', 0.0),

                # Métriques de confiance
                'confidence': confidence,
                'uncertainty': uncertainty,
                'recommendation': recommendation,

                # Détails raisonnement
                'reasoning_details': reasoning_result['reasoning_details'],
                'pattern_breakdown': reasoning_result['pattern_breakdown'],
                'meta_info': reasoning_result['meta_info'],

                # Métriques performance
                'prediction_time': prediction_time,
                'model_type': 'AZR',
                'session_active': self.is_active
            }

            # Mise à jour historique
            self._update_prediction_history(result, round_number)

            logger.debug(f"Prédiction AZR - Player: {player_prob:.3f}, "
                        f"Banker: {banker_prob:.3f}, Confiance: {confidence:.3f}")

            return result

        except Exception as e:
            logger.error(f"Erreur prédiction AZR: {e}")
            return self._create_fallback_prediction()