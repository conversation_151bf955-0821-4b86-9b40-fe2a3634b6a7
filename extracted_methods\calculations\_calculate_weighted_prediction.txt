# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 422 à 457
# Type: Méthode de la classe PredictionCalculator

    def _calculate_weighted_prediction(self, predictions: Dict[str, Dict[str, float]],
                                     weights: Dict[str, float]) -> Dict[str, float]:
        """Calcule prédiction pondérée"""
        try:
            calc_config = global_config.calculations
            default_prob = calc_config.max_prob_center

            total_weight = sum(weights.values())
            if total_weight == global_config.calculations.min_total_weight:
                return {'player': default_prob, 'banker': default_prob}

            weighted_player = sum(
                pred.get('player', default_prob) * weights.get(model, 0)
                for model, pred in predictions.items()
            ) / total_weight

            weighted_banker = sum(
                pred.get('banker', default_prob) * weights.get(model, 0)
                for model, pred in predictions.items()
            ) / total_weight

            # Normalisation pour assurer player + banker = 1
            total = weighted_player + weighted_banker
            if total > global_config.calculations.min_normalization_total:
                weighted_player /= total
                weighted_banker /= total

            return {
                'player': weighted_player,
                'banker': weighted_banker
            }

        except Exception as e:
            logger.error(f"Erreur prédiction pondérée: {e}")
            default_prob = global_config.calculations.max_prob_center
            return {'player': default_prob, 'banker': default_prob}