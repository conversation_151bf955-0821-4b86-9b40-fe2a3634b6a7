# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 232 à 266
# Type: Méthode de la classe AdaptiveReasoner

    def _validate_and_score_patterns(self, patterns: List[Dict],
                                   recent_history: List[int]) -> List[Dict]:
        """Valide et score les patterns"""
        validated_patterns = []

        for pattern in patterns:
            try:
                # Validation du pattern
                validation_result = self.pattern_validator.validate_pattern(
                    pattern, recent_history
                )

                # Ajout informations de validation
                pattern_with_validation = pattern.copy()
                pattern_with_validation.update({
                    'validation_result': validation_result,
                    'adjusted_confidence': validation_result.get('confidence_adjusted', pattern.get('confidence', 0.5)),
                    'reliability': validation_result.get('pattern_reliability', 0.5),
                    'reward': validation_result.get('reward', 0.0)
                })

                # Filtrage par confiance minimale
                if pattern_with_validation['adjusted_confidence'] > self.confidence_threshold:
                    validated_patterns.append(pattern_with_validation)

            except Exception as e:
                logger.warning(f"Erreur validation pattern {pattern.get('type', 'unknown')}: {e}")
                continue

        # Tri par confiance ajustée
        validated_patterns.sort(key=lambda x: x['adjusted_confidence'], reverse=True)

        # Limitation nombre de patterns
        max_patterns = global_config.azr.max_active_patterns
        return validated_patterns[:max_patterns]