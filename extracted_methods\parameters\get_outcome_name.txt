# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\parameters.py
# Lignes: 339 à 346
# Type: Méthode

def get_outcome_name(outcome: int) -> str:
    """Obtient le nom d'un résultat"""
    if outcome == global_config.prediction.player_outcome:
        return "Player"
    elif outcome == global_config.prediction.banker_outcome:
        return "Banker"
    else:
        return "Unknown"