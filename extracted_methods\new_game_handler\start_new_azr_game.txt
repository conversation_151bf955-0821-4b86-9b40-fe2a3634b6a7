# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 56 à 93
# Type: Méthode de la classe NewGameHandler

    def start_new_azr_game(self) -> Dict[str, Any]:
        """
        Lance nouvelle partie AZR avec calibration complète

        Returns:
            Dict contenant résultats démarrage
        """
        try:
            if self.calibration_in_progress:
                return {
                    'success': False,
                    'message': 'Calibration déjà en cours'
                }

            logger.info("🎯 Démarrage nouvelle partie AZR (60 manches)")

            # Mise à jour interface
            self._update_interface_status("🔄 Calibration en cours...")

            # Lancement calibration en thread séparé
            calibration_thread = threading.Thread(
                target=self._perform_game_calibration,
                daemon=True
            )
            calibration_thread.start()

            return {
                'success': True,
                'message': 'Calibration nouvelle partie démarrée',
                'status': 'calibrating'
            }

        except Exception as e:
            logger.error(f"Erreur démarrage nouvelle partie: {e}")
            return {
                'success': False,
                'message': f'Erreur: {e}'
            }