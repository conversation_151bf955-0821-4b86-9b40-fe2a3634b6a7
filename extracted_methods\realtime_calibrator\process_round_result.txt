# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 143 à 205
# Type: Méthode de la classe RealtimeCalibrator

    def process_round_result(self, round_number: int, prediction_result: Dict[str, Any],
                           actual_outcome: int) -> Dict[str, Any]:
        """
        Traite résultat d'une manche pour calibration temps réel

        Args:
            round_number: Numéro de manche
            prediction_result: Résultat prédiction AZR
            actual_outcome: Résultat réel (0=Player, 1=Banker)

        Returns:
            Dict contenant résultats calibration
        """
        try:
            self.current_round = round_number

            # Mise à jour phase
            self._update_current_phase()

            # Enregistrement performance
            performance_entry = {
                'round': round_number,
                'predicted': prediction_result.get('predicted_outcome', 0),
                'actual': actual_outcome,
                'correct': prediction_result.get('predicted_outcome', 0) == actual_outcome,
                'confidence': prediction_result.get('confidence', 0.5),
                'timestamp': time.time(),
                'phase': self.current_phase,
                'parameters': self.calibrated_parameters.copy()
            }

            self.performance_history.append(performance_entry)

            # Mise à jour métriques temps réel
            self._update_realtime_metrics()

            # Déclenchement calibration si nécessaire
            calibration_triggered = self._should_trigger_calibration()

            if calibration_triggered:
                calibration_result = self._trigger_immediate_calibration()
                return {
                    'success': True,
                    'round': round_number,
                    'phase': self.current_phase,
                    'calibration_triggered': True,
                    'calibration_result': calibration_result,
                    'updated_parameters': self.calibrated_parameters.copy(),
                    'realtime_metrics': self._get_realtime_metrics()
                }
            else:
                return {
                    'success': True,
                    'round': round_number,
                    'phase': self.current_phase,
                    'calibration_triggered': False,
                    'current_parameters': self.calibrated_parameters.copy(),
                    'realtime_metrics': self._get_realtime_metrics()
                }

        except Exception as e:
            logger.error(f"Erreur traitement round {round_number}: {e}")
            return {'success': False, 'error': str(e)}