# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 32 à 48
# Type: Méthode de la classe BaccaratPatternValidator

    def __init__(self):
        """Initialisation du validateur de patterns"""
        self.validation_history = []
        self.pattern_performance = defaultdict(list)
        self.real_time_results = []

        # Paramètres validation
        self.min_validation_samples = global_config.azr.min_validation_samples
        self.validation_window = global_config.azr.validation_window
        self.reward_decay = global_config.azr.reward_decay

        # Métriques temps réel
        self.current_session_accuracy = 0.0
        self.total_validations = 0
        self.successful_validations = 0

        logger.info("BaccaratPatternValidator initialisé avec paradigme AZR")