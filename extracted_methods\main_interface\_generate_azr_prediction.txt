# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 399 à 425
# Type: Méthode de la classe BaccaratPredictorApp

    def _generate_azr_prediction(self):
        """Génère prédiction via système AZR 100% opérationnel"""
        try:
            # Contexte pour AZR
            context = {
                'recent_results': self.results.copy(),
                'current_round': self.current_round,
                'total_rounds': len(self.results)
            }

            # Génération prédiction AZR 100% opérationnel
            prediction = self.azr_system.get_baccarat_prediction(context)

            # Ajout informations AZR dans prédiction
            if 'azr_info' in prediction:
                azr_info = prediction['azr_info']
                self.azr_info_label.config(
                    text=f"AZR: Itérations={azr_info['training_iterations']}, "
                         f"Biais={azr_info['learned_bias']:.3f}, "
                         f"Seuil={azr_info['learned_threshold']:.3f}"
                )

            return prediction

        except Exception as e:
            logger.error(f"Erreur génération prédiction AZR: {e}")
            return self._generate_basic_baccarat_prediction()