# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 652 à 663
# Type: Méthode de la classe EnsembleModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def predict_proba(self, X: np.ndarray) -> np.ndarray:
        """Interface compatible pour ensemble"""
        # Conversion données
        if len(X.shape) > 1:
            sequence = X[-1].astype(int).tolist()
        else:
            sequence = X.astype(int).tolist()

        prediction, confidence = self.predict(sequence)

        # Format proba [Player, Banker]
        return np.array([prediction, 1.0 - prediction])