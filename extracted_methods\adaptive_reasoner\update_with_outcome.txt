# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 524 à 557
# Type: Méthode de la classe AdaptiveReasoner

    def update_with_outcome(self, predicted_outcome: int, actual_outcome: int,
                          patterns_used: List[Dict]):
        """Met à jour avec résultat réel"""
        self.total_predictions += 1

        if predicted_outcome == actual_outcome:
            self.successful_predictions += 1

            # Mise à jour patterns réussis
            for pattern in patterns_used:
                pattern['session_created'] = self.reasoning_sessions
                self.successful_patterns.append(pattern)
                self.pattern_proposer.update_pattern_performance(pattern, True)
        else:
            # Mise à jour patterns échoués
            for pattern in patterns_used:
                self.failed_patterns.append(pattern)
                self.pattern_proposer.update_pattern_performance(pattern, False)

        # Nettoyage patterns anciens
        self._cleanup_old_patterns()

        # ═══════════════════════════════════════════════════════════════════
        # OPTIMISATION MÉTA-PARAMÈTRES EN TEMPS RÉEL
        # ═══════════════════════════════════════════════════════════════════

        # Enregistrement performance pour optimisation
        current_accuracy = self._calculate_session_accuracy()
        self._record_parameter_performance(current_accuracy)

        # Optimisation périodique des méta-paramètres
        if self.total_predictions - self.last_optimization_round >= self.optimization_frequency:
            self._optimize_meta_parameters()
            self.last_optimization_round = self.total_predictions