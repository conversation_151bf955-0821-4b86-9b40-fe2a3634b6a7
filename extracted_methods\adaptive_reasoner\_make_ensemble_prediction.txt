# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 322 à 375
# Type: Méthode de la classe AdaptiveReasoner

    def _make_ensemble_prediction(self, validated_patterns: List[Dict],
                                confidence_scores: Dict[str, float]) -> Dict[str, Any]:
        """Génère prédiction d'ensemble"""
        if not validated_patterns:
            return {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,  # Par défaut Player
                'prediction_strength': 0.0
            }

        # Collecte prédictions avec pondération
        weighted_predictions = []
        total_weight = 0.0

        for pattern in validated_patterns:
            validation = pattern.get('validation_result', {})
            prediction_details = validation.get('prediction_details', {})
            predicted_outcome = prediction_details.get('predicted_outcome')

            if predicted_outcome is not None:
                weight = pattern['adjusted_confidence'] * pattern.get('reliability', 0.5)
                weighted_predictions.append((predicted_outcome, weight))
                total_weight += weight

        if not weighted_predictions:
            return {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,
                'prediction_strength': 0.0
            }

        # Calcul probabilités pondérées
        player_weight = sum(weight for outcome, weight in weighted_predictions if outcome == 0)
        banker_weight = sum(weight for outcome, weight in weighted_predictions if outcome == 1)

        if total_weight > 0:
            player_prob = player_weight / total_weight
            banker_prob = banker_weight / total_weight
        else:
            player_prob = banker_prob = 0.5

        # Prédiction finale
        predicted_outcome = 0 if player_prob > banker_prob else 1
        prediction_strength = abs(player_prob - banker_prob)

        return {
            'player_probability': player_prob,
            'banker_probability': banker_prob,
            'predicted_outcome': predicted_outcome,
            'prediction_strength': prediction_strength,
            'total_patterns': len(weighted_predictions)
        }