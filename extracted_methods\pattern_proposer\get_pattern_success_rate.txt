# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 776 à 784
# Type: Méthode de la classe BaccaratPatternProposer

    def get_pattern_success_rate(self, pattern: Dict) -> float:
        """Obtient taux de succès d'un pattern"""
        pattern_key = self._get_pattern_key(pattern)

        successes = self.successful_patterns.get(pattern_key, 0)
        failures = self.failed_patterns.get(pattern_key, 0)
        total = successes + failures

        return successes / total if total > 0 else 0.5