# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 331 à 341
# Type: Méthode de la classe LSTMModel

    def _prepare_sequences(self, sequence):
        """Prépare séquences pour LSTM"""
        if len(sequence) < self.sequence_length:
            # Pad avec moyenne historique si séquence trop courte
            pad_value = 0.5 if len(sequence) == 0 else sum(sequence) / len(sequence)
            padded = [pad_value] * (self.sequence_length - len(sequence)) + list(sequence)
            return np.array([padded]).reshape(1, self.sequence_length, 1)

        # Utilise les dernières sequence_length valeurs
        recent_sequence = sequence[-self.sequence_length:]
        return np.array([recent_sequence]).reshape(1, self.sequence_length, 1)