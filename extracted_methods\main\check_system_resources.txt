# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main.py
# Lignes: 68 à 92
# Type: Méthode

def check_system_resources():
    """Vérifie les ressources système"""
    try:
        import psutil

        # Vérification RAM
        memory = psutil.virtual_memory()
        ram_gb = memory.total / (1024**3)

        # Vérification CPU
        cpu_count = psutil.cpu_count()

        logger.info(f"Ressources système: RAM {ram_gb:.1f}GB, CPU {cpu_count} cœurs")

        if ram_gb < 8:
            logger.warning(f"RAM faible: {ram_gb:.1f}GB (recommandé: 16GB+)")

        if cpu_count < 4:
            logger.warning(f"CPU limité: {cpu_count} cœurs (recommandé: 8+)")

        return True

    except Exception as e:
        logger.error(f"Erreur vérification ressources: {e}")
        return False