# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 332 à 341
# Type: Méthode de la classe AZRSolver
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, environment: AZREnvironment):
        self.environment = environment
        self.solution_history = []
        self.performance_metrics = {
            'solutions_attempted': 0,
            'solutions_successful': 0,
            'avg_confidence': 0.5,
            'avg_quality_score': 0.5
        }
        self.learned_patterns = {}