# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 34 à 54
# Type: Méthode de la classe NewGameHandler

    def __init__(self, main_interface):
        """
        Initialisation gestionnaire nouvelle partie

        Args:
            main_interface: Référence vers interface principale
        """
        self.main_interface = main_interface

        # Calibrateurs AZR
        self.game_calibrator = GameInitializationCalibrator()
        self.realtime_calibrator = RealtimeCalibrator()
        self.threshold_manager = UnlimitedThresholdManager()

        # État partie
        self.game_active = False
        self.game_round = 0
        self.game_phase = 'waiting'  # waiting, warmup, optimal, post_optimal
        self.calibration_in_progress = False

        logger.info("NewGameHandler initialisé")