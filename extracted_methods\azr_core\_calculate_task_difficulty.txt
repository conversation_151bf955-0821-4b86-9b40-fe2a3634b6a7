# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 299 à 319
# Type: Méthode de la classe AZRProposer

    def _calculate_task_difficulty(self, sequence: List[int]) -> float:
        """Calcule difficulté d'une tâche basée sur la séquence"""
        if not sequence:
            return 0.5

        # Analyse entropie
        player_count = sum(1 for x in sequence if x == 0)
        player_freq = player_count / len(sequence)

        # Plus proche de 50/50, plus difficile
        entropy = -player_freq * math.log2(player_freq + 1e-10) - \
                 (1 - player_freq) * math.log2(1 - player_freq + 1e-10)

        # Normalise entropie (max = 1.0)
        normalized_entropy = entropy / 1.0

        # Difficulté basée sur entropie et longueur
        base_difficulty = normalized_entropy
        length_factor = min(1.0, len(sequence) / 10.0)

        return min(0.9, base_difficulty * 0.7 + length_factor * 0.3)