# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 161 à 198
# Type: Méthode de la classe BaccaratPredictorApp

    def predict_next(self):
        """Génère prédiction pour la prochaine manche via AZR authentique"""
        try:
            self.current_round += 1

            # Génération prédiction AZR authentique
            prediction = self._generate_azr_prediction()

            if prediction:
                # Affichage prédiction
                outcome_text = "👤 PLAYER" if prediction['predicted_outcome'] == 0 else "🏦 BANKER"
                confidence = prediction['confidence']
                method = prediction.get('method', 'azr_authentic')

                pred_text = f"Manche {self.current_round}: {outcome_text}\n"
                pred_text += f"Confiance: {confidence:.1%}\n"
                pred_text += f"Méthode: {method}"

                self.prediction_label.config(text=pred_text)

                # Stockage prédiction
                self.predictions.append(prediction)

                # Affichage détails
                self._show_prediction_details(prediction)

                # Affichage statut AZR
                self._show_azr_status()

                # Activation interface après première prédiction
                self._activate_game_interface()

                # Log
                logger.info(f"Prédiction manche {self.current_round}: {outcome_text} (confiance: {confidence:.1%})")

        except Exception as e:
            logger.error(f"Erreur génération prédiction: {e}")
            messagebox.showerror("Erreur", f"Erreur génération prédiction: {e}")