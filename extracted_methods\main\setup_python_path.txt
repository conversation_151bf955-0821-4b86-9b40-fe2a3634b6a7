# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main.py
# Lignes: 26 à 38
# Type: Méthode

def setup_python_path():
    """Configure le chemin Python pour les imports"""
    try:
        # Ajout du répertoire courant au path (tous les fichiers sont maintenant ici)
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))

        logger.info("Chemins Python configurés avec succès")
        return True

    except Exception as e:
        logger.error(f"Erreur configuration chemins Python: {e}")
        return False