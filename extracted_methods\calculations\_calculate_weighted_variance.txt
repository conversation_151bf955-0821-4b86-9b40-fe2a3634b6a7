# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 288 à 308
# Type: Méthode de la classe ConfidenceCalculator

    def _calculate_weighted_variance(self, predictions: Dict[str, Dict[str, float]],
                                   weights: Dict[str, float]) -> float:
        """Calcule variance pondérée des prédictions"""
        try:
            # Prédiction moyenne pondérée
            avg_player = sum(pred.get('player', self.max_prob_center) * weights.get(model, 0)
                           for model, pred in predictions.items())
            avg_banker = sum(pred.get('banker', self.max_prob_center) * weights.get(model, 0)
                           for model, pred in predictions.items())

            # Variance pondérée
            var_player = sum(weights.get(model, 0) * (pred.get('player', self.max_prob_center) - avg_player)**2
                           for model, pred in predictions.items())
            var_banker = sum(weights.get(model, 0) * (pred.get('banker', self.max_prob_center) - avg_banker)**2
                           for model, pred in predictions.items())

            return (var_player + var_banker) / global_config.calculations.variance_divisor

        except Exception as e:
            logger.error(f"Erreur variance pondérée: {e}")
            return global_config.calculations.default_confidence