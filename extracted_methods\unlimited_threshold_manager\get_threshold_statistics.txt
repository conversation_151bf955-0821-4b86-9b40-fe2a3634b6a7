# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 150 à 164
# Type: Méthode de la classe UnlimitedThreshold<PERSON>anager

    def get_threshold_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques des seuils"""
        return {
            'current_thresholds': self.current_thresholds.copy(),
            'adaptation_ranges': self.adaptation_ranges.copy(),
            'unlimited_mode': self.unlimited_mode,
            'optimization_count': self.optimization_count,
            'last_optimization_round': self.last_optimization_round,
            'best_performance_achieved': self.best_performance_achieved,
            'performance_plateau_count': self.performance_plateau_count,
            'performance_history_size': len(self.performance_history),
            'optimization_history_size': len(self.optimization_history),
            'aggressive_optimization': self.aggressive_optimization,
            'no_performance_limits': self.no_performance_limits
        }