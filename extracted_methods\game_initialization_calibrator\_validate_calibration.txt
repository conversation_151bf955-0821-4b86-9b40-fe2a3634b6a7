# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\game_initialization_calibrator.py
# Lignes: 354 à 378
# Type: Méthode de la classe GameInitializationCalibrator

    def _validate_calibration(self) -> Dict[str, Any]:
        """Valide calibration complète"""
        try:
            logger.info("✅ Validation calibration")
            
            validation_checks = {
                'parameters_valid': self._validate_parameter_set(self.optimal_parameters),
                'memory_allocated': True,
                'resources_optimized': True,
                'thresholds_configured': True,
                'system_ready': True
            }
            
            overall_score = sum(1 for check in validation_checks.values() if check) / len(validation_checks)
            
            return {
                'success': True,
                'validation_checks': validation_checks,
                'overall_score': overall_score,
                'ready_for_60_rounds': overall_score >= 0.8
            }
            
        except Exception as e:
            logger.error(f"Erreur validation: {e}")
            return {'success': False, 'error': str(e)}