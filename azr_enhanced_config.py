"""
Configuration AZR Améliorée selon Recherches Scientifiques
==========================================================

Ce fichier contient les hyperparamètres et configurations optimisées
basées sur les recherches approfondies sur les modèles AZR.

Référence: recherches_azr_entrainement_hyperparametres.txt
"""

from dataclasses import dataclass
from typing import Dict, Any
import logging

logger = logging.getLogger(__name__)

@dataclass
class AZREnhancedConfig:
    """Configuration AZR optimisée selon recherches scientifiques"""
    
    # ═══════════════════════════════════════════════════════════════════
    # HYPERPARAMÈTRES FONDAMENTAUX SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Learning rates différenciés Proposer/Solver
    learning_rate_proposer: float = 0.0002      # 1e-4 à 5e-4 optimal
    learning_rate_solver: float = 0.0005        # 3e-4 à 1e-3 optimal
    learning_rate_baseline: float = 0.001       # 1e-3 pour baseline
    
    # Schedules d'apprentissage
    warmup_steps_proposer: int = 2000           # 1000-5000 steps
    warmup_steps_solver: int = 1000             # 500-2000 steps
    lr_min_proposer: float = 1e-6               # Minimum LR proposer
    lr_min_solver: float = 5e-6                 # Minimum LR solver
    
    # Batch sizes optimisés
    batch_size_proposer: int = 32               # 16-64 tâches par batch
    batch_size_solver: int = 64                 # 32-128 solutions par batch
    gradient_accumulation_steps: int = 4        # 2-8 steps selon recherches
    
    # Buffers selon recommandations
    task_buffer_size: int = 10000               # 10,000-50,000 tâches
    experience_replay_size: int = 2000          # 1,000-5,000 épisodes
    curriculum_buffer_size: int = 1000          # 500-2,000 tâches par niveau
    
    # ═══════════════════════════════════════════════════════════════════
    # ALGORITHME TASK-RELATIVE REINFORCE++ SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Optimisation et stabilité
    gradient_clipping: float = 1.0              # 0.5-2.0 selon recherches
    baseline_update_frequency: int = 25         # Chaque 10-50 steps
    advantage_normalization: bool = True        # Normalisation avantages
    entropy_regularization: float = 0.05        # 0.01-0.1 régularisation
    
    # Exploration vs Exploitation
    epsilon_initial: float = 0.85               # 0.8-0.9 exploration initiale
    epsilon_final: float = 0.08                 # 0.05-0.1 exploration finale
    epsilon_decay: float = 0.997                # 0.995-0.999 decay rate
    temperature_sampling: float = 1.0           # 0.7-1.2 température
    
    # ═══════════════════════════════════════════════════════════════════
    # CURRICULUM LEARNING ADAPTATIF SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Paramètres curriculum
    curriculum_difficulty_initial: float = 0.3  # 0.2-0.4 difficulté initiale
    difficulty_progression_rate: float = 0.03   # 0.01-0.05 par epoch
    mastery_threshold: float = 0.78             # 70-85% seuil de maîtrise
    performance_window: int = 200               # 100-500 épisodes
    difficulty_adjustment_rate: float = 0.05    # 0.01-0.1 ajustement
    min_difficulty: float = 0.1                 # Difficulté minimum
    max_difficulty: float = 0.9                 # Difficulté maximum
    stability_threshold: float = 0.05           # Seuil stabilité
    
    # ═══════════════════════════════════════════════════════════════════
    # RÉCOMPENSES OPTIMISÉES SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Proposer rewards
    reward_optimal_task: float = 1.0            # Tâche optimale
    penalty_easy_task: float = -0.5             # Tâche trop facile
    penalty_impossible_task: float = -1.0       # Tâche impossible
    diversity_bonus_min: float = 0.1            # +0.1 à +0.3 bonus diversité
    diversity_bonus_max: float = 0.3
    
    # Solver rewards
    reward_correct_solution: float = 1.0        # Solution correcte
    penalty_incorrect_solution: float = -0.3    # -0.1 à -0.5 solution incorrecte
    penalty_syntax_error: float = -0.8          # Erreur syntaxe
    efficiency_bonus_min: float = 0.1           # +0.1 à +0.2 bonus efficacité
    efficiency_bonus_max: float = 0.2
    
    # ═══════════════════════════════════════════════════════════════════
    # STABILITÉ SELF-PLAY SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Target networks et stabilité
    target_network_update_rate: float = 0.005   # Soft update τ=0.005
    polyak_averaging_alpha: float = 0.999       # α=0.999 Polyak averaging
    gradient_penalty: float = 50.0              # 10.0-100.0 gradient penalty
    spectral_normalization: bool = False        # Optionnel selon recherches
    
    # ═══════════════════════════════════════════════════════════════════
    # MONITORING ET DEBUGGING SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Fréquences de monitoring
    checkpoint_frequency: int = 1000            # Chaque 1000 steps
    validation_frequency: int = 5000            # Chaque 5000 steps
    detailed_logging: bool = True               # Logging détaillé
    monitoring_enabled: bool = True             # Monitoring activé
    
    # Métriques de surveillance
    gradient_norm_threshold_max: float = 10.0   # Alerte si > 10.0
    gradient_norm_threshold_min: float = 0.01   # Alerte si < 0.01
    success_rate_threshold: float = 0.30        # Alerte si < 30%
    memory_usage_threshold: float = 0.90        # Alerte si > 90%
    
    # ═══════════════════════════════════════════════════════════════════
    # PHASES D'ENTRAÎNEMENT SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Phase 1 - Stabilisation (1000-5000 steps)
    phase1_stabilization_steps: int = 2000
    phase1_focus: str = "learning_rate_batch_size"
    
    # Phase 2 - Performance (5000-20000 steps)
    phase2_performance_steps: int = 15000
    phase2_focus: str = "rewards_curriculum"
    
    # Phase 3 - Efficacité (20000+ steps)
    phase3_efficiency_steps: int = 30000
    phase3_focus: str = "computational_efficiency"
    
    # ═══════════════════════════════════════════════════════════════════
    # OPTIMISEURS SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Paramètres Adam/AdamW
    optimizer_type: str = "AdamW"               # Adam ou AdamW
    beta1: float = 0.9                          # Beta1 standard
    beta2: float = 0.999                        # Beta2 standard
    weight_decay: float = 0.001                 # 1e-4 à 1e-2
    
    # ═══════════════════════════════════════════════════════════════════
    # MÉTRIQUES D'ÉVALUATION SELON RECHERCHES
    # ═══════════════════════════════════════════════════════════════════
    
    # Métriques primaires
    primary_metrics = [
        "task_success_rate",
        "code_execution_accuracy", 
        "mathematical_reasoning_score",
        "cross_domain_transfer"
    ]
    
    # Métriques secondaires
    secondary_metrics = [
        "learning_efficiency",
        "task_diversity",
        "computational_cost",
        "memory_usage"
    ]
    
    # Métriques de robustesse
    robustness_metrics = [
        "performance_variance",
        "stability_score",
        "generalization_score",
        "reward_hacking_resistance"
    ]

    def get_phase_config(self, current_step: int) -> Dict[str, Any]:
        """Retourne configuration selon phase d'entraînement actuelle"""
        if current_step <= self.phase1_stabilization_steps:
            return {
                "phase": 1,
                "name": "Stabilisation",
                "focus": self.phase1_focus,
                "learning_rate_proposer": self.learning_rate_proposer,
                "learning_rate_solver": self.learning_rate_solver,
                "batch_size_proposer": self.batch_size_proposer,
                "batch_size_solver": self.batch_size_solver,
                "primary_metrics": ["gradient_norms", "loss_decrease"]
            }
        elif current_step <= self.phase1_stabilization_steps + self.phase2_performance_steps:
            return {
                "phase": 2,
                "name": "Performance",
                "focus": self.phase2_focus,
                "learning_rate_proposer": self.learning_rate_proposer * 0.8,
                "learning_rate_solver": self.learning_rate_solver * 0.8,
                "batch_size_proposer": self.batch_size_proposer,
                "batch_size_solver": self.batch_size_solver,
                "primary_metrics": ["success_rate", "task_diversity"]
            }
        else:
            return {
                "phase": 3,
                "name": "Efficacité",
                "focus": self.phase3_focus,
                "learning_rate_proposer": self.learning_rate_proposer * 0.5,
                "learning_rate_solver": self.learning_rate_solver * 0.5,
                "batch_size_proposer": min(64, self.batch_size_proposer * 2),
                "batch_size_solver": min(128, self.batch_size_solver * 2),
                "primary_metrics": ["steps_per_second", "memory_usage"]
            }

    def get_reward_config(self) -> Dict[str, float]:
        """Retourne configuration des récompenses optimisées"""
        return {
            # Proposer
            "reward_optimal_task": self.reward_optimal_task,
            "penalty_easy_task": self.penalty_easy_task,
            "penalty_impossible_task": self.penalty_impossible_task,
            "diversity_bonus_min": self.diversity_bonus_min,
            "diversity_bonus_max": self.diversity_bonus_max,
            
            # Solver
            "reward_correct_solution": self.reward_correct_solution,
            "penalty_incorrect_solution": self.penalty_incorrect_solution,
            "penalty_syntax_error": self.penalty_syntax_error,
            "efficiency_bonus_min": self.efficiency_bonus_min,
            "efficiency_bonus_max": self.efficiency_bonus_max
        }

    def get_curriculum_config(self) -> Dict[str, Any]:
        """Retourne configuration curriculum learning"""
        return {
            "difficulty_initial": self.curriculum_difficulty_initial,
            "progression_rate": self.difficulty_progression_rate,
            "mastery_threshold": self.mastery_threshold,
            "performance_window": self.performance_window,
            "adjustment_rate": self.difficulty_adjustment_rate,
            "min_difficulty": self.min_difficulty,
            "max_difficulty": self.max_difficulty,
            "stability_threshold": self.stability_threshold
        }

    def validate_config(self) -> bool:
        """Valide la cohérence de la configuration"""
        try:
            # Vérifications learning rates
            assert 0 < self.learning_rate_proposer < 1, "Learning rate proposer invalide"
            assert 0 < self.learning_rate_solver < 1, "Learning rate solver invalide"
            
            # Vérifications batch sizes
            assert self.batch_size_proposer > 0, "Batch size proposer invalide"
            assert self.batch_size_solver > 0, "Batch size solver invalide"
            
            # Vérifications curriculum
            assert 0 < self.curriculum_difficulty_initial < 1, "Difficulté initiale invalide"
            assert 0 < self.mastery_threshold < 1, "Seuil maîtrise invalide"
            
            # Vérifications récompenses
            assert self.reward_optimal_task > 0, "Récompense optimale invalide"
            assert self.penalty_easy_task < 0, "Pénalité tâche facile invalide"
            
            logger.info("✅ Configuration AZR validée avec succès")
            return True
            
        except AssertionError as e:
            logger.error(f"❌ Erreur validation configuration: {e}")
            return False

# Instance globale de configuration optimisée
enhanced_azr_config = AZREnhancedConfig()

# Validation automatique
if not enhanced_azr_config.validate_config():
    logger.warning("⚠️ Configuration AZR non validée - utilisation des valeurs par défaut")
