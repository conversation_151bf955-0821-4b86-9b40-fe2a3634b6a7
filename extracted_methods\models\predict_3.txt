# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 665 à 746
# Type: Méthode de la classe EnsembleModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def predict(self, sequence):
        """
        Prédiction ensemble avec calcul unifié confiance/incertitude
        Basé sur décomposition variance épistémique/aléatoire
        """
        try:
            if len(sequence) < 2:
                return 0.5, 0.3

            # 📊 PRÉDICTIONS BASE LEARNERS
            lstm_pred, lstm_conf = self.lstm_model.predict(sequence)
            lgbm_pred, lgbm_conf = self.lgbm_model.predict(sequence)
            markov_pred, markov_conf = self.markov_model.predict(sequence)

            # 🎯 PRÉDICTIONS ET CONFIDENCES
            base_predictions = [lstm_pred, lgbm_pred, markov_pred]
            base_confidences = [lstm_conf, lgbm_conf, markov_conf]

            # 📐 CALCUL VARIANCE ÉPISTÉMIQUE (Incertitude modèle)
            # Variance entre prédictions des modèles base
            epistemic_variance = np.var(base_predictions)

            # 📊 CALCUL VARIANCE ALÉATOIRE (Incertitude données)
            # Moyenne des incertitudes individuelles pondérées
            individual_uncertainties = [1.0 - conf for conf in base_confidences]
            aleatoric_variance = np.mean(individual_uncertainties)

            # 🔬 VARIANCE TOTALE SELON FORMULE SCIENTIFIQUE
            total_variance = epistemic_variance + aleatoric_variance

            # 📈 PONDÉRATION ADAPTATIVE BASÉE SUR PERFORMANCE
            self._update_adaptive_weights(sequence, base_predictions, base_confidences)

            # 🎯 PRÉDICTION ENSEMBLE FINALE AUTONOME
            # Combinaison pondérée pure des base learners
            ensemble_prediction = (
                base_predictions[0] * self.model_weights['lstm'] +
                base_predictions[1] * self.model_weights['lgbm'] +
                base_predictions[2] * self.model_weights['markov']
            )

            # 📊 CONFIANCE ENSEMBLE UNIFIÉE SELON RECHERCHES SCIENTIFIQUES
            # Basée sur cohérence entre modèles et variance totale
            model_agreement = max(0.1, 1.0 - epistemic_variance)  # Plus d'accord = plus de confiance
            data_certainty = max(0.1, 1.0 - aleatoric_variance)   # Moins d'incertitude = plus de confiance

            # Confiance pondérée des base learners
            weighted_confidence = (
                base_confidences[0] * self.model_weights['lstm'] +
                base_confidences[1] * self.model_weights['lgbm'] +
                base_confidences[2] * self.model_weights['markov']
            )

            # Confiance finale combinant tous les facteurs selon formules scientifiques
            ensemble_confidence = np.clip(
                (model_agreement * 0.4 + data_certainty * 0.3 + weighted_confidence * 0.3),
                0.1, 0.9
            )

            # 📈 HISTORIQUE SCIENTIFIQUE
            self.ensemble_predictions.append(ensemble_prediction)
            self.confidence_history.append(ensemble_confidence)
            self.epistemic_variance_history.append(epistemic_variance)
            self.aleatoric_variance_history.append(aleatoric_variance)

            # 🎯 MÉTRIQUES DÉTAILLÉES ENSEMBLE AUTONOME
            ensemble_uncertainty = total_variance

            logger.debug(f"🧠 Ensemble Autonome - LSTM: {lstm_pred:.3f}, LGBM: {lgbm_pred:.3f}, "
                        f"Markov: {markov_pred:.3f}")
            logger.debug(f"📊 Ensemble - Poids: LSTM={self.model_weights['lstm']:.3f}, "
                        f"LGBM={self.model_weights['lgbm']:.3f}, Markov={self.model_weights['markov']:.3f}")
            logger.debug(f"🔬 Ensemble - Variance épistémique: {epistemic_variance:.3f}, "
                        f"Variance aléatoire: {aleatoric_variance:.3f}")
            logger.debug(f"🎯 Ensemble - Prédiction finale: {ensemble_prediction:.3f}, "
                        f"Confiance: {ensemble_confidence:.3f}")

            return float(ensemble_prediction), float(ensemble_confidence)

        except Exception as e:
            logger.error(f"Erreur Ensemble predict: {e}")
            return 0.5, 0.3