# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 322 à 330
# Type: Méthode de la classe MetricsCalculator

    def calculate_accuracy(self, y_true: List[int], y_pred: List[int]) -> float:
        """Calcule précision"""
        try:
            if not y_true or not y_pred or len(y_true) != len(y_pred):
                return global_config.calculations.default_accuracy
            return accuracy_score(y_true, y_pred)
        except Exception as e:
            logger.error(f"Erreur calcul accuracy: {e}")
            return global_config.calculations.default_accuracy