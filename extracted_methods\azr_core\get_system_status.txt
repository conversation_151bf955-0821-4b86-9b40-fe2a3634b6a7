# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 1770 à 1805
# Type: Méthode de la classe AZRSystem

    def get_system_status(self) -> Dict[str, Any]:
        """Retourne statut complet du système AZR avec métriques optimisées"""
        try:
            # Calcul métriques récentes
            recent_accuracy = (np.mean(self.training_history['prediction_accuracy'][-10:])
                             if self.training_history['prediction_accuracy'] else 0.0)

            current_lambda = (self.training_history['adaptive_lambda_history'][-1]
                            if self.training_history['adaptive_lambda_history'] else self.adaptive_lambda)

            return {
                'training_iterations': self.training_iterations,
                'shared_parameters': self.shared_parameters.copy(),
                'proposer_metrics': self.proposer.performance_metrics.copy(),
                'solver_metrics': self.solver.performance_metrics.copy(),
                'recent_performance': {
                    'avg_learnability_reward': np.mean(self.training_history['learnability_rewards'][-10:])
                        if self.training_history['learnability_rewards'] else 0.0,
                    'avg_solution_reward': np.mean(self.training_history['solution_rewards'][-10:])
                        if self.training_history['solution_rewards'] else 0.0,
                    'avg_combined_reward': np.mean(self.training_history['combined_rewards'][-10:])
                        if self.training_history['combined_rewards'] else 0.0,
                    'recent_accuracy': recent_accuracy,
                    'current_adaptive_lambda': current_lambda,
                    'target_accuracy': self.target_accuracy
                },
                'reward_system_status': {
                    'adaptive_lambda_enabled': True,
                    'progressive_rewards_enabled': True,
                    'improvement_tracking_enabled': True,
                    'confidence_calibration_enabled': True
                }
            }
        except Exception as e:
            logger.error(f"Erreur statut système: {e}")
            return {'error': str(e)}