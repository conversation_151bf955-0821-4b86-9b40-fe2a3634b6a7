# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 837 à 843
# Type: Méthode de la classe BaccaratPredictorApp

    def _azr_calculate_solution_reward(self, task, solution):
        """Calcule récompense solution (stub)"""
        if not solution:
            return 0.0

        quality_score = solution.get('quality_score', 0.5)
        return 1.0 if quality_score > 0.6 else 0.0