# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 534 à 539
# Type: Méthode de la classe MarkovModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, order=2):
        self.order = order  # Ordre de la chaîne de Markov
        self.transition_matrix = {}
        self.state_counts = {}
        self.prediction_history = []
        self.is_trained = False