"""
GESTIONNAIRE NOUVELLE PARTIE AZR
================================

Module pour gérer le démarrage d'une nouvelle partie avec calibration
optimale AZR pour 60 manches.
"""

import sys
import os
import time
import threading
from pathlib import Path
from typing import Dict, Any
import logging

# Import modules AZR (maintenant dans le même dossier)
from game_initialization_calibrator import GameInitializationCalibrator
from realtime_calibrator import RealtimeCalibrator
from unlimited_threshold_manager import UnlimitedThresholdManager

# Configuration logging
logger = logging.getLogger(__name__)


class NewGameHandler:
    """
    Gestionnaire de nouvelle partie AZR

    Gère le démarrage d'une nouvelle partie avec calibration complète
    et initialisation optimale pour 60 manches.
    """

    def __init__(self, main_interface):
        """
        Initialisation gestionnaire nouvelle partie

        Args:
            main_interface: Référence vers interface principale
        """
        self.main_interface = main_interface

        # Calibrateurs AZR
        self.game_calibrator = GameInitializationCalibrator()
        self.realtime_calibrator = RealtimeCalibrator()
        self.threshold_manager = UnlimitedThresholdManager()

        # État partie
        self.game_active = False
        self.game_round = 0
        self.game_phase = 'waiting'  # waiting, warmup, optimal, post_optimal
        self.calibration_in_progress = False

        logger.info("NewGameHandler initialisé")

    def start_new_azr_game(self) -> Dict[str, Any]:
        """
        Lance nouvelle partie AZR avec calibration complète

        Returns:
            Dict contenant résultats démarrage
        """
        try:
            if self.calibration_in_progress:
                return {
                    'success': False,
                    'message': 'Calibration déjà en cours'
                }

            logger.info("🎯 Démarrage nouvelle partie AZR (60 manches)")

            # Mise à jour interface
            self._update_interface_status("🔄 Calibration en cours...")

            # Lancement calibration en thread séparé
            calibration_thread = threading.Thread(
                target=self._perform_game_calibration,
                daemon=True
            )
            calibration_thread.start()

            return {
                'success': True,
                'message': 'Calibration nouvelle partie démarrée',
                'status': 'calibrating'
            }

        except Exception as e:
            logger.error(f"Erreur démarrage nouvelle partie: {e}")
            return {
                'success': False,
                'message': f'Erreur: {e}'
            }

    def _perform_game_calibration(self):
        """Effectue calibration complète pour nouvelle partie"""
        try:
            self.calibration_in_progress = True
            calibration_start = time.time()

            # ═══════════════════════════════════════════════════════════════════
            # CALIBRATION COMPLÈTE NOUVELLE PARTIE
            # ═══════════════════════════════════════════════════════════════════

            # 1. Calibration initialisation
            self._update_interface_status("🔄 Calibration système...")
            init_result = self.game_calibrator.start_new_game_calibration()

            if not init_result['success']:
                self._handle_calibration_error("Erreur calibration système", init_result)
                return

            # 2. Démarrage calibration temps réel
            self._update_interface_status("⚡ Activation calibration temps réel...")
            realtime_result = self.realtime_calibrator.start_realtime_calibration()

            if not realtime_result['success']:
                self._handle_calibration_error("Erreur calibration temps réel", realtime_result)
                return

            # 3. Reset données partie
            self._update_interface_status("🔄 Reset données partie...")
            self._reset_game_data()

            # 4. Configuration interface
            self._update_interface_status("🎯 Configuration interface...")
            self._configure_interface_for_new_game()

            # 5. Finalisation
            calibration_duration = time.time() - calibration_start

            # Mise à jour état
            self.game_active = True
            self.game_round = 0
            self.game_phase = 'warmup'
            self.calibration_in_progress = False

            # Mise à jour interface finale
            self._update_interface_status(f"✅ Prêt - Calibration: {calibration_duration:.1f}s")
            self._update_game_phase_display()

            logger.info(f"✅ Nouvelle partie AZR prête - Calibration: {calibration_duration:.1f}s")

        except Exception as e:
            logger.error(f"Erreur calibration partie: {e}")
            self._handle_calibration_error("Erreur calibration", {'error': str(e)})

    def _reset_game_data(self):
        """Reset toutes les données de partie"""
        try:
            # Reset données interface
            if hasattr(self.main_interface, 'results'):
                self.main_interface.results.clear()

            if hasattr(self.main_interface, 'predictions'):
                self.main_interface.predictions.clear()

            # Reset compteurs
            self.game_round = 0

            # Reset graphique
            if hasattr(self.main_interface, 'draw_trend_chart'):
                self.main_interface.draw_trend_chart()

            # Reset statistiques
            if hasattr(self.main_interface, 'update_stats'):
                self.main_interface.update_stats()

            logger.info("Données partie réinitialisées")

        except Exception as e:
            logger.error(f"Erreur reset données: {e}")

    def _configure_interface_for_new_game(self):
        """Configure interface pour nouvelle partie"""
        try:
            # Activation boutons prédiction
            if hasattr(self.main_interface, 'player_button'):
                self.main_interface.player_button.config(state='normal')

            if hasattr(self.main_interface, 'banker_button'):
                self.main_interface.banker_button.config(state='normal')

            # Mise à jour titre
            if hasattr(self.main_interface, 'root'):
                self.main_interface.root.title("🎯 AZR Baccarat - Nouvelle Partie (60 manches)")

            # Reset affichage prédiction
            if hasattr(self.main_interface, 'prediction_vars'):
                self.main_interface.prediction_vars['player'].set("Player: --%")
                self.main_interface.prediction_vars['banker'].set("Banker: --%")
                self.main_interface.prediction_vars['confidence'].set("Confiance: En attente")
                self.main_interface.prediction_vars['recommendation'].set("🎯 Prêt pour première manche")

            logger.info("Interface configurée pour nouvelle partie")

        except Exception as e:
            logger.error(f"Erreur configuration interface: {e}")

    def _update_interface_status(self, status: str):
        """Met à jour statut dans interface"""
        try:
            # Mise à jour statut calibration AZR
            if hasattr(self.main_interface, 'azr_calibration_status'):
                self.main_interface.azr_calibration_status.set(status)

            # Mise à jour dans logs si disponible
            if hasattr(self.main_interface, 'log_message'):
                self.main_interface.log_message(status)

            # Force mise à jour interface
            if hasattr(self.main_interface, 'root'):
                self.main_interface.root.update_idletasks()

        except Exception as e:
            logger.error(f"Erreur mise à jour interface: {e}")

    def _update_game_phase_display(self):
        """Met à jour affichage phase de jeu"""
        try:
            phase_messages = {
                'waiting': "📍 Phase: En attente nouvelle partie",
                'warmup': "🔥 Phase: Échauffement (manches 1-30)",
                'optimal': "🎯 Phase: Optimale (manches 31-60)",
                'post_optimal': "🏁 Phase: Terminée"
            }

            phase_message = phase_messages.get(self.game_phase, "📍 Phase: Inconnue")

            # Mise à jour interface
            if hasattr(self.main_interface, 'game_phase_status'):
                self.main_interface.game_phase_status.set(phase_message)

            if hasattr(self.main_interface, 'azr_phase_var'):
                self.main_interface.azr_phase_var.set(phase_message)

        except Exception as e:
            logger.error(f"Erreur mise à jour phase: {e}")

    def _handle_calibration_error(self, message: str, error_details: Dict[str, Any]):
        """Gère erreurs de calibration"""
        try:
            error_msg = f"{message}: {error_details.get('error', 'Erreur inconnue')}"
            logger.error(error_msg)

            # Mise à jour interface
            self._update_interface_status(f"❌ {message}")

            # Reset état
            self.calibration_in_progress = False
            self.game_active = False

        except Exception as e:
            logger.error(f"Erreur gestion erreur calibration: {e}")

    def process_round_result(self, outcome: int) -> Dict[str, Any]:
        """
        Traite résultat d'une manche

        Args:
            outcome: 0 (Player) ou 1 (Banker)

        Returns:
            Dict contenant résultats traitement
        """
        try:
            if not self.game_active:
                return {
                    'success': False,
                    'message': 'Aucune partie active'
                }

            self.game_round += 1

            # Mise à jour phase selon manche
            self._update_game_phase()

            # Traitement calibration temps réel
            if hasattr(self.main_interface, 'last_prediction'):
                prediction_result = getattr(self.main_interface, 'last_prediction', {})

                # Calibration temps réel
                calibration_result = self.realtime_calibrator.process_round_result(
                    self.game_round, prediction_result, outcome
                )

                # Mise à jour seuils adaptatifs
                threshold_result = self.threshold_manager.update_thresholds_realtime(
                    self.game_round, prediction_result, outcome
                )

                # Mise à jour interface
                self._update_round_display()

                return {
                    'success': True,
                    'round': self.game_round,
                    'phase': self.game_phase,
                    'calibration_result': calibration_result,
                    'threshold_result': threshold_result
                }

            return {
                'success': True,
                'round': self.game_round,
                'phase': self.game_phase,
                'message': 'Manche traitée'
            }

        except Exception as e:
            logger.error(f"Erreur traitement manche: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _update_game_phase(self):
        """Met à jour phase de jeu selon manche"""
        try:
            if self.game_round <= 30:
                new_phase = 'warmup'
            elif self.game_round <= 60:
                new_phase = 'optimal'
            else:
                new_phase = 'post_optimal'

            if new_phase != self.game_phase:
                self.game_phase = new_phase
                self._update_game_phase_display()
                logger.info(f"Passage phase: {new_phase} (manche {self.game_round})")

        except Exception as e:
            logger.error(f"Erreur mise à jour phase: {e}")

    def _update_round_display(self):
        """Met à jour affichage manche"""
        try:
            # Mise à jour compteur manches
            round_message = f"Manche: {self.game_round}/60"

            if hasattr(self.main_interface, 'stats_vars') and 'game_stats' in self.main_interface.stats_vars:
                current_stats = self.main_interface.stats_vars['game_stats'].get()
                # Mise à jour avec nouveau compteur
                updated_stats = f"{round_message} | " + current_stats.split('|', 1)[-1] if '|' in current_stats else round_message
                self.main_interface.stats_vars['game_stats'].set(updated_stats)

            # Mise à jour phase
            self._update_game_phase_display()

        except Exception as e:
            logger.error(f"Erreur mise à jour affichage: {e}")

    def get_game_status(self) -> Dict[str, Any]:
        """Obtient statut partie actuelle"""
        return {
            'game_active': self.game_active,
            'game_round': self.game_round,
            'game_phase': self.game_phase,
            'calibration_in_progress': self.calibration_in_progress,
            'calibration_complete': self.game_calibrator.calibration_complete,
            'ready_for_game': self.game_calibrator.is_ready_for_game()
        }

    def is_game_complete(self) -> bool:
        """Vérifie si partie terminée"""
        return self.game_round >= 60

    def get_optimal_parameters(self) -> Dict[str, float]:
        """Obtient paramètres optimaux calibrés"""
        return self.game_calibrator.get_optimal_parameters()
