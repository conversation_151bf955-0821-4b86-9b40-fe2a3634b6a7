# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 223 à 235
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def _check_adaptation_needed(self, session_accuracy: float,
                               reasoning_result: Dict[str, Any]) -> bool:
        """Vérifie si adaptation est nécessaire"""
        # Critères d'adaptation
        low_accuracy = session_accuracy < self.adaptation_threshold
        low_confidence = reasoning_result['confidence'] < 0.4
        high_uncertainty = reasoning_result['uncertainty'] > 0.7
        few_patterns = reasoning_result['reasoning_details']['patterns_used'] < 2

        # Adaptation nécessaire si plusieurs critères remplis
        criteria_met = sum([low_accuracy, low_confidence, high_uncertainty, few_patterns])

        return criteria_met >= 2