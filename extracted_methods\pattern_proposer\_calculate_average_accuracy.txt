# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 733 à 746
# Type: Méthode de la classe BaccaratPatternProposer

    def _calculate_average_accuracy(self, lengths: List[int], performance_data: Dict) -> float:
        """Calcule accuracy moyenne pour une liste de longueurs"""
        if not lengths:
            return 0.0

        total_accuracy = 0.0
        valid_count = 0

        for length in lengths:
            if length in performance_data and performance_data[length]['attempts'] > 0:
                total_accuracy += performance_data[length]['accuracy']
                valid_count += 1

        return total_accuracy / valid_count if valid_count > 0 else 0.0