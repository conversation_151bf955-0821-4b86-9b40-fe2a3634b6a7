# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 850 à 864
# Type: Méthode

def validate_probabilities(probabilities: Dict[str, float]) -> bool:
    """Valide que les probabilités sont cohérentes"""
    try:
        # Vérification valeurs [0,1]
        for prob in probabilities.values():
            if not (0 <= prob <= 1):
                return False

        # Vérification somme proche de 1 (tolérance)
        total = sum(probabilities.values())
        tolerance = global_config.calculations.probability_tolerance
        return abs(total - 1.0) < tolerance

    except Exception:
        return False