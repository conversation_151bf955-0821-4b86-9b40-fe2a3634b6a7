# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 634 à 650
# Type: Méthode de la classe EnsembleModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self):
        # 📊 BASE LEARNERS AUTONOMES
        self.lstm_model = LSTMModel(sequence_length=8, hidden_size=32)
        self.lgbm_model = LGBMModel(n_estimators=30, max_depth=3)
        self.markov_model = MarkovModel(order=2)

        # 📈 SYSTÈME DE PONDÉRATION ADAPTATIF
        self.model_weights = {'lstm': 0.33, 'lgbm': 0.33, 'markov': 0.34}
        self.performance_history = {'lstm': [], 'lgbm': [], 'markov': []}

        # 🎯 HISTORIQUE ENSEMBLE
        self.ensemble_predictions = []
        self.confidence_history = []

        # 🔬 MÉTRIQUES SCIENTIFIQUES
        self.epistemic_variance_history = []
        self.aleatoric_variance_history = []