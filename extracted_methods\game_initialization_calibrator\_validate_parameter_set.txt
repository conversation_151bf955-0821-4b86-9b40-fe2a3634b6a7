# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\game_initialization_calibrator.py
# Lignes: 380 à 398
# Type: Méthode de la classe GameInitializationCalibrator

    def _validate_parameter_set(self, parameters: Dict[str, float]) -> float:
        """Valide un jeu de paramètres"""
        try:
            score = 0.0
            total_checks = 0
            
            # Validation plages
            for param, value in parameters.items():
                if param in self.parameter_ranges_60_rounds:
                    min_val, max_val = self.parameter_ranges_60_rounds[param]
                    if min_val <= value <= max_val:
                        score += 1.0
                    total_checks += 1
            
            return score / total_checks if total_checks > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Erreur validation paramètres: {e}")
            return 0.0