# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 179 à 196
# Type: Méthode de la classe ConfidenceCalculator

    def calculate_lstm_confidence(self, probas: np.n<PERSON><PERSON>) -> np.ndarray:
        """Calcule confiance LSTM basée sur distance à 0.5"""
        try:
            banker_probs = probas[:, 1] if probas.ndim > 1 else probas

            # Confiance basée sur distance à 0.5
            base_confidence = np.abs(banker_probs - 0.5) * 2.0

            # Appliquer facteurs de configuration
            confidence = base_confidence * global_config.calculations.confidence_multiplier

            return np.clip(confidence,
                          global_config.calculations.confidence_min,
                          global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur confiance LSTM: {e}")
            return np.full(len(probas), global_config.calculations.default_confidence)