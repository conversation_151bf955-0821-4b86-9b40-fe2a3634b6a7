# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 597 à 619
# Type: Méthode de la classe UncertaintyCalculator

    def _calculate_fallback_aleatoric_uncertainty(self, predictions: Dict[str, Dict[str, float]]) -> float:
        """Fallback pour incertitude aléatoire basée sur entropie moyenne"""
        try:
            entropies = []

            for pred in predictions.values():
                banker_prob = pred.get('banker', 0.5)
                player_prob = pred.get('player', 0.5)

                # Entropie binaire
                entropy = -(banker_prob * np.log2(banker_prob + 1e-10) +
                           player_prob * np.log2(player_prob + 1e-10))
                entropies.append(entropy)

            # Moyenne des entropies normalisée
            mean_entropy = np.mean(entropies)
            max_entropy = 1.0  # Entropie max pour distribution binaire

            return min(mean_entropy / max_entropy, 1.0)

        except Exception as e:
            logger.error(f"Erreur fallback incertitude aléatoire: {e}")
            return 0.5