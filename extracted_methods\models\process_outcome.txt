# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 157 à 194
# Type: Méthode de la classe AZRBaccaratPredictor

    def process_outcome(self, actual_outcome: int, round_number: int = None) -> Dict[str, Any]:
        """
        Traite le résultat réel d'une manche pour apprentissage adaptatif

        Args:
            actual_outcome: Résultat réel (0=Player, 1=Banker)
            round_number: Numéro de manche optionnel

        Returns:
            Dict contenant résultats du traitement
        """
        try:
            if not self.is_active:
                return {'success': False, 'message': 'Aucune session active'}

            # Traitement par le moteur self-play
            round_result = self.self_play_engine.process_new_round(
                actual_outcome, round_number
            )

            if round_result['success']:
                # Mise à jour métriques performance
                self._update_performance_metrics(round_result)

                # Vérification fin de session
                if round_result.get('session_complete', False):
                    self._end_session()

                logger.debug(f"Outcome traité - Résultat: {actual_outcome}, "
                           f"Correct: {round_result['prediction_correct']}")

                return round_result
            else:
                return round_result

        except Exception as e:
            logger.error(f"Erreur traitement outcome: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}