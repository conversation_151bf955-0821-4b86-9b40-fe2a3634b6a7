# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 773 à 798
# Type: Méthode de la classe EnsembleCalculator

    def calculate_ensemble_prediction(self, model_outputs: Dict[str, Any],
                                    ensemble_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calcule prédiction d'ensemble complète
        """
        try:
            # Extraction prédictions et poids
            predictions = model_outputs.get('predictions', {})
            weights = ensemble_config.get('weights', {})

            # Prédiction finale
            final_prediction = self.prediction_calc.calculate_final_prediction(predictions, weights)

            # Incertitude
            uncertainty = self.uncertainty_calc.calculate_prediction_uncertainty(predictions)
            final_prediction['uncertainty'] = uncertainty

            # Métriques additionnelles
            final_prediction['ensemble_method'] = ensemble_config.get('method', 'weighted_average')
            final_prediction['num_models'] = len(predictions)

            return final_prediction

        except Exception as e:
            logger.error(f"Erreur ensemble: {e}")
            return self.prediction_calc._default_prediction()