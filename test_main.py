#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TEST MAIN - DIAGNOSTIC
======================

Script de test pour identifier les problèmes de lancement.
"""

import sys
import os
import logging

# Configuration logging simple
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def test_basic_imports():
    """Test des imports de base"""
    print("🔍 Test imports de base...")
    
    try:
        import numpy as np
        print("✅ numpy OK")
    except ImportError as e:
        print(f"❌ numpy: {e}")
        return False
    
    try:
        import tkinter as tk
        print("✅ tkinter OK")
    except ImportError as e:
        print(f"❌ tkinter: {e}")
        return False
    
    return True

def test_azr_imports():
    """Test des imports AZR"""
    print("🔍 Test imports AZR...")
    
    # Test parameters
    try:
        from parameters import global_config
        print("✅ parameters OK")
    except ImportError as e:
        print(f"❌ parameters: {e}")
        return False
    except Exception as e:
        print(f"❌ parameters (autre erreur): {e}")
        return False
    
    # Test adaptive_reasoner
    try:
        from adaptive_reasoner import BaccaratAdaptiveReasoner
        print("✅ adaptive_reasoner OK")
    except ImportError as e:
        print(f"❌ adaptive_reasoner: {e}")
        return False
    except Exception as e:
        print(f"❌ adaptive_reasoner (autre erreur): {e}")
        return False
    
    # Test pattern_proposer
    try:
        from pattern_proposer import BaccaratPatternProposer
        print("✅ pattern_proposer OK")
    except ImportError as e:
        print(f"❌ pattern_proposer: {e}")
        return False
    except Exception as e:
        print(f"❌ pattern_proposer (autre erreur): {e}")
        return False
    
    # Test pattern_validator
    try:
        from pattern_validator import BaccaratPatternValidator
        print("✅ pattern_validator OK")
    except ImportError as e:
        print(f"❌ pattern_validator: {e}")
        return False
    except Exception as e:
        print(f"❌ pattern_validator (autre erreur): {e}")
        return False
    
    # Test self_play_engine
    try:
        from self_play_engine import BaccaratSelfPlayEngine
        print("✅ self_play_engine OK")
    except ImportError as e:
        print(f"❌ self_play_engine: {e}")
        return False
    except Exception as e:
        print(f"❌ self_play_engine (autre erreur): {e}")
        return False
    
    # Test models
    try:
        from models import AZRBaccaratPredictor
        print("✅ models OK")
    except ImportError as e:
        print(f"❌ models: {e}")
        return False
    except Exception as e:
        print(f"❌ models (autre erreur): {e}")
        return False
    
    # Test azr_core
    try:
        from azr_core import AZRSystem
        print("✅ azr_core OK")
    except ImportError as e:
        print(f"❌ azr_core: {e}")
        return False
    except Exception as e:
        print(f"❌ azr_core (autre erreur): {e}")
        return False
    
    return True

def test_interface_import():
    """Test import interface"""
    print("🔍 Test import interface...")
    
    try:
        from main_interface import BaccaratPredictorApp
        print("✅ main_interface OK")
        return True
    except ImportError as e:
        print(f"❌ main_interface: {e}")
        return False
    except Exception as e:
        print(f"❌ main_interface (autre erreur): {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_creation():
    """Test création simple des objets"""
    print("🔍 Test création objets...")
    
    try:
        from adaptive_reasoner import BaccaratAdaptiveReasoner
        reasoner = BaccaratAdaptiveReasoner()
        print("✅ BaccaratAdaptiveReasoner créé")
    except Exception as e:
        print(f"❌ BaccaratAdaptiveReasoner: {e}")
        return False
    
    try:
        from pattern_proposer import BaccaratPatternProposer
        proposer = BaccaratPatternProposer()
        print("✅ BaccaratPatternProposer créé")
    except Exception as e:
        print(f"❌ BaccaratPatternProposer: {e}")
        return False
    
    try:
        from pattern_validator import BaccaratPatternValidator
        validator = BaccaratPatternValidator()
        print("✅ BaccaratPatternValidator créé")
    except Exception as e:
        print(f"❌ BaccaratPatternValidator: {e}")
        return False
    
    return True

def test_interface_creation():
    """Test création interface"""
    print("🔍 Test création interface...")
    
    try:
        from main_interface import BaccaratPredictorApp
        app = BaccaratPredictorApp()
        print("✅ BaccaratPredictorApp créé")
        
        # Test mise à jour sans mainloop
        app.root.update()
        print("✅ Interface mise à jour")
        
        # Fermeture
        app.root.destroy()
        print("✅ Interface fermée")
        
        return True
    except Exception as e:
        print(f"❌ Création interface: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test principal"""
    print("🧪 DIAGNOSTIC MAIN.PY")
    print("=" * 50)
    
    # Test 1: Imports de base
    if not test_basic_imports():
        print("❌ Échec imports de base")
        return 1
    
    # Test 2: Imports AZR
    if not test_azr_imports():
        print("❌ Échec imports AZR")
        return 1
    
    # Test 3: Import interface
    if not test_interface_import():
        print("❌ Échec import interface")
        return 1
    
    # Test 4: Création objets
    if not test_simple_creation():
        print("❌ Échec création objets")
        return 1
    
    # Test 5: Création interface
    if not test_interface_creation():
        print("❌ Échec création interface")
        return 1
    
    print("\n🎉 TOUS LES TESTS RÉUSSIS!")
    print("Le problème n'est pas dans les imports ou créations de base.")
    print("Essayez de lancer main.py maintenant.")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\nAppuyez sur Entrée pour fermer...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Test interrompu")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Erreur critique: {e}")
        import traceback
        traceback.print_exc()
        input("\nAppuyez sur Entrée pour fermer...")
        sys.exit(1)
