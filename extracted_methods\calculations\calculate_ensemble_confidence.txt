# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 122 à 155
# Type: Méthode de la classe ConfidenceCalculator

    def calculate_ensemble_confidence(self, predictions: Dict[str, Dict[str, float]],
                                    weights: Dict[str, float]) -> float:
        """
        FORMULE UNIFIÉE DE CONFIANCE ENSEMBLE - Basée sur recherches internationales

        Formule : Confiance = (Performance_globale × Consensus) / (1 + Variance_pondérée)
        """
        try:
            if not predictions or not weights:
                return global_config.calculations.default_confidence

            # 1. Calcul du consensus (accord entre modèles)
            consensus = self._calculate_model_consensus(predictions)

            # 2. Performance globale (moyenne des poids normalisés)
            total_weight = sum(weights.values())
            if total_weight > 0:
                global_performance = total_weight / len(weights)  # Performance moyenne
            else:
                global_performance = global_config.calculations.default_confidence

            # 3. Variance pondérée des prédictions
            weighted_variance = self._calculate_weighted_variance(predictions, weights)

            # 4. FORMULE UNIFIÉE FINALE
            confidence = (global_performance * consensus) / (1.0 + weighted_variance)

            return np.clip(confidence,
                          global_config.calculations.confidence_min,
                          global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur confiance ensemble unifiée: {e}")
            return global_config.calculations.default_confidence