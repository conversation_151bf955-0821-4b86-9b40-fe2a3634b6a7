# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 570 à 581
# Type: Méthode de la classe AdaptiveReasoner

    def _calculate_optimal_analysis_window(self, current_round: int, history_length: int) -> int:
        """Calcule fenêtre d'analyse optimale selon position dans jeu"""
        if current_round is None:
            return global_config.azr.pattern_detection_window

        # Fenêtre adaptative selon position
        if current_round <= 35:  # Début fenêtre optimale
            return min(global_config.azr.pattern_detection_window, history_length)
        elif current_round <= 50:  # Milieu fenêtre optimale
            return min(global_config.azr.confidence_calculation_window, history_length)
        else:  # Fin fenêtre optimale
            return min(global_config.azr.pattern_validation_window, history_length)