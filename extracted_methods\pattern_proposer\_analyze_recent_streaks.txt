# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 443 à 473
# Type: Méthode de la classe BaccaratPatternProposer

    def _analyze_recent_streaks(self, results: List[int]) -> List[Dict]:
        """Analyse les séries récentes"""
        streaks = []
        if len(results) < 2:
            return streaks

        current_outcome = results[0]
        current_length = 1

        for i in range(1, len(results)):
            if results[i] == current_outcome:
                current_length += 1
            else:
                if current_length >= 2:
                    streaks.append({
                        'outcome': current_outcome,
                        'length': current_length,
                        'end_position': i - 1
                    })
                current_outcome = results[i]
                current_length = 1

        # Ajouter la série finale si elle existe
        if current_length >= 2:
            streaks.append({
                'outcome': current_outcome,
                'length': current_length,
                'end_position': len(results) - 1
            })

        return streaks[-5:]  # G<PERSON>er les 5 séries les plus récentes