# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 48 à 53
# Type: Méthode

    def recall_score(y_true, y_pred, average='binary', zero_division=0):
        if not y_true or not y_pred:
            return 0.5
        tp = sum(1 for t, p in zip(y_true, y_pred) if t == 1 and p == 1)
        fn = sum(1 for t, p in zip(y_true, y_pred) if t == 1 and p == 0)
        return tp / (tp + fn) if (tp + fn) > 0 else zero_division