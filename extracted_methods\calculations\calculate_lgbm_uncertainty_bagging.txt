# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 655 à 685
# Type: Méthode de la classe UncertaintyCalculator

    def calculate_lgbm_uncertainty_bagging(self, model, X: np.ndarray) -> np.ndarray:
        """Calcule incertitude LGBM via variance des estimateurs"""
        try:
            if not hasattr(model, 'is_trained') or not model.is_trained:
                return np.full(len(X), global_config.calculations.default_confidence)

            # Si modèle calibré avec estimateurs multiples
            if hasattr(model, 'calibrator') and model.calibrator is not None:
                if hasattr(model.calibrator, 'calibrated_classifiers_'):
                    estimator_probas = []
                    for clf in model.calibrator.calibrated_classifiers_:
                        probas = clf.predict_proba(X)
                        estimator_probas.append(probas[:, 1])  # Probabilité Banker

                    if estimator_probas:
                        estimator_probas = np.array(estimator_probas)
                        uncertainty = np.var(estimator_probas, axis=0)
                        # ✅ Normalisation stabilisée pour LGBM
                        uncertainty = np.clip(np.sqrt(uncertainty * 8.0) / 2.0, 0.0, 1.0)
                        return uncertainty

            # Fallback : incertitude basée sur distance à 0.5
            probas = model.predict_proba(X)
            banker_probs = probas[:, 1]
            uncertainty = 1.0 - np.abs(banker_probs - 0.5) * 2.0

            return np.clip(uncertainty, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Erreur incertitude LGBM bagging: {e}")
            return np.full(len(X), global_config.calculations.default_confidence)