# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 156 à 187
# Type: Méthode de la classe BaccaratPatternProposer

    def _propose_frequency_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns basés sur fréquences"""
        patterns = []

        if len(results) < 10:
            return patterns

        # Analyse fréquences par fenêtres glissantes
        window_sizes = [10, 15, 20, 30]

        for window_size in window_sizes:
            if len(results) >= window_size:
                window = results[-window_size:]
                player_freq = window.count(0) / len(window)
                banker_freq = window.count(1) / len(window)

                # Détection déséquilibres significatifs
                if abs(player_freq - 0.5) > 0.15:  # Seuil de déséquilibre
                    dominant = 0 if player_freq > banker_freq else 1
                    confidence = abs(player_freq - 0.5) * 2  # Normalisation

                    patterns.append({
                        'type': 'frequency',
                        'pattern': f'dominant_{dominant}',
                        'window_size': window_size,
                        'dominant_outcome': dominant,
                        'frequency_ratio': max(player_freq, banker_freq),
                        'confidence': confidence,
                        'deviation': abs(player_freq - 0.5)
                    })

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:5]