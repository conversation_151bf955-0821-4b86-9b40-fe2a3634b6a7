# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 115 à 221
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def process_new_round(self, actual_outcome: int, round_number: int = None) -> Dict[str, Any]:
        """
        Traite un nouveau round de jeu

        Args:
            actual_outcome: Résultat réel (0=Player, 1=Banker)
            round_number: Numéro de round optionnel

        Returns:
            Dict contenant prédiction et analyse
        """
        try:
            if not self.is_running or not self.current_session:
                return {'success': False, 'message': 'Aucune session active'}

            if self.is_paused:
                return {'success': False, 'message': 'Session en pause'}

            round_start = time.time()

            # 1. Raisonnement sur prochaine issue (avant de connaître le résultat)
            current_history = list(self.current_session['game_history'])
            reasoning_result = self.adaptive_reasoner.reason_next_outcome(
                current_history, round_number
            )

            # 2. Ajout du résultat réel à l'historique
            self.current_session['game_history'].append(actual_outcome)
            self.current_session['rounds_played'] += 1
            self.total_rounds_played += 1

            # 3. Évaluation de la prédiction
            predicted_outcome = reasoning_result['prediction']['predicted_outcome']
            prediction_correct = (predicted_outcome == actual_outcome)

            # 4. Mise à jour statistiques session
            self.current_session['predictions_made'] += 1
            if prediction_correct:
                self.current_session['correct_predictions'] += 1

            # 5. Mise à jour du raisonneur avec le résultat
            patterns_used = reasoning_result.get('pattern_breakdown', {}).get('top_patterns', [])
            self.adaptive_reasoner.update_with_outcome(
                predicted_outcome, actual_outcome, patterns_used
            )

            # 6. Calcul métriques de performance
            session_accuracy = (self.current_session['correct_predictions'] /
                              self.current_session['predictions_made'])

            # 7. Détection besoin d'adaptation
            adaptation_needed = self._check_adaptation_needed(session_accuracy, reasoning_result)

            # 8. Exécution adaptation si nécessaire
            adaptation_result = None
            if adaptation_needed:
                adaptation_result = self._execute_adaptation(reasoning_result, session_accuracy)
                self.current_session['adaptations_made'] += 1

            # 9. Mise à jour historique performance
            round_performance = {
                'round': round_number or self.current_session['rounds_played'],
                'predicted': predicted_outcome,
                'actual': actual_outcome,
                'correct': prediction_correct,
                'confidence': reasoning_result['confidence'],
                'uncertainty': reasoning_result['uncertainty'],
                'patterns_used': len(patterns_used),
                'reasoning_time': time.time() - round_start
            }

            self.current_session['session_performance'].append(round_performance)
            self.performance_history.append(round_performance)

            # 10. Incrémentation cycles d'apprentissage
            self.learning_cycles += 1

            # 11. Vérification fin de session
            session_complete = self._check_session_completion()

            # Résultat du round
            result = {
                'success': True,
                'round_number': round_number or self.current_session['rounds_played'],
                'prediction': reasoning_result['prediction'],
                'actual_outcome': actual_outcome,
                'prediction_correct': prediction_correct,
                'confidence': reasoning_result['confidence'],
                'uncertainty': reasoning_result['uncertainty'],
                'recommendation': reasoning_result['recommendation'],
                'session_accuracy': session_accuracy,
                'adaptation_executed': adaptation_needed,
                'adaptation_result': adaptation_result,
                'session_complete': session_complete,
                'reasoning_details': reasoning_result['reasoning_details'],
                'pattern_breakdown': reasoning_result['pattern_breakdown'],
                'performance_metrics': self._calculate_performance_metrics()
            }

            logger.debug(f"Round traité - Prédiction: {predicted_outcome}, "
                        f"Réel: {actual_outcome}, Correct: {prediction_correct}")

            return result

        except Exception as e:
            logger.error(f"Erreur traitement round: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}