# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 616 à 620
# Type: Méthode de la classe MarkovModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def fit(self, X: np.ndarray, y: np.n<PERSON><PERSON>):
        """Entraînement Markov (mise à jour matrice)"""
        logger.info("Markov: Entraînement simulé - Modèle prêt")
        self.is_trained = True
        return self