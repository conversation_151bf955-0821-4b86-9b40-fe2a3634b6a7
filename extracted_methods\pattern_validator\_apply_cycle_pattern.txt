# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 257 à 284
# Type: Méthode de la classe BaccaratPatternValidator

    def _apply_cycle_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern cyclique"""
        cycle_length = pattern.get('cycle_length', 3)
        predicted_outcome = pattern.get('predicted_outcome')

        if len(test_data) < cycle_length:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        # Vérification position dans le cycle
        position = len(test_data) % cycle_length
        cycle_pattern = pattern.get('cycle_pattern', [])

        if position < len(cycle_pattern):
            expected_outcome = cycle_pattern[position]
            confidence = pattern.get('confidence', 0.5)

            return {
                'predicted_outcome': expected_outcome,
                'confidence': confidence,
                'reason': 'cycle_position',
                'cycle_position': position
            }

        return {
            'predicted_outcome': predicted_outcome,
            'confidence': pattern.get('confidence', 0.5) * 0.7,
            'reason': 'cycle_extrapolation'
        }