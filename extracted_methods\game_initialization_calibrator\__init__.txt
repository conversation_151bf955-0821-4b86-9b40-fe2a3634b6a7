# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\game_initialization_calibrator.py
# Lignes: 36 à 98
# Type: Méthode de la classe GameInitializationCalibrator

    def __init__(self):
        """Initialisation calibrateur début de partie"""
        
        # ═══════════════════════════════════════════════════════════════════
        # CONFIGURATION CALIBRATION DÉBUT PARTIE
        # ═══════════════════════════════════════════════════════════════════
        
        # Ressources système haute performance
        self.max_ram_gb = global_config.azr.max_ram_usage_gb
        self.cpu_cores = mp.cpu_count()
        self.available_ram = psutil.virtual_memory().total / (1024**3)
        
        # Paramètres partie 60 manches
        self.game_total_rounds = 60
        self.warmup_rounds = global_config.azr.warmup_phase_rounds  # 30
        self.optimal_window_start = global_config.azr.prediction_start_round  # 31
        self.optimal_window_end = global_config.azr.prediction_end_round  # 60
        
        # État calibration
        self.calibration_complete = False
        self.calibration_start_time = None
        self.calibration_duration = 0.0
        
        # Paramètres optimaux découverts
        self.optimal_parameters = {}
        self.calibration_history = deque(maxlen=100)
        
        # ═══════════════════════════════════════════════════════════════════
        # PARAMÈTRES CALIBRATION SPÉCIFIQUES 60 MANCHES
        # ═══════════════════════════════════════════════════════════════════
        
        # Plages optimisation pour partie 60 manches
        self.parameter_ranges_60_rounds = {
            'learning_rate': (0.05, 0.3),           # Optimisé pour 60 manches
            'confidence_threshold': (0.1, 0.8),     # Adaptatif selon phase
            'exploration_rate': (0.1, 0.6),         # Réduit pour partie courte
            'adaptation_rate': (0.08, 0.25),        # Réactif pour 60 manches
            'pattern_decay_rate': (0.01, 0.08),     # Adapté durée partie
            'warmup_exploration_factor': (0.6, 0.9), # Exploration intensive début
            'optimal_precision_factor': (0.8, 1.0),  # Précision maximale 31-60
            'memory_retention_factor': (0.7, 0.95)   # Rétention patterns récents
        }
        
        # Calibration par phase
        self.phase_specific_parameters = {
            'warmup': {
                'exploration_rate': 0.8,
                'learning_rate': 0.2,
                'confidence_threshold': 0.2
            },
            'optimal': {
                'exploration_rate': 0.3,
                'learning_rate': 0.15,
                'confidence_threshold': 0.4
            },
            'post_optimal': {
                'exploration_rate': 0.1,
                'learning_rate': 0.1,
                'confidence_threshold': 0.6
            }
        }
        
        logger.info(f"GameInitializationCalibrator initialisé - RAM: {self.available_ram:.1f}GB, CPU: {self.cpu_cores} cœurs")