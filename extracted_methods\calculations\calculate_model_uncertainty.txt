# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 742 à 756
# Type: Méthode de la classe UncertaintyCalculator

    def calculate_model_uncertainty(self, model_performance: Dict[str, float]) -> Dict[str, float]:
        """Calcule incertitude par modèle basée sur performance"""
        try:
            uncertainties = {}

            for model, accuracy in model_performance.items():
                # Incertitude inversement proportionnelle à la performance
                uncertainty = global_config.calculations.uncertainty_base - accuracy
                uncertainties[model] = min(global_config.calculations.confidence_max, max(global_config.calculations.confidence_min, uncertainty))

            return uncertainties

        except Exception as e:
            logger.error(f"Erreur incertitude modèles: {e}")
            return {}