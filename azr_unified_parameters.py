"""
🎯 AZR UNIFIED PARAMETER SYSTEM - CENTRALISATION COMPLÈTE
========================================================

Système unifié de gestion des paramètres pour le modèle AZR (Absolute Zero Reasoner).
Remplace tous les paramètres dispersés par un orchestrateur central cohérent.

PRINCIPES AZR APPLIQUÉS :
- Unified LLM : Un seul état partagé pour tous les composants
- Orchestrateur Central : Coordination de tous les sous-systèmes
- Paramètres Adaptatifs : Évolution selon les performances réelles
- Cohérence Mathématique : Relations dérivées, pas de valeurs magiques

ARCHITECTURE :
- ParameterOrchestrator : Chef d'orchestre central
- Shared State : État partagé entre tous les composants
- Dynamic Adaptation : Adaptation temps réel aux performances
- Mathematical Coherence : Relations mathématiques cohérentes
"""

import numpy as np
import logging
import json
import os
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from enum import Enum
import threading
from datetime import datetime

logger = logging.getLogger(__name__)

class AZRPhase(Enum):
    """Phases d'apprentissage AZR"""
    INITIALIZATION = "initialization"
    WARMUP = "warmup"
    LEARNING = "learning"
    OPTIMIZATION = "optimization"
    MASTERY = "mastery"

@dataclass
class AZRFoundationParameters:
    """
    🎯 PARAMÈTRES FONDAMENTAUX AZR
    Base mathématique pour tous les autres paramètres
    """
    # Paramètres de base (Golden Ratio et constantes mathématiques)
    golden_ratio: float = 0.618033988749  # φ - Base pour cohérence
    euler_constant: float = 0.577215664901  # γ - Base pour adaptation
    pi_factor: float = 0.318309886184  # 1/π - Base pour probabilités

    # Cibles de performance
    performance_target: float = 0.55  # Cible réaliste pour Baccarat
    confidence_target: float = 0.65  # Cible confiance optimale

    # Vitesses d'adaptation (basées sur constantes mathématiques)
    base_learning_rate: float = field(init=False)
    adaptation_speed: float = field(init=False)
    volatility_sensitivity: float = field(init=False)

    def __post_init__(self):
        """Calcule paramètres dérivés avec cohérence mathématique"""
        self.base_learning_rate = self.euler_constant * 0.1  # ≈ 0.0577
        self.adaptation_speed = self.golden_ratio * 0.2  # ≈ 0.1236
        self.volatility_sensitivity = self.pi_factor * 5  # ≈ 1.5915

@dataclass
class AZRSharedState:
    """
    🎯 ÉTAT PARTAGÉ AZR (UNIFIED LLM)
    État central synchronisé entre tous les composants
    """
    # Performance actuelle
    current_performance: float = 0.5
    current_confidence: float = 0.5
    current_volatility: float = 0.0

    # Phase d'apprentissage
    current_phase: AZRPhase = AZRPhase.INITIALIZATION
    iterations_in_phase: int = 0
    total_iterations: int = 0

    # Momentum et tendances
    performance_momentum: float = 0.0
    confidence_momentum: float = 0.0
    adaptation_momentum: float = 0.0

    # Métriques de cohérence
    coherence_score: float = 1.0
    stability_index: float = 1.0
    learning_efficiency: float = 0.5

    # Historique récent (pour calculs adaptatifs)
    recent_performances: List[float] = field(default_factory=list)
    recent_confidences: List[float] = field(default_factory=list)
    recent_predictions: List[Dict] = field(default_factory=list)

class AZRParameterOrchestrator:
    """
    🎯 ORCHESTRATEUR CENTRAL DES PARAMÈTRES AZR

    Chef d'orchestre qui coordonne tous les paramètres du système.
    Implémente les principes du modèle Absolute Zero Reasoner :
    - Unified Model avec état partagé
    - Adaptation dynamique aux performances
    - Cohérence mathématique globale
    """

    def __init__(self, config_path: Optional[str] = None):
        """Initialise l'orchestrateur avec paramètres fondamentaux"""
        self.foundation = AZRFoundationParameters()
        self.shared_state = AZRSharedState()
        self.lock = threading.Lock()  # Thread safety

        # Historique pour adaptation
        self.performance_history = []
        self.adaptation_history = []
        self.parameter_evolution = {}

        # Chargement configuration si fournie
        if config_path and os.path.exists(config_path):
            self.load_configuration(config_path)

        logger.info("🎯 AZR Parameter Orchestrator initialisé - Unified Model actif")

    def get_wait_parameters(self) -> Dict[str, float]:
        """
        🚫 PARAMÈTRES SYSTÈME WAIT ADAPTATIFS
        Calculs basés sur performance réelle et cohérence mathématique
        """
        with self.lock:
            # Base adaptative selon performance
            performance_gap = self.foundation.performance_target - self.shared_state.current_performance

            # Seuil adaptatif avec golden ratio
            base_threshold = self.foundation.confidence_target * self.foundation.golden_ratio
            adaptation_factor = performance_gap * self.foundation.adaptation_speed

            adaptive_threshold = base_threshold + adaptation_factor

            # Limites cohérentes
            min_threshold = base_threshold * 0.3
            max_threshold = base_threshold * 1.8

            # Ajustement volatilité
            volatility_penalty = self.shared_state.current_volatility * self.foundation.volatility_sensitivity * 0.1
            final_threshold = np.clip(adaptive_threshold - volatility_penalty, min_threshold, max_threshold)

            return {
                'base_wait_threshold': final_threshold,
                'adaptation_rate': self.foundation.adaptation_speed,
                'performance_window': max(3, int(8 * self.foundation.golden_ratio)),  # ≈ 5
                'min_wait_threshold': min_threshold,
                'max_wait_threshold': max_threshold,
                'volatility_factor': self.foundation.volatility_sensitivity,
                'streak_penalty_factor': 2.0 / self.foundation.golden_ratio,  # ≈ 3.236
                'performance_target': self.foundation.performance_target,
                'adaptation_aggressiveness': 1.0 / self.foundation.pi_factor  # ≈ 3.14
            }

    def get_confidence_parameters(self) -> Dict[str, float]:
        """
        📊 PARAMÈTRES CONFIANCE COHÉRENTS
        Basés sur état partagé et relations mathématiques
        """
        with self.lock:
            momentum_factor = self.shared_state.confidence_momentum
            stability_bonus = self.shared_state.stability_index * 0.1

            return {
                'base_confidence_threshold': self.foundation.confidence_target * self.foundation.golden_ratio,
                'adjustment_limit': self.foundation.base_learning_rate * 4.0,
                'learning_rate': self.foundation.base_learning_rate,
                'momentum_factor': momentum_factor,
                'stability_bonus': stability_bonus,
                'coherence_factor': self.foundation.golden_ratio,
                'min_confidence': 0.01,
                'max_confidence': 0.99,
                'target_confidence': self.foundation.confidence_target
            }

    def get_streak_parameters(self) -> Dict[str, Any]:
        """
        🔄 PARAMÈTRES DÉTECTION STREAKS
        Streaks significatives à partir de 4 (selon spécifications)
        """
        with self.lock:
            return {
                'significant_streak_length': 4,  # Spécification utilisateur
                'significant_alternation_length': 4,  # Spécification utilisateur
                'anti_streak_factor': 2.0 / self.foundation.golden_ratio,
                'continuation_base_prob': self.foundation.golden_ratio,
                'length_penalty_rate': self.foundation.base_learning_rate * 2,
                'volatility_adjustment': self.foundation.volatility_sensitivity,
                'phase_adjustment': self._get_phase_adjustment(),
                'momentum_influence': self.shared_state.performance_momentum * 0.1
            }

    def get_pattern_parameters(self) -> Dict[str, float]:
        """
        🎨 PARAMÈTRES DÉTECTION PATTERNS
        Cohérents avec l'état global du système
        """
        with self.lock:
            learning_boost = self.shared_state.learning_efficiency * 0.2

            return {
                'alternation_threshold': 4,  # Spécification utilisateur
                'pattern_strength_factor': self.foundation.golden_ratio,
                'confidence_boost': self.foundation.base_learning_rate * 3 + learning_boost,
                'validation_threshold': self.foundation.confidence_target * 0.8,
                'min_pattern_length': 2,
                'max_pattern_length': int(12 * self.foundation.golden_ratio),  # ≈ 7
                'decay_rate': self.foundation.euler_constant * 0.1,
                'coherence_requirement': self.shared_state.coherence_score * 0.8
            }

    def get_learning_parameters(self) -> Dict[str, float]:
        """
        🧠 PARAMÈTRES APPRENTISSAGE ADAPTATIFS
        Évoluent selon la phase d'apprentissage
        """
        with self.lock:
            phase_multiplier = self._get_phase_multiplier()

            return {
                'learning_rate': self.foundation.base_learning_rate * phase_multiplier,
                'exploration_rate': self.foundation.pi_factor * phase_multiplier,
                'exploitation_rate': 1.0 - (self.foundation.pi_factor * phase_multiplier),
                'adaptation_rate': self.foundation.adaptation_speed,
                'momentum_decay': self.foundation.euler_constant * 0.1,
                'performance_target': self.foundation.performance_target,
                'confidence_target': self.foundation.confidence_target,
                'phase': self.shared_state.current_phase.value,
                'iterations_in_phase': self.shared_state.iterations_in_phase
            }

    def get_model_parameters(self) -> Dict[str, Any]:
        """
        🤖 PARAMÈTRES MODÈLES (LSTM, LGBM, Markov)
        Adaptés selon performance et phase
        """
        with self.lock:
            efficiency_factor = self.shared_state.learning_efficiency

            return {
                # LSTM
                'lstm_sequence_length': max(5, int(10 * self.foundation.golden_ratio)),
                'lstm_hidden_size': int(32 * (1 + efficiency_factor * 0.5)),
                'lstm_learning_rate': self.foundation.base_learning_rate * 2,

                # LGBM
                'lgbm_n_estimators': max(30, int(50 * (1 + efficiency_factor))),
                'lgbm_max_depth': max(3, int(5 * self.foundation.golden_ratio)),
                'lgbm_learning_rate': self.foundation.base_learning_rate * 10,

                # Markov
                'markov_order': max(2, int(3 * self.foundation.golden_ratio)),
                'markov_smoothing': self.foundation.euler_constant * 0.1,

                # Ensemble
                'ensemble_weights': self._calculate_adaptive_weights(),
                'rebalancing_frequency': max(5, int(10 * self.foundation.golden_ratio))
            }

    def update_shared_state(self, prediction_result: Dict[str, Any], actual_outcome: Optional[int] = None):
        """
        🔄 MET À JOUR L'ÉTAT PARTAGÉ (UNIFIED LLM)
        Synchronise tous les composants avec nouvelles données
        """
        with self.lock:
            # Mise à jour historique
            self.shared_state.recent_predictions.append(prediction_result)
            if len(self.shared_state.recent_predictions) > 20:
                self.shared_state.recent_predictions.pop(0)

            # Mise à jour confiance
            confidence = prediction_result.get('confidence', 0.5)
            self.shared_state.recent_confidences.append(confidence)
            if len(self.shared_state.recent_confidences) > 20:
                self.shared_state.recent_confidences.pop(0)

            self.shared_state.current_confidence = np.mean(self.shared_state.recent_confidences[-5:])

            # Mise à jour performance si résultat connu
            if actual_outcome is not None:
                predicted = prediction_result.get('predicted_outcome', 0)
                accuracy = 1.0 if predicted == actual_outcome else 0.0

                self.shared_state.recent_performances.append(accuracy)
                if len(self.shared_state.recent_performances) > 20:
                    self.shared_state.recent_performances.pop(0)

                self.shared_state.current_performance = np.mean(self.shared_state.recent_performances[-5:])

            # Calcul volatilité
            if len(self.shared_state.recent_performances) >= 3:
                self.shared_state.current_volatility = np.std(self.shared_state.recent_performances[-10:])

            # Mise à jour momentum
            self._update_momentum()

            # Mise à jour phase
            self._update_learning_phase()

            # Calcul métriques de cohérence
            self._update_coherence_metrics()

            # Incrémentation compteurs
            self.shared_state.total_iterations += 1
            self.shared_state.iterations_in_phase += 1

            logger.debug(f"🎯 État partagé mis à jour: perf={self.shared_state.current_performance:.3f}, "
                        f"conf={self.shared_state.current_confidence:.3f}, phase={self.shared_state.current_phase.value}")

    def _update_momentum(self):
        """Calcule momentum des métriques"""
        if len(self.shared_state.recent_performances) >= 2:
            recent_trend = (self.shared_state.recent_performances[-1] -
                           self.shared_state.recent_performances[-2])
            self.shared_state.performance_momentum = (
                0.8 * self.shared_state.performance_momentum + 0.2 * recent_trend
            )

        if len(self.shared_state.recent_confidences) >= 2:
            conf_trend = (self.shared_state.recent_confidences[-1] -
                         self.shared_state.recent_confidences[-2])
            self.shared_state.confidence_momentum = (
                0.8 * self.shared_state.confidence_momentum + 0.2 * conf_trend
            )

    def _update_learning_phase(self):
        """Met à jour la phase d'apprentissage selon performance"""
        current_perf = self.shared_state.current_performance
        iterations = self.shared_state.iterations_in_phase

        # Transitions de phase basées sur performance et temps
        if self.shared_state.current_phase == AZRPhase.INITIALIZATION:
            if iterations >= 5:
                self.shared_state.current_phase = AZRPhase.WARMUP
                self.shared_state.iterations_in_phase = 0

        elif self.shared_state.current_phase == AZRPhase.WARMUP:
            if current_perf > 0.52 or iterations >= 15:
                self.shared_state.current_phase = AZRPhase.LEARNING
                self.shared_state.iterations_in_phase = 0

        elif self.shared_state.current_phase == AZRPhase.LEARNING:
            if current_perf > 0.58 or iterations >= 30:
                self.shared_state.current_phase = AZRPhase.OPTIMIZATION
                self.shared_state.iterations_in_phase = 0

        elif self.shared_state.current_phase == AZRPhase.OPTIMIZATION:
            if current_perf > 0.65 or iterations >= 50:
                self.shared_state.current_phase = AZRPhase.MASTERY
                self.shared_state.iterations_in_phase = 0

    def _update_coherence_metrics(self):
        """Calcule métriques de cohérence du système"""
        if len(self.shared_state.recent_performances) >= 3:
            # Cohérence = alignement confiance/performance
            recent_perf = np.mean(self.shared_state.recent_performances[-5:])
            recent_conf = np.mean(self.shared_state.recent_confidences[-5:])

            coherence = 1.0 - abs(recent_perf - recent_conf)
            self.shared_state.coherence_score = (
                0.9 * self.shared_state.coherence_score + 0.1 * coherence
            )

            # Stabilité = inverse de la volatilité
            stability = 1.0 / (1.0 + self.shared_state.current_volatility)
            self.shared_state.stability_index = (
                0.9 * self.shared_state.stability_index + 0.1 * stability
            )

            # Efficacité d'apprentissage
            if len(self.shared_state.recent_performances) >= 10:
                early_perf = np.mean(self.shared_state.recent_performances[:5])
                late_perf = np.mean(self.shared_state.recent_performances[-5:])
                learning_trend = max(0, late_perf - early_perf)

                self.shared_state.learning_efficiency = (
                    0.95 * self.shared_state.learning_efficiency + 0.05 * learning_trend
                )

    def _get_phase_adjustment(self) -> float:
        """Retourne ajustement selon phase d'apprentissage"""
        phase_adjustments = {
            AZRPhase.INITIALIZATION: 1.5,  # Plus conservateur
            AZRPhase.WARMUP: 1.2,
            AZRPhase.LEARNING: 1.0,
            AZRPhase.OPTIMIZATION: 0.8,
            AZRPhase.MASTERY: 0.6  # Plus agressif
        }
        return phase_adjustments.get(self.shared_state.current_phase, 1.0)

    def _get_phase_multiplier(self) -> float:
        """Retourne multiplicateur selon phase"""
        phase_multipliers = {
            AZRPhase.INITIALIZATION: 2.0,  # Apprentissage rapide
            AZRPhase.WARMUP: 1.5,
            AZRPhase.LEARNING: 1.0,
            AZRPhase.OPTIMIZATION: 0.7,
            AZRPhase.MASTERY: 0.5  # Apprentissage fin
        }
        return phase_multipliers.get(self.shared_state.current_phase, 1.0)

    def _calculate_adaptive_weights(self) -> Dict[str, float]:
        """Calcule poids adaptatifs pour ensemble"""
        # Poids basés sur performance et phase
        base_weights = {'lstm': 0.33, 'lgbm': 0.33, 'markov': 0.34}

        # Ajustement selon phase
        if self.shared_state.current_phase in [AZRPhase.INITIALIZATION, AZRPhase.WARMUP]:
            # Favorise Markov au début (plus simple)
            return {'lstm': 0.25, 'lgbm': 0.25, 'markov': 0.50}
        elif self.shared_state.current_phase == AZRPhase.MASTERY:
            # Favorise LSTM en maîtrise (plus sophistiqué)
            return {'lstm': 0.50, 'lgbm': 0.30, 'markov': 0.20}

        return base_weights

    def get_all_parameters(self) -> Dict[str, Any]:
        """
        🎯 RETOURNE TOUS LES PARAMÈTRES UNIFIÉS
        Interface principale pour accès aux paramètres
        """
        with self.lock:
            return {
                'wait': self.get_wait_parameters(),
                'confidence': self.get_confidence_parameters(),
                'streaks': self.get_streak_parameters(),
                'patterns': self.get_pattern_parameters(),
                'learning': self.get_learning_parameters(),
                'models': self.get_model_parameters(),
                'shared_state': {
                    'performance': self.shared_state.current_performance,
                    'confidence': self.shared_state.current_confidence,
                    'volatility': self.shared_state.current_volatility,
                    'phase': self.shared_state.current_phase.value,
                    'coherence': self.shared_state.coherence_score,
                    'stability': self.shared_state.stability_index,
                    'learning_efficiency': self.shared_state.learning_efficiency,
                    'total_iterations': self.shared_state.total_iterations
                },
                'foundation': {
                    'golden_ratio': self.foundation.golden_ratio,
                    'performance_target': self.foundation.performance_target,
                    'confidence_target': self.foundation.confidence_target,
                    'base_learning_rate': self.foundation.base_learning_rate,
                    'adaptation_speed': self.foundation.adaptation_speed
                }
            }

    def save_configuration(self, path: str):
        """Sauvegarde configuration complète"""
        config_data = {
            'foundation': self.foundation.__dict__,
            'shared_state': {
                **self.shared_state.__dict__,
                'current_phase': self.shared_state.current_phase.value
            },
            'performance_history': self.performance_history[-100:],  # Garde les 100 dernières
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0'
        }

        with open(path, 'w') as f:
            json.dump(config_data, f, indent=2, default=str)

        logger.info(f"🎯 Configuration AZR unifiée sauvegardée: {path}")

    def load_configuration(self, path: str):
        """Charge configuration sauvegardée"""
        try:
            with open(path, 'r') as f:
                config_data = json.load(f)

            # Restaure fondation
            foundation_data = config_data.get('foundation', {})
            for key, value in foundation_data.items():
                if hasattr(self.foundation, key):
                    setattr(self.foundation, key, value)

            # Restaure état partagé
            state_data = config_data.get('shared_state', {})
            for key, value in state_data.items():
                if key == 'current_phase':
                    self.shared_state.current_phase = AZRPhase(value)
                elif hasattr(self.shared_state, key):
                    setattr(self.shared_state, key, value)

            # Restaure historique
            self.performance_history = config_data.get('performance_history', [])

            logger.info(f"🎯 Configuration AZR unifiée chargée: {path}")

        except Exception as e:
            logger.error(f"Erreur chargement configuration: {e}")

    def get_diagnostic_info(self) -> Dict[str, Any]:
        """Retourne informations de diagnostic"""
        with self.lock:
            return {
                'orchestrator_status': 'active',
                'unified_model_state': 'synchronized',
                'parameter_coherence': self.shared_state.coherence_score,
                'system_stability': self.shared_state.stability_index,
                'learning_phase': self.shared_state.current_phase.value,
                'total_iterations': self.shared_state.total_iterations,
                'performance_trend': self.shared_state.performance_momentum,
                'confidence_trend': self.shared_state.confidence_momentum,
                'recent_performance': self.shared_state.current_performance,
                'recent_confidence': self.shared_state.current_confidence,
                'volatility_index': self.shared_state.current_volatility,
                'mathematical_foundation': {
                    'golden_ratio_usage': self.foundation.golden_ratio,
                    'euler_constant_usage': self.foundation.euler_constant,
                    'pi_factor_usage': self.foundation.pi_factor
                }
            }


# ═══════════════════════════════════════════════════════════════════
# INSTANCE GLOBALE (SINGLETON PATTERN)
# ═══════════════════════════════════════════════════════════════════

_azr_orchestrator = None
_orchestrator_lock = threading.Lock()

def get_azr_orchestrator() -> AZRParameterOrchestrator:
    """
    Retourne l'instance globale de l'orchestrateur AZR (Singleton)
    Thread-safe pour utilisation multi-thread
    """
    global _azr_orchestrator

    if _azr_orchestrator is None:
        with _orchestrator_lock:
            if _azr_orchestrator is None:  # Double-check locking
                _azr_orchestrator = AZRParameterOrchestrator()
                logger.info("🎯 AZR Orchestrator global initialisé")

    return _azr_orchestrator

def reset_azr_orchestrator():
    """Réinitialise l'orchestrateur (pour tests)"""
    global _azr_orchestrator
    with _orchestrator_lock:
        _azr_orchestrator = None
        logger.info("🎯 AZR Orchestrator global réinitialisé")

# ═══════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES POUR COMPATIBILITÉ
# ═══════════════════════════════════════════════════════════════════

def get_wait_params() -> Dict[str, float]:
    """Fonction de compatibilité pour système WAIT"""
    return get_azr_orchestrator().get_wait_parameters()

def get_confidence_params() -> Dict[str, float]:
    """Fonction de compatibilité pour confiance"""
    return get_azr_orchestrator().get_confidence_parameters()

def get_streak_params() -> Dict[str, Any]:
    """Fonction de compatibilité pour streaks"""
    return get_azr_orchestrator().get_streak_parameters()

def get_pattern_params() -> Dict[str, float]:
    """Fonction de compatibilité pour patterns"""
    return get_azr_orchestrator().get_pattern_parameters()

def update_azr_state(prediction_result: Dict[str, Any], actual_outcome: Optional[int] = None):
    """Fonction de compatibilité pour mise à jour état"""
    return get_azr_orchestrator().update_shared_state(prediction_result, actual_outcome)

# Export des classes et fonctions principales
__all__ = [
    'AZRParameterOrchestrator',
    'AZRFoundationParameters',
    'AZRSharedState',
    'AZRPhase',
    'get_azr_orchestrator',
    'reset_azr_orchestrator',
    'get_wait_params',
    'get_confidence_params',
    'get_streak_params',
    'get_pattern_params',
    'update_azr_state'
]
