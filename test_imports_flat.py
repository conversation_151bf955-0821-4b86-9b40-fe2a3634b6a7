#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TEST IMPORTS VERSION FLAT
=========================

Test des imports pour la version avec tous les fichiers dans le même dossier.
"""

import sys
import traceback

def test_imports():
    """Test tous les imports nécessaires"""
    print("🧪 Test des imports - Version Flat")
    print("=" * 50)
    
    # Test imports de base
    try:
        print("📦 Test imports Python de base...")
        import numpy as np
        import tkinter as tk
        import logging
        print("✅ Imports de base OK")
    except Exception as e:
        print(f"❌ Erreur imports de base: {e}")
        return False
    
    # Test import parameters
    try:
        print("📊 Test import parameters...")
        from parameters import global_config
        print(f"✅ Parameters OK - Version: {global_config.system.version}")
    except Exception as e:
        print(f"❌ Erreur import parameters: {e}")
        traceback.print_exc()
        return False
    
    # Test import calculations
    try:
        print("🔢 Test import calculations...")
        from calculations import global_confidence_calculator
        print("✅ Calculations OK")
    except Exception as e:
        print(f"❌ Erreur import calculations: {e}")
        traceback.print_exc()
        return False
    
    # Test imports AZR Core
    try:
        print("🧠 Test imports AZR Core...")
        from pattern_proposer import BaccaratPatternProposer
        from pattern_validator import BaccaratPatternValidator
        from adaptive_reasoner import BaccaratAdaptiveReasoner
        from self_play_engine import BaccaratSelfPlayEngine
        print("✅ AZR Core OK")
    except Exception as e:
        print(f"❌ Erreur imports AZR Core: {e}")
        traceback.print_exc()
        return False
    
    # Test import models
    try:
        print("🎮 Test import models...")
        from models import AZRBaccaratPredictor
        print("✅ Models OK")
    except Exception as e:
        print(f"❌ Erreur import models: {e}")
        traceback.print_exc()
        return False
    
    # Test import interface
    try:
        print("🖥️ Test import interface...")
        from main_interface import BaccaratPredictorApp
        print("✅ Interface OK")
    except Exception as e:
        print(f"❌ Erreur import interface: {e}")
        traceback.print_exc()
        return False
    
    # Test autres modules
    try:
        print("🔧 Test autres modules...")
        from new_game_handler import NewGameHandler
        from game_initialization_calibrator import GameInitializationCalibrator
        from realtime_calibrator import RealtimeCalibrator
        from unlimited_threshold_manager import UnlimitedThresholdManager
        from resources import get_system_info
        from chargementsauvegarde import save_game_data
        print("✅ Autres modules OK")
    except Exception as e:
        print(f"❌ Erreur autres modules: {e}")
        traceback.print_exc()
        return False
    
    print("\n🎯 TOUS LES IMPORTS FONCTIONNENT !")
    print("=" * 50)
    return True

def test_basic_functionality():
    """Test fonctionnalité de base"""
    try:
        print("\n🔬 Test fonctionnalité de base...")
        
        # Test création objets principaux
        from parameters import global_config
        from pattern_proposer import BaccaratPatternProposer
        from adaptive_reasoner import BaccaratAdaptiveReasoner
        
        # Test création instances
        proposer = BaccaratPatternProposer()
        reasoner = BaccaratAdaptiveReasoner()
        
        print("✅ Création objets AZR OK")
        
        # Test configuration
        print(f"📊 Configuration: {global_config.azr.total_rounds} manches")
        print(f"🎯 Seuil confiance: {global_config.azr.confidence_threshold}")
        
        print("✅ Fonctionnalité de base OK")
        return True
        
    except Exception as e:
        print(f"❌ Erreur fonctionnalité: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        # Test imports
        imports_ok = test_imports()
        
        if imports_ok:
            # Test fonctionnalité
            functionality_ok = test_basic_functionality()
            
            if functionality_ok:
                print("\n🏆 TOUS LES TESTS RÉUSSIS !")
                print("🎯 Le programme est prêt à fonctionner en version flat")
                sys.exit(0)
            else:
                print("\n❌ Erreur fonctionnalité")
                sys.exit(1)
        else:
            print("\n❌ Erreur imports")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Erreur fatale: {e}")
        traceback.print_exc()
        sys.exit(1)
