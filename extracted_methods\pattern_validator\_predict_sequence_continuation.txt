# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 467 à 478
# Type: Méthode de la classe BaccaratPatternValidator

    def _predict_sequence_continuation(self, sequence: List[int], test_data: List[int]) -> int:
        """Prédit continuation d'une séquence"""
        # Logique simple: alternance ou continuation basée sur pattern
        if len(sequence) >= 2:
            if sequence[-1] == sequence[-2]:
                # Série en cours, prédire rupture
                return 1 - sequence[-1]
            else:
                # Alternance, prédire continuation
                return 1 - sequence[-1]

        return 1 - sequence[-1] if sequence else 0