# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 703 à 731
# Type: Méthode de la classe RealtimeCalibrator

    def _calculate_parameter_interaction(self, param1: str, param2: str) -> float:
        """Calcule interaction entre deux paramètres"""
        try:
            base_performance = self._calculate_current_performance()

            # Test modification simultanée
            test_params = self.calibrated_parameters.copy()

            # Perturbations
            range1 = self.parameter_ranges[param1]
            range2 = self.parameter_ranges[param2]

            delta1 = (range1[1] - range1[0]) * 0.05
            delta2 = (range2[1] - range2[0]) * 0.05

            # Test interaction positive
            test_params[param1] = np.clip(test_params[param1] + delta1, range1[0], range1[1])
            test_params[param2] = np.clip(test_params[param2] + delta2, range2[0], range2[1])

            interaction_performance = self._simulate_performance_with_parameters(test_params)

            # Score interaction
            interaction_score = interaction_performance - base_performance

            return interaction_score

        except Exception as e:
            logger.error(f"Erreur calcul interaction {param1}-{param2}: {e}")
            return 0.0