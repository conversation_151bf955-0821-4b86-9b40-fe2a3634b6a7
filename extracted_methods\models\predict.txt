# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 356 à 399
# Type: Méthode de la classe LSTMModel

    def predict(self, sequence):
        """Prédiction LSTM avec confiance basée sur variance séquentielle"""
        try:
            if len(sequence) < 2:
                return 0.5, 0.3  # Prédiction neutre, confiance faible

            # 📊 ANALYSE PATTERNS SÉQUENTIELS LSTM
            X = self._prepare_sequences(sequence)

            # Simulation LSTM : analyse patterns temporels
            # Pattern 1: Tendance récente
            recent_trend = np.mean(sequence[-min(5, len(sequence)):])

            # Pattern 2: Oscillations périodiques
            if len(sequence) >= 4:
                oscillation = np.std(sequence[-4:])
            else:
                oscillation = 0.5

            # Pattern 3: Momentum directionnel
            if len(sequence) >= 3:
                momentum = (sequence[-1] - sequence[-3]) / 2
            else:
                momentum = 0

            # 🧠 PRÉDICTION LSTM SIMULÉE
            lstm_prediction = np.clip(
                recent_trend + momentum * 0.3 + (0.5 - oscillation) * 0.2,
                0.1, 0.9
            )

            # 📈 CONFIANCE BASÉE SUR STABILITÉ SÉQUENTIELLE
            if len(sequence) >= 5:
                sequence_variance = np.var(sequence[-5:])
                lstm_confidence = max(0.2, min(0.8, 1.0 - sequence_variance * 2))
            else:
                lstm_confidence = 0.4

            self.prediction_history.append(lstm_prediction)
            return float(lstm_prediction), float(lstm_confidence)

        except Exception as e:
            logger.error(f"Erreur LSTM predict: {e}")
            return 0.5, 0.3