# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 82 à 153
# Type: Méthode de la classe BaccaratPredictorApp

    def setup_ui(self):
        """Configure l'interface utilisateur"""

        # Style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#1a1a2e', foreground='white')
        style.configure('Info.TLabel', font=('Arial', 12), background='#1a1a2e', foreground='#00ff88')
        style.configure('Prediction.TLabel', font=('Arial', 14, 'bold'), background='#1a1a2e', foreground='#ffaa00')

        # Titre principal
        title_frame = tk.Frame(self.root, bg='#1a1a2e')
        title_frame.pack(pady=20)

        title_label = ttk.Label(title_frame, text="🎯 PRÉDICTEUR BACCARAT AZR AUTHENTIQUE", style='Title.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="Système AZR avec Dual Role Architecture & TRR++", style='Info.TLabel')
        subtitle_label.pack(pady=5)

        # Frame principal
        main_frame = tk.Frame(self.root, bg='#1a1a2e')
        main_frame.pack(expand=True, fill='both', padx=20, pady=10)

        # Zone prédiction
        pred_frame = tk.LabelFrame(main_frame, text="🔮 PRÉDICTION AZR", bg='#2d2d44', fg='white', font=('Arial', 12, 'bold'))
        pred_frame.pack(fill='x', pady=10)

        self.prediction_label = ttk.Label(pred_frame, text="Cliquez sur 'COMMENCER' pour débuter", style='Prediction.TLabel')
        self.prediction_label.pack(pady=20)

        # Boutons de contrôle
        control_frame = tk.Frame(main_frame, bg='#1a1a2e')
        control_frame.pack(fill='x', pady=10)

        # Bouton prédire (seulement pour la première manche)
        self.predict_btn = tk.Button(control_frame, text="🎯 COMMENCER", command=self.predict_next,
                                   bg='#00aa44', fg='white', font=('Arial', 12, 'bold'), width=15)
        self.predict_btn.pack(side='left', padx=5)

        # Boutons résultats (initialement désactivés)
        self.player_btn = tk.Button(control_frame, text="👤 PLAYER", command=lambda: self.record_result(0),
                                  bg='#0066cc', fg='white', font=('Arial', 12, 'bold'), width=15, state='disabled')
        self.player_btn.pack(side='left', padx=5)

        self.banker_btn = tk.Button(control_frame, text="🏦 BANKER", command=lambda: self.record_result(1),
                                  bg='#cc6600', fg='white', font=('Arial', 12, 'bold'), width=15, state='disabled')
        self.banker_btn.pack(side='left', padx=5)

        # Bouton reset
        self.reset_btn = tk.Button(control_frame, text="🔄 RESET", command=self.reset_game,
                                 bg='#cc0044', fg='white', font=('Arial', 12, 'bold'), width=15)
        self.reset_btn.pack(side='right', padx=5)

        # Indicateur statut
        self.status_label = tk.Label(control_frame, text="Cliquez sur 'COMMENCER' pour débuter",
                                   bg='#1a1a2e', fg='#ffaa00', font=('Arial', 10))
        self.status_label.pack(side='right', padx=20)

        # Zone informations
        info_frame = tk.LabelFrame(main_frame, text="📊 INFORMATIONS AZR", bg='#2d2d44', fg='white', font=('Arial', 12, 'bold'))
        info_frame.pack(fill='both', expand=True, pady=10)

        self.info_text = tk.Text(info_frame, bg='#1a1a2e', fg='#00ff88', font=('Courier', 10), wrap='word')
        self.info_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Labels informatifs
        self.method_label = ttk.Label(info_frame, text="Méthode: AZR Authentique", style='Info.TLabel')
        self.method_label.pack(anchor='w', padx=10)

        self.azr_info_label = ttk.Label(info_frame, text="AZR: Système initialisé", style='Info.TLabel')
        self.azr_info_label.pack(anchor='w', padx=10)