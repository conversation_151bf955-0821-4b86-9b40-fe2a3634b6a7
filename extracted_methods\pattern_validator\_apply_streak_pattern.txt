# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 232 à 255
# Type: Méthode de la classe BaccaratPatternValidator

    def _apply_streak_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern de série"""
        if len(test_data) < 2:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        predicted_outcome = pattern.get('predicted_outcome')
        continuation_prob = pattern.get('continuation_probability', 0.5)

        # Vérification série actuelle
        current_streak = self._get_current_streak_info(test_data)

        if current_streak['length'] >= 2:
            # Ajustement confiance basé sur longueur série
            length_factor = min(current_streak['length'] / 5.0, 1.0)
            confidence = pattern.get('confidence', 0.5) * length_factor
        else:
            confidence = pattern.get('confidence', 0.5) * 0.5

        return {
            'predicted_outcome': predicted_outcome,
            'confidence': confidence,
            'reason': 'streak_analysis',
            'current_streak_length': current_streak['length']
        }