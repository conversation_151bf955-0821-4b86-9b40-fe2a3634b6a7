# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 266 à 281
# Type: Méthode de la classe BaccaratPredictorApp

    def _auto_generate_next_prediction(self):
        """Génère automatiquement la prédiction pour la manche suivante"""
        try:
            # Indication visuelle de génération en cours
            self.status_label.config(text="🤖 Génération prédiction automatique...")
            self.prediction_label.config(text="🔄 Analyse AZR en cours...\nVeuillez patienter")

            # Désactiver temporairement boutons résultats
            self.player_btn.config(state='disabled')
            self.banker_btn.config(state='disabled')

            # Attendre un court instant pour que l'utilisateur voie le résultat
            self.root.after(800, self._execute_auto_prediction)

        except Exception as e:
            logger.error(f"Erreur génération auto prédiction: {e}")