# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 492 à 506
# Type: Méthode de la classe BaccaratPatternValidator

    def _get_current_streak_info(self, data: List[int]) -> Dict:
        """Obtient informations sur la série actuelle"""
        if not data:
            return {'length': 0, 'outcome': None}

        current_outcome = data[-1]
        length = 1

        for i in range(len(data) - 2, -1, -1):
            if data[i] == current_outcome:
                length += 1
            else:
                break

        return {'length': length, 'outcome': current_outcome}