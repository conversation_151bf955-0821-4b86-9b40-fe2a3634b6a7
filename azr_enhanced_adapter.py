"""
Adaptateur AZR Amélioré selon Recherches Scientifiques
=====================================================

Ce module adapte notre système AZR existant avec les améliorations
découvertes dans les recherches approfondies.

Référence: recherches_azr_entrainement_hyperparametres.txt
"""

import logging
import numpy as np
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from collections import deque
import json

logger = logging.getLogger(__name__)

@dataclass
class AZREnhancedHyperparameters:
    """Hyperparamètres AZR optimisés selon recherches"""

    # Learning rates différenciés selon recherches
    learning_rate_proposer: float = 0.0002      # 2e-4 optimal pour Proposer
    learning_rate_solver: float = 0.0005        # 5e-4 optimal pour Solver
    learning_rate_baseline: float = 0.001       # 1e-3 pour baseline TRR++

    # Batch sizes optimisés
    batch_size_proposer: int = 32               # 16-64 tâches par batch
    batch_size_solver: int = 64                 # 32-128 solutions par batch
    gradient_accumulation_steps: int = 4        # 2-8 steps

    # Buffers selon recommandations
    task_buffer_size: int = 10000               # 10,000-50,000 tâches
    experience_replay_size: int = 2000          # 1,000-5,000 épisodes
    curriculum_buffer_size: int = 1000          # 500-2,000 tâches par niveau

    # Curriculum learning adaptatif
    curriculum_difficulty_initial: float = 0.3  # 0.2-0.4 difficulté initiale
    difficulty_progression_rate: float = 0.03   # 0.01-0.05 par epoch
    mastery_threshold: float = 0.78             # 70-85% seuil de maîtrise
    performance_window: int = 200               # 100-500 épisodes

    # Récompenses optimisées
    reward_optimal_task: float = 1.0            # Tâche optimale
    penalty_easy_task: float = -0.5             # Tâche trop facile
    penalty_impossible_task: float = -1.0       # Tâche impossible
    diversity_bonus_range: tuple = (0.1, 0.3)   # Bonus diversité

    # Stabilité self-play
    target_network_update_rate: float = 0.005   # Soft update τ=0.005
    gradient_clipping: float = 1.0              # 0.5-2.0
    epsilon_decay: float = 0.997                # 0.995-0.999

    # Exploration vs exploitation
    epsilon_initial: float = 0.85               # 0.8-0.9
    epsilon_final: float = 0.08                 # 0.05-0.1
    temperature_sampling: float = 1.0           # 0.7-1.2

class AZREnhancedMetrics:
    """Métriques AZR étendues selon recherches"""

    def __init__(self):
        # Métriques primaires selon recherches
        self.task_success_rate = 0.0
        self.code_execution_accuracy = 0.0
        self.mathematical_reasoning_score = 0.0
        self.cross_domain_transfer = 0.0

        # Métriques secondaires
        self.learning_efficiency = 0.0
        self.task_diversity = 0.0
        self.computational_cost = 0.0
        self.memory_usage = 0.0

        # Métriques de robustesse
        self.performance_variance = 0.0
        self.stability_score = 0.0
        self.generalization_score = 0.0
        self.reward_hacking_resistance = 0.0

        # Historiques pour calculs
        self.success_history = deque(maxlen=100)
        self.efficiency_history = deque(maxlen=100)
        self.diversity_history = deque(maxlen=100)

    def update_primary_metrics(self, task_success: bool, execution_accuracy: float,
                             reasoning_score: float, transfer_score: float):
        """Met à jour métriques primaires"""
        self.success_history.append(1.0 if task_success else 0.0)
        self.task_success_rate = np.mean(self.success_history) if self.success_history else 0.0

        self.code_execution_accuracy = execution_accuracy
        self.mathematical_reasoning_score = reasoning_score
        self.cross_domain_transfer = transfer_score

    def update_secondary_metrics(self, efficiency: float, diversity: float,
                               cost: float, memory: float):
        """Met à jour métriques secondaires"""
        self.efficiency_history.append(efficiency)
        self.diversity_history.append(diversity)

        self.learning_efficiency = np.mean(self.efficiency_history) if self.efficiency_history else 0.0
        self.task_diversity = np.mean(self.diversity_history) if self.diversity_history else 0.0
        self.computational_cost = cost
        self.memory_usage = memory

    def update_robustness_metrics(self, variance: float, stability: float,
                                generalization: float, hacking_resistance: float):
        """Met à jour métriques de robustesse"""
        self.performance_variance = variance
        self.stability_score = stability
        self.generalization_score = generalization
        self.reward_hacking_resistance = hacking_resistance

    def get_comprehensive_report(self) -> Dict[str, Any]:
        """Retourne rapport complet des métriques"""
        return {
            'primary_metrics': {
                'task_success_rate': self.task_success_rate,
                'code_execution_accuracy': self.code_execution_accuracy,
                'mathematical_reasoning_score': self.mathematical_reasoning_score,
                'cross_domain_transfer': self.cross_domain_transfer
            },
            'secondary_metrics': {
                'learning_efficiency': self.learning_efficiency,
                'task_diversity': self.task_diversity,
                'computational_cost': self.computational_cost,
                'memory_usage': self.memory_usage
            },
            'robustness_metrics': {
                'performance_variance': self.performance_variance,
                'stability_score': self.stability_score,
                'generalization_score': self.generalization_score,
                'reward_hacking_resistance': self.reward_hacking_resistance
            }
        }

class TaskRelativeREINFORCEPlusPlus:
    """Implémentation Task-Relative REINFORCE++ selon recherches"""

    def __init__(self, hyperparams: AZREnhancedHyperparameters):
        self.hyperparams = hyperparams
        self.baselines_by_task_type = {}
        self.advantage_history = deque(maxlen=1000)
        self.gradient_norms = deque(maxlen=100)

    def calculate_baseline(self, task_type: str, reward_history: List[float]) -> float:
        """Calcule baseline séparée pour chaque type de tâche"""
        if task_type not in self.baselines_by_task_type:
            self.baselines_by_task_type[task_type] = deque(maxlen=50)

        # Ajoute récompenses récentes
        self.baselines_by_task_type[task_type].extend(reward_history)

        # Calcule baseline comme moyenne mobile
        baseline_values = list(self.baselines_by_task_type[task_type])
        return np.mean(baseline_values) if baseline_values else 0.0

    def calculate_advantage(self, reward: float, baseline: float) -> float:
        """Calcule avantage normalisé"""
        advantage = reward - baseline
        self.advantage_history.append(advantage)

        # Normalisation des avantages si activée
        if len(self.advantage_history) > 10:
            advantages = list(self.advantage_history)
            mean_adv = np.mean(advantages)
            std_adv = np.std(advantages) + 1e-8
            normalized_advantage = (advantage - mean_adv) / std_adv
            return normalized_advantage

        return advantage

    def update_parameters(self, task_type: str, reward: float,
                         policy_gradient: np.ndarray) -> np.ndarray:
        """Mise à jour paramètres avec TRR++"""
        # Calcule baseline pour ce type de tâche
        baseline = self.calculate_baseline(task_type, [reward])

        # Calcule avantage normalisé
        advantage = self.calculate_advantage(reward, baseline)

        # Gradient policy avec avantage
        gradient_update = advantage * policy_gradient

        # Gradient clipping
        gradient_norm = np.linalg.norm(gradient_update)
        self.gradient_norms.append(gradient_norm)

        if gradient_norm > self.hyperparams.gradient_clipping:
            gradient_update = gradient_update * (self.hyperparams.gradient_clipping / gradient_norm)

        return gradient_update

class CurriculumLearningAdaptive:
    """Curriculum Learning adaptatif selon recherches"""

    def __init__(self, hyperparams: AZREnhancedHyperparameters):
        self.hyperparams = hyperparams
        self.current_difficulty = hyperparams.curriculum_difficulty_initial
        self.performance_history = deque(maxlen=hyperparams.performance_window)
        self.difficulty_history = deque(maxlen=100)

    def update_performance(self, success_rate: float):
        """Met à jour performance et ajuste difficulté"""
        self.performance_history.append(success_rate)

        if len(self.performance_history) >= 10:  # Minimum d'observations
            recent_performance = np.mean(list(self.performance_history)[-20:])

            # Ajustement adaptatif de difficulté
            if recent_performance > self.hyperparams.mastery_threshold:
                # Performance élevée → Augmente difficulté
                difficulty_increase = self.hyperparams.difficulty_progression_rate
                self.current_difficulty = min(
                    self.hyperparams.max_difficulty,
                    self.current_difficulty + difficulty_increase
                )

            elif recent_performance < 0.5:  # Performance faible
                # Performance faible → Diminue difficulté
                difficulty_decrease = self.hyperparams.difficulty_progression_rate * 0.5
                self.current_difficulty = max(
                    self.hyperparams.min_difficulty,
                    self.current_difficulty - difficulty_decrease
                )

        self.difficulty_history.append(self.current_difficulty)

    def get_current_difficulty(self) -> float:
        """Retourne difficulté actuelle"""
        return self.current_difficulty

    def get_difficulty_trend(self) -> str:
        """Analyse tendance de difficulté"""
        if len(self.difficulty_history) < 5:
            return "insufficient_data"

        recent_difficulties = list(self.difficulty_history)[-5:]
        trend = np.polyfit(range(len(recent_difficulties)), recent_difficulties, 1)[0]

        if trend > 0.01:
            return "increasing"
        elif trend < -0.01:
            return "decreasing"
        else:
            return "stable"

class AZREnhancedAdapter:
    """Adaptateur principal pour intégrer améliorations AZR"""

    def __init__(self, existing_azr_system):
        self.azr_system = existing_azr_system
        self.hyperparams = AZREnhancedHyperparameters()
        self.metrics = AZREnhancedMetrics()
        self.trr_plus_plus = TaskRelativeREINFORCEPlusPlus(self.hyperparams)
        self.curriculum = CurriculumLearningAdaptive(self.hyperparams)

        # Historiques étendus
        self.training_phase = 1  # Phase actuelle (1=Stabilisation, 2=Performance, 3=Efficacité)
        self.training_step = 0
        self.enhanced_history = {
            'baseline_values': [],
            'gradient_norms': [],
            'curriculum_difficulty': [],
            'exploration_rates': [],
            'phase_transitions': []
        }

        logger.info("🚀 AZR Enhanced Adapter initialisé avec recherches scientifiques")

    def adapt_existing_system(self):
        """Adapte le système AZR existant avec les améliorations"""
        try:
            # 1. Met à jour hyperparamètres
            self._update_hyperparameters()

            # 2. Améliore système de récompenses
            self._enhance_reward_system()

            # 3. Intègre TRR++
            self._integrate_trr_plus_plus()

            # 4. Active curriculum adaptatif
            self._activate_adaptive_curriculum()

            # 5. Améliore métriques
            self._enhance_metrics_system()

            # 6. Configure monitoring selon recherches
            self._setup_enhanced_monitoring()

            logger.info("✅ Système AZR adapté avec succès selon recherches")

        except Exception as e:
            logger.error(f"❌ Erreur adaptation système AZR: {e}")

    def _setup_enhanced_monitoring(self):
        """Configure monitoring avancé selon recherches"""
        # Ajoute alertes automatiques
        self.azr_system.monitoring_alerts = {
            'gradient_norm_high': False,
            'gradient_norm_low': False,
            'success_rate_low': False,
            'memory_usage_high': False
        }

        # Hook pour surveillance continue
        original_iteration = self.azr_system.self_play_iteration

        def monitored_iteration(context):
            result = original_iteration(context)
            self._check_monitoring_alerts(result)
            return result

        self.azr_system.self_play_iteration = monitored_iteration
        logger.info("🔍 Monitoring avancé configuré")

    def _check_monitoring_alerts(self, iteration_result):
        """Vérifie alertes de monitoring"""
        # Vérification gradient norms
        if self.trr_plus_plus.gradient_norms:
            recent_norm = self.trr_plus_plus.gradient_norms[-1]
            if recent_norm > 10.0:
                self.azr_system.monitoring_alerts['gradient_norm_high'] = True
                logger.warning(f"⚠️ Gradient norm élevé: {recent_norm:.3f}")
            elif recent_norm < 0.01:
                self.azr_system.monitoring_alerts['gradient_norm_low'] = True
                logger.warning(f"⚠️ Gradient norm faible: {recent_norm:.3f}")

        # Vérification success rate
        if self.metrics.task_success_rate < 0.30:
            self.azr_system.monitoring_alerts['success_rate_low'] = True
            logger.warning(f"⚠️ Taux de succès faible: {self.metrics.task_success_rate:.3f}")

    def _update_hyperparameters(self):
        """Met à jour hyperparamètres selon recherches"""
        # Learning rates différenciés - FORCE LA CRÉATION
        self.azr_system.learning_rate_proposer = self.hyperparams.learning_rate_proposer
        self.azr_system.learning_rate_solver = self.hyperparams.learning_rate_solver
        self.azr_system.learning_rate_baseline = self.hyperparams.learning_rate_baseline

        # Batch sizes optimisés - FORCE LA CRÉATION
        self.azr_system.batch_size_proposer = self.hyperparams.batch_size_proposer
        self.azr_system.batch_size_solver = self.hyperparams.batch_size_solver
        self.azr_system.gradient_accumulation_steps = self.hyperparams.gradient_accumulation_steps

        # Buffers étendus - FORCE LA CRÉATION
        self.azr_system.task_buffer_size = self.hyperparams.task_buffer_size
        self.azr_system.experience_replay_size = self.hyperparams.experience_replay_size
        self.azr_system.curriculum_buffer_size = self.hyperparams.curriculum_buffer_size

        # Curriculum parameters - FORCE LA CRÉATION
        self.azr_system.curriculum_difficulty_initial = self.hyperparams.curriculum_difficulty_initial
        self.azr_system.difficulty_progression_rate = self.hyperparams.difficulty_progression_rate
        self.azr_system.mastery_threshold = self.hyperparams.mastery_threshold
        self.azr_system.performance_window_enhanced = self.hyperparams.performance_window

        # Exploration parameters - FORCE LA CRÉATION
        self.azr_system.epsilon_initial = self.hyperparams.epsilon_initial
        self.azr_system.epsilon_final = self.hyperparams.epsilon_final
        self.azr_system.epsilon_decay_enhanced = self.hyperparams.epsilon_decay
        self.azr_system.temperature_sampling = self.hyperparams.temperature_sampling

        # Stabilité parameters - FORCE LA CRÉATION
        self.azr_system.target_network_update_rate_enhanced = self.hyperparams.target_network_update_rate
        self.azr_system.gradient_clipping_enhanced = self.hyperparams.gradient_clipping

        logger.info("📊 Hyperparamètres mis à jour selon recherches")

    def _enhance_reward_system(self):
        """Améliore système de récompenses selon recherches"""
        # Récompenses optimisées pour Proposer - FORCE LA CRÉATION
        self.azr_system.reward_optimal_task = self.hyperparams.reward_optimal_task
        self.azr_system.penalty_easy_task = self.hyperparams.penalty_easy_task
        self.azr_system.penalty_impossible_task = self.hyperparams.penalty_impossible_task
        self.azr_system.diversity_bonus_min = self.hyperparams.diversity_bonus_range[0]
        self.azr_system.diversity_bonus_max = self.hyperparams.diversity_bonus_range[1]

        # Récompenses optimisées pour Solver - FORCE LA CRÉATION
        self.azr_system.reward_correct_solution = 1.0
        self.azr_system.penalty_incorrect_solution = -0.3
        self.azr_system.penalty_syntax_error = -0.8
        self.azr_system.efficiency_bonus_min = 0.1
        self.azr_system.efficiency_bonus_max = 0.2

        # Système de récompenses AZR étendu - FORCE LA CRÉATION
        self.azr_system.enhanced_reward_system = {
            'proposer_rewards': {
                'optimal_task': self.hyperparams.reward_optimal_task,
                'easy_task_penalty': self.hyperparams.penalty_easy_task,
                'impossible_task_penalty': self.hyperparams.penalty_impossible_task,
                'diversity_bonus_range': self.hyperparams.diversity_bonus_range
            },
            'solver_rewards': {
                'correct_solution': 1.0,
                'incorrect_solution_penalty': -0.3,
                'syntax_error_penalty': -0.8,
                'efficiency_bonus_range': (0.1, 0.2)
            },
            'reward_normalization': True,
            'baseline_adjustment': True,
            'variance_reduction': True
        }

        logger.info("🎯 Système de récompenses amélioré selon recherches")

    def _integrate_trr_plus_plus(self):
        """Intègre algorithme Task-Relative REINFORCE++"""
        # Remplace méthode de mise à jour existante
        original_update = self.azr_system._update_shared_parameters_trr

        def enhanced_update(task, solution, learnability_reward, solution_reward):
            # Applique TRR++ amélioré
            task_type = task.task_type

            # Mise à jour Proposer avec TRR++
            proposer_gradient = np.random.randn(10)  # Placeholder
            proposer_update = self.trr_plus_plus.update_parameters(
                task_type + "_proposer", learnability_reward, proposer_gradient
            )

            # Mise à jour Solver avec TRR++
            solver_gradient = np.random.randn(10)  # Placeholder
            solver_update = self.trr_plus_plus.update_parameters(
                task_type + "_solver", solution_reward, solver_gradient
            )

            # Appelle méthode originale
            original_update(task, solution, learnability_reward, solution_reward)

        # Remplace méthode
        self.azr_system._update_shared_parameters_trr = enhanced_update

        logger.info("🧠 Task-Relative REINFORCE++ intégré")

    def _activate_adaptive_curriculum(self):
        """Active curriculum learning adaptatif"""
        # Ajoute curriculum au système
        self.azr_system.adaptive_curriculum = self.curriculum

        # Hook pour mise à jour curriculum
        original_iteration = self.azr_system.self_play_iteration

        def enhanced_iteration(context):
            result = original_iteration(context)

            # Met à jour curriculum basé sur performance
            if 'solution_reward' in result:
                success_rate = 1.0 if result['solution_reward'] > 0.5 else 0.0
                self.curriculum.update_performance(success_rate)

            return result

        self.azr_system.self_play_iteration = enhanced_iteration

        logger.info("📚 Curriculum learning adaptatif activé")

    def _enhance_metrics_system(self):
        """Améliore système de métriques"""
        self.azr_system.enhanced_metrics = self.metrics

        # Ajoute méthode de rapport étendu
        def get_enhanced_status():
            base_status = self.azr_system.get_system_status()
            enhanced_metrics = self.metrics.get_comprehensive_report()

            return {
                **base_status,
                'enhanced_metrics': enhanced_metrics,
                'training_phase': self.training_phase,
                'curriculum_difficulty': self.curriculum.get_current_difficulty(),
                'difficulty_trend': self.curriculum.get_difficulty_trend()
            }

        self.azr_system.get_enhanced_status = get_enhanced_status

        logger.info("📈 Système de métriques amélioré")

    def update_training_phase(self):
        """Met à jour phase d'entraînement selon recherches"""
        self.training_step += 1

        # Phase 1: Stabilisation (0-2000 steps)
        if self.training_step <= 2000 and self.training_phase != 1:
            self.training_phase = 1
            self.enhanced_history['phase_transitions'].append({
                'step': self.training_step,
                'phase': 1,
                'name': 'Stabilisation'
            })
            logger.info("📍 Phase 1: Stabilisation activée")

        # Phase 2: Performance (2000-20000 steps)
        elif 2000 < self.training_step <= 20000 and self.training_phase != 2:
            self.training_phase = 2
            self.enhanced_history['phase_transitions'].append({
                'step': self.training_step,
                'phase': 2,
                'name': 'Performance'
            })
            logger.info("📍 Phase 2: Performance activée")

        # Phase 3: Efficacité (20000+ steps)
        elif self.training_step > 20000 and self.training_phase != 3:
            self.training_phase = 3
            self.enhanced_history['phase_transitions'].append({
                'step': self.training_step,
                'phase': 3,
                'name': 'Efficacité'
            })
            logger.info("📍 Phase 3: Efficacité activée")

    def get_adaptation_report(self) -> Dict[str, Any]:
        """Retourne rapport complet d'adaptation"""
        return {
            'hyperparameters': {
                'learning_rate_proposer': self.hyperparams.learning_rate_proposer,
                'learning_rate_solver': self.hyperparams.learning_rate_solver,
                'batch_size_proposer': self.hyperparams.batch_size_proposer,
                'batch_size_solver': self.hyperparams.batch_size_solver,
                'curriculum_difficulty': self.curriculum.get_current_difficulty()
            },
            'training_status': {
                'current_step': self.training_step,
                'current_phase': self.training_phase,
                'phase_name': ['', 'Stabilisation', 'Performance', 'Efficacité'][self.training_phase]
            },
            'metrics': self.metrics.get_comprehensive_report(),
            'trr_plus_plus': {
                'baseline_count': len(self.trr_plus_plus.baselines_by_task_type),
                'advantage_history_length': len(self.trr_plus_plus.advantage_history),
                'recent_gradient_norm': list(self.trr_plus_plus.gradient_norms)[-5:] if self.trr_plus_plus.gradient_norms else []
            },
            'curriculum': {
                'current_difficulty': self.curriculum.get_current_difficulty(),
                'difficulty_trend': self.curriculum.get_difficulty_trend(),
                'performance_window_size': len(self.curriculum.performance_history)
            }
        }
