# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 277 à 284
# Type: Méthode de la classe RealtimeCalibrator

    def _get_realtime_metrics(self) -> Dict[str, float]:
        """Obtient métriques temps réel"""
        return {
            'accuracy': self.realtime_accuracy,
            'confidence': self.realtime_confidence,
            'stability': self.realtime_stability,
            'performance_score': (self.realtime_accuracy + self.realtime_confidence + self.realtime_stability) / 3
        }