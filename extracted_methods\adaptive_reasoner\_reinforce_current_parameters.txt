# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 796 à 806
# Type: Méthode de la classe AdaptiveReasoner

    def _reinforce_current_parameters(self):
        """Renforce paramètres actuels (performance améliorée)"""
        # Légère augmentation learning rate
        self.adaptive_learning_rate *= 1.05
        self.adaptive_learning_rate = min(self.adaptive_learning_rate, self.learning_rate_range[1])

        # Légère réduction seuil confiance (moins conservateur)
        self.adaptive_confidence_threshold *= 0.95
        self.adaptive_confidence_threshold = max(self.adaptive_confidence_threshold, self.confidence_threshold_range[0])

        self._update_current_parameter_set()