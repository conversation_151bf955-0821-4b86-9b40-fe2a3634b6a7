# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 340 à 369
# Type: Méthode de la classe BaccaratPatternValidator

    def _calculate_azr_reward(self, validation_result: Dict, pattern: Dict) -> float:
        """Calcule récompense AZR basée sur validation"""
        if validation_result.get('awaiting_outcome'):
            # Récompense intermédiaire basée sur confiance
            confidence = validation_result.get('predicted_confidence', 0.5)
            return confidence * 0.5  # Récompense partielle

        success = validation_result.get('success', False)
        accuracy = validation_result.get('accuracy', 0.0)
        confidence = validation_result.get('predicted_confidence', 0.5)

        if success:
            # Récompense positive avec bonus confiance
            base_reward = 1.0
            confidence_bonus = confidence * 0.5
            pattern_complexity_bonus = self._calculate_pattern_complexity_bonus(pattern)

            total_reward = base_reward + confidence_bonus + pattern_complexity_bonus
        else:
            # Pénalité avec facteur confiance
            base_penalty = -0.5
            confidence_penalty = validation_result.get('confidence_penalty', 0.0)

            total_reward = base_penalty - confidence_penalty

        # Application decay temporel
        time_factor = self._calculate_time_decay_factor()
        final_reward = total_reward * time_factor

        return np.clip(final_reward, -2.0, 2.0)