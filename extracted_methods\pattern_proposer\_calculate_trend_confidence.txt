# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 375 à 412
# Type: Méthode de la classe BaccaratPatternProposer

    def _calculate_trend_confidence(self, segment: List[int], slope: float) -> float:
        """
        Calcule confiance d'une tendance - LOGIQUE BACCARAT PURE
        SUPPRESSION corrélations statistiques inadaptées au Baccarat
        """
        try:
            # ═══════════════════════════════════════════════════════════════════
            # LOGIQUE BACCARAT SPÉCIFIQUE (pas de corrélations mathématiques)
            # ═══════════════════════════════════════════════════════════════════

            # Analyse directe de la consistance du pattern
            if len(segment) < 3:
                return 0.0

            # Vérification tendance simple (pas de régression)
            recent_changes = []
            for i in range(1, len(segment)):
                if segment[i] != segment[i-1]:
                    recent_changes.append(1)
                else:
                    recent_changes.append(0)

            # Confiance basée sur consistance directe
            if len(recent_changes) == 0:
                return 0.0

            change_rate = sum(recent_changes) / len(recent_changes)

            # Logique Baccarat: tendances modérées plus fiables
            if 0.3 <= change_rate <= 0.7:
                return 0.6  # Tendance équilibrée
            elif change_rate < 0.3:
                return 0.4  # Trop stable (suspect)
            else:
                return 0.3  # Trop chaotique

        except Exception:
            return 0.0