# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 200 à 218
# Type: Méthode de la classe BaccaratPredictorApp

    def _activate_game_interface(self):
        """Active l'interface de jeu après la première prédiction"""
        try:
            # Désactiver bouton commencer
            self.predict_btn.config(state='disabled', text="🎯 EN COURS")

            # Activer boutons résultats
            self.player_btn.config(state='normal')
            self.banker_btn.config(state='normal')

            # Mise à jour statut
            self.status_label.config(text="Sélectionnez le résultat réel de la manche")

            # Message dans zone info
            self.info_text.insert(tk.END, "🎮 Interface activée - Sélectionnez le résultat de la manche\n")
            self.info_text.see(tk.END)

        except Exception as e:
            logger.error(f"Erreur activation interface: {e}")