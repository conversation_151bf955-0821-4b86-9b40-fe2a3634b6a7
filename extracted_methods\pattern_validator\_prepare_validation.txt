# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 330 à 338
# Type: Méthode de la classe BaccaratPatternValidator

    def _prepare_validation(self, prediction: Dict, pattern: Dict) -> Dict[str, Any]:
        """Prépare validation pour prédiction en attente"""
        return {
            'success': None,
            'accuracy': None,
            'predicted_confidence': prediction.get('confidence', 0.5),
            'awaiting_outcome': True,
            'prediction_details': prediction
        }