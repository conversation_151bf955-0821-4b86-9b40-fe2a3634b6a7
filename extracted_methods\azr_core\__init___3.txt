# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 645 à 703
# Type: Méthode de la classe AZRSystem
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self):
        self.environment = AZREnvironment()
        self.proposer = AZRProposer(self.environment)
        self.solver = AZRSolver(self.environment)

        # 🧠 ENSEMBLE MODELS AVEC AZR META-LEARNER
        if ENSEMBLE_AVAILABLE:
            self.ensemble_model = EnsembleModel()
            self.use_ensemble = True
            logger.info("🚀 AZR initialisé avec ensemble LSTM+LGBM+Markov")
        else:
            self.ensemble_model = None
            self.use_ensemble = False
            logger.warning("⚠️ AZR initialisé en mode solo (ensemble non disponible)")

        # Paramètres système optimisés
        self.lambda_balance = 0.7  # Plus d'accent sur solution reward (performance)
        self.learning_rate = 0.01
        self.training_iterations = 0

        # Paramètres récompenses adaptatives
        self.adaptive_lambda = 0.7  # Lambda adaptatif
        self.target_accuracy = 0.6  # Cible de précision
        self.performance_window = 20  # Fenêtre d'évaluation performance
        self.streak_bonus_factor = 0.1  # Facteur bonus séquences correctes
        self.calibration_penalty_factor = 0.2  # Facteur pénalité calibration

        # Historique apprentissage étendu
        self.training_history = {
            'learnability_rewards': [],
            'solution_rewards': [],
            'combined_rewards': [],
            'performance_metrics': [],
            'prediction_accuracy': [],
            'confidence_calibration': [],
            'correct_streaks': [],
            'adaptive_lambda_history': [],
            'ensemble_performance': []     # Performance ensemble
        }

        # Paramètres modèle partagés
        self.shared_parameters = {
            'confidence_adjustment': 0.0,
            'prediction_bias': 0.0,
            'decision_threshold': 0.5,
            'pattern_weights': {
                'frequency': 0.4,
                'streaks': 0.3,
                'alternation': 0.3
            },
            # 🎯 NOUVEAUX PARAMÈTRES ENSEMBLE
            'ensemble_weights': {          # Poids modèles ensemble
                'lstm': 0.33,
                'lgbm': 0.33,
                'markov': 0.34
            },
            'ensemble_confidence_factor': 1.0,  # Facteur confiance ensemble
            'azr_ensemble_balance': 0.3    # Balance AZR vs ensemble (30% AZR, 70% ensemble)
        }