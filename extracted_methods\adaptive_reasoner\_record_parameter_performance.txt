# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 729 à 742
# Type: Méthode de la classe AdaptiveReasoner

    def _record_parameter_performance(self, accuracy: float):
        """Enregistre performance des paramètres actuels"""
        performance_entry = {
            'timestamp': time.time(),
            'accuracy': accuracy,
            'parameters': self.current_parameter_set.copy(),
            'total_predictions': self.total_predictions
        }

        self.parameter_performance_history.append(performance_entry)

        # Limitation taille historique
        if len(self.parameter_performance_history) > self.parameter_optimization_window * 2:
            self.parameter_performance_history = self.parameter_performance_history[-self.parameter_optimization_window * 2:]