"""
🧪 TEST SIMPLE ORCHESTRATEUR AZR
================================

Test basique de l'orchestrateur unifié sans dépendances complexes.
"""

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_orchestrator():
    """Test simple de l'orchestrateur"""
    try:
        logger.info("🧪 Test orchestrateur AZR unifié")
        
        # Import orchestrateur
        from azr_unified_parameters import get_azr_orchestrator
        
        # Création instance
        orchestrator = get_azr_orchestrator()
        logger.info("✅ Orchestrateur créé")
        
        # Test paramètres
        all_params = orchestrator.get_all_parameters()
        logger.info(f"✅ Paramètres récupérés: {len(all_params)} catégories")
        
        # Test diagnostic
        diagnostic = orchestrator.get_diagnostic_info()
        logger.info(f"✅ Diagnostic: {diagnostic['orchestrator_status']}")
        
        # Test mise à jour
        prediction = {
            'predicted_outcome': 1,
            'confidence': 0.65,
            'method': 'test'
        }
        orchestrator.update_shared_state(prediction, 1)
        logger.info("✅ État mis à jour")
        
        logger.info("🎉 Test orchestrateur réussi!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_orchestrator()
    print(f"\nRésultat: {'SUCCÈS' if success else 'ÉCHEC'}")
