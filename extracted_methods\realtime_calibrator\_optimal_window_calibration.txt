# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 371 à 422
# Type: Méthode de la classe RealtimeCalibrator

    def _optimal_window_calibration(self) -> Dict[str, Any]:
        """
        Calibration fenêtre optimale (manches 31-60)
        Précision maximale avec ajustements fins
        """
        try:
            old_parameters = self.calibrated_parameters.copy()

            # ═══════════════════════════════════════════════════════════════════
            # CALIBRATION PRÉCISION MAXIMALE
            # ═══════════════════════════════════════════════════════════════════

            # Ajustements fins basés sur performance récente
            performance_trend = self._analyze_performance_trend()

            # Optimisation gradient parallélisée
            gradient_results = self._parallel_gradient_optimization()

            # Analyse interactions paramètres (haute capacité mémoire)
            interaction_analysis = self._analyze_parameter_interactions()

            # Application ajustements optimaux
            parameter_adjustments = self._calculate_optimal_adjustments(
                performance_trend, gradient_results, interaction_analysis
            )

            # Mise à jour paramètres avec ajustements fins
            for param, adjustment in parameter_adjustments.items():
                if param in self.calibrated_parameters:
                    old_value = self.calibrated_parameters[param]
                    new_value = np.clip(
                        old_value + adjustment,
                        self.parameter_ranges[param][0],
                        self.parameter_ranges[param][1]
                    )
                    self.calibrated_parameters[param] = new_value

            improvement = self._calculate_improvement(old_parameters, self.calibrated_parameters)

            return {
                'success': True,
                'phase': 'optimal',
                'old_parameters': old_parameters,
                'new_parameters': self.calibrated_parameters.copy(),
                'improvement': improvement,
                'adjustments_applied': parameter_adjustments,
                'performance_trend': performance_trend
            }

        except Exception as e:
            logger.error(f"Erreur calibration optimal: {e}")
            return {'success': False, 'error': str(e)}