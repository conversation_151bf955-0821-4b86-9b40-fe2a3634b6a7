# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 702 à 727
# Type: Méthode de la classe AdaptiveReasoner

    def get_reasoning_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques de raisonnement"""
        return {
            'reasoning_sessions': self.reasoning_sessions,
            'total_predictions': self.total_predictions,
            'successful_predictions': self.successful_predictions,
            'session_accuracy': self._calculate_session_accuracy(),
            'successful_patterns_count': len(self.successful_patterns),
            'failed_patterns_count': len(self.failed_patterns),
            'average_confidence': np.mean(list(self.confidence_history)) if self.confidence_history else 0.5,
            'confidence_trend': list(self.confidence_history)[-10:],  # 10 dernières
            'validator_stats': self.pattern_validator.get_validation_statistics(),
            # Nouvelles statistiques fenêtre optimale
            'optimal_window_config': {
                'start_round': global_config.azr.prediction_start_round,
                'end_round': global_config.azr.prediction_end_round,
                'min_history': global_config.azr.min_history_for_prediction
            },
            # Statistiques optimisation méta-paramètres
            'meta_parameter_optimization': {
                'current_parameters': self.current_parameter_set.copy(),
                'parameter_performance_score': self.parameter_performance_score,
                'optimization_history_length': len(self.parameter_performance_history),
                'last_optimization_round': self.last_optimization_round
            }
        }