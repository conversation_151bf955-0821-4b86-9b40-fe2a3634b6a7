# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 482 à 492
# Type: Méthode de la classe PredictionCalculator

    def _default_prediction(self) -> Dict[str, Any]:
        """Prédiction par défaut"""
        default_prob = global_config.calculations.max_prob_center
        return {
            'player': default_prob,
            'banker': default_prob,
            'confidence': default_prob,
            'recommendation': global_config.calculations.wait_recommendation,
            'model_predictions': {},
            'model_weights': {}
        }