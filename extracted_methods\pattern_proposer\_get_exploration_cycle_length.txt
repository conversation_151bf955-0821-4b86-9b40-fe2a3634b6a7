# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 753 à 756
# Type: Méthode de la classe BaccaratPatternProposer

    def _get_exploration_cycle_length(self) -> int:
        """Obtient longueur de cycle pour exploration"""
        min_len, max_len = self.cycle_length_range
        return np.random.randint(min_len, max_len + 1)