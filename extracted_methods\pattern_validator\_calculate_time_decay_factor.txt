# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 395 à 398
# Type: Méthode de la classe BaccaratPatternValidator

    def _calculate_time_decay_factor(self) -> float:
        """Calcule facteur de decay temporel"""
        # Récompenses récentes plus importantes
        return max(0.5, 1.0 - (len(self.validation_history) * self.reward_decay))