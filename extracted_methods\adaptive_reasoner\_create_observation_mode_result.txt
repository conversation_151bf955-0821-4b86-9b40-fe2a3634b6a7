# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 583 à 610
# Type: Méthode de la classe AdaptiveReasoner

    def _create_observation_mode_result(self, game_history: List[int],
                                      current_round: int, message: str) -> Dict[str, Any]:
        """Crée résultat en mode observation (avant manche 31)"""
        return {
            'prediction': {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,
                'prediction_strength': 0.0
            },
            'confidence': 0.0,  # Pas de confiance en mode observation
            'uncertainty': 1.0,  # Incertitude maximale
            'recommendation': f"Observer - Manche {current_round}/30 (Fenêtre optimale: 31-60)",
            'reasoning_details': {
                'mode': 'observation',
                'current_round': current_round,
                'optimal_window_start': global_config.azr.prediction_start_round,
                'patterns_detected': 0,
                'message': message
            },
            'pattern_breakdown': {'total_patterns': 0, 'observation_mode': True},
            'meta_info': {
                'round': current_round,
                'history_length': len(game_history),
                'in_optimal_window': False,
                'mode': 'observation'
            }
        }