# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 218 à 238
# Type: Méthode de la classe NewGameHandler

    def _update_game_phase_display(self):
        """Met à jour affichage phase de jeu"""
        try:
            phase_messages = {
                'waiting': "📍 Phase: En attente nouvelle partie",
                'warmup': "🔥 Phase: Échauffement (manches 1-30)",
                'optimal': "🎯 Phase: Optimale (manches 31-60)",
                'post_optimal': "🏁 Phase: Terminée"
            }

            phase_message = phase_messages.get(self.game_phase, "📍 Phase: Inconnue")

            # Mise à jour interface
            if hasattr(self.main_interface, 'game_phase_status'):
                self.main_interface.game_phase_status.set(phase_message)

            if hasattr(self.main_interface, 'azr_phase_var'):
                self.main_interface.azr_phase_var.set(phase_message)

        except Exception as e:
            logger.error(f"Erreur mise à jour phase: {e}")