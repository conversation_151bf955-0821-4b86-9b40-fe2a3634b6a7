# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\game_initialization_calibrator.py
# Lignes: 100 à 175
# Type: Méthode de la classe GameInitializationCalibrator

    def start_new_game_calibration(self) -> Dict[str, Any]:
        """
        Lance calibration complète pour nouvelle partie 60 manches
        
        Returns:
            Dict contenant résultats calibration et paramètres optimaux
        """
        try:
            self.calibration_start_time = time.time()
            logger.info("🎯 Début calibration nouvelle partie AZR (60 manches)")
            
            # ═══════════════════════════════════════════════════════════════════
            # CALIBRATION COMPLÈTE DÉBUT PARTIE
            # ═══════════════════════════════════════════════════════════════════
            
            # 1. Reset complet système
            reset_result = self._perform_complete_system_reset()
            
            # 2. Optimisation ressources système
            resource_result = self._optimize_system_resources()
            
            # 3. Calibration paramètres pour 60 manches
            parameter_result = self._calibrate_parameters_for_60_rounds()
            
            # 4. Initialisation structures mémoire
            memory_result = self._initialize_memory_structures()
            
            # 5. Calibration seuils adaptatifs
            threshold_result = self._calibrate_adaptive_thresholds()
            
            # 6. Validation calibration
            validation_result = self._validate_calibration()
            
            self.calibration_duration = time.time() - self.calibration_start_time
            self.calibration_complete = True
            
            # Enregistrement historique
            calibration_entry = {
                'timestamp': time.time(),
                'duration': self.calibration_duration,
                'optimal_parameters': self.optimal_parameters.copy(),
                'system_resources': {
                    'ram_available': self.available_ram,
                    'cpu_cores': self.cpu_cores,
                    'ram_allocated': self.max_ram_gb
                },
                'calibration_results': {
                    'reset': reset_result,
                    'resources': resource_result,
                    'parameters': parameter_result,
                    'memory': memory_result,
                    'thresholds': threshold_result,
                    'validation': validation_result
                }
            }
            self.calibration_history.append(calibration_entry)
            
            logger.info(f"✅ Calibration nouvelle partie terminée - Durée: {self.calibration_duration:.2f}s")
            
            return {
                'success': True,
                'calibration_duration': self.calibration_duration,
                'optimal_parameters': self.optimal_parameters.copy(),
                'system_status': 'Optimisé pour 60 manches',
                'phases_configured': ['warmup', 'optimal', 'post_optimal'],
                'ready_for_game': True,
                'calibration_details': calibration_entry['calibration_results']
            }
            
        except Exception as e:
            logger.error(f"Erreur calibration nouvelle partie: {e}")
            return {
                'success': False,
                'error': str(e),
                'calibration_duration': time.time() - self.calibration_start_time if self.calibration_start_time else 0
            }