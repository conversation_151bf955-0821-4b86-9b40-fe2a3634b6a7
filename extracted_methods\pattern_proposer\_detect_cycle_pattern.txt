# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 501 à 518
# Type: Méthode de la classe BaccaratPatternProposer

    def _detect_cycle_pattern(self, results: List[int], cycle_length: int) -> float:
        """Détecte patterns cycliques"""
        if len(results) < cycle_length * 2:
            return 0.0

        # Vérification répétition du pattern
        pattern = results[-cycle_length:]
        matches = 0
        total_checks = 0

        for i in range(len(results) - cycle_length, -1, -cycle_length):
            if i >= cycle_length:
                segment = results[i-cycle_length:i]
                total_checks += 1
                if segment == pattern:
                    matches += 1

        return matches / total_checks if total_checks > 0 else 0.0