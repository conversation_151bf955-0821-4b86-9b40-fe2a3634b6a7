"""
AZR CORE MODULE
===============

Module principal du paradigme Absolute Zero Reasoner pour Baccarat.
Fournit tous les composants nécessaires pour le raisonnement adaptatif
sans entraînement préalable.
"""

from .pattern_proposer import BaccaratPatternProposer
from .pattern_validator import BaccaratPatternValidator
from .adaptive_reasoner import BaccaratAdaptiveReasoner
from .self_play_engine import BaccaratSelfPlayEngine

__all__ = [
    'BaccaratPatternProposer',
    'BaccaratPatternValidator', 
    'BaccaratAdaptiveReasoner',
    'BaccaratSelfPlayEngine'
]

__version__ = "1.0.0"
__author__ = "AZR Baccarat Team"
__description__ = "Absolute Zero Reasoner pour prédiction Baccarat sans entraînement"
