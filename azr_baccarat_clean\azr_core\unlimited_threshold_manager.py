"""
GESTIONNAIRE SEUILS ADAPTATIFS ILLIMITÉS AZR
============================================

Module de gestion des seuils adaptatifs sans limitations pour performance maximale.
Supprime tous les plafonds et contraintes pour libérer le potentiel du modèle.
"""

import numpy as np
import sys
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import logging
from collections import deque

# Import configuration centralisée
sys.path.insert(0, str(Path(__file__).parent.parent / "parameters"))
from parameters import global_config

# Configuration logging
logger = logging.getLogger(__name__)


class UnlimitedThresholdManager:
    """
    Gestionnaire de seuils adaptatifs illimités
    
    Supprime toutes les limitations fixes pour permettre au modèle
    d'atteindre sa performance maximale sans contraintes.
    """
    
    def __init__(self):
        """Initialisation gestionnaire seuils illimités"""
        
        # ═══════════════════════════════════════════════════════════════════
        # SEUILS ADAPTATIFS ILLIMITÉS
        # ═══════════════════════════════════════════════════════════════════
        
        # Seuils actuels (départ conservateur, optimisation vers performance max)
        self.current_thresholds = {
            'confidence': 0.1,      # Départ très bas - monte vers optimum
            'uncertainty': 0.9,     # Départ très haut - descend vers optimum
            'prediction': 0.5,      # Neutre - auto-calibré
            'accuracy': 0.0,        # Aucune limite minimale
            'precision': 0.0,       # Aucune limite minimale
            'recall': 0.0          # Aucune limite minimale
        }
        
        # Historique performance par seuil
        self.threshold_performance_history = {
            threshold_name: deque(maxlen=1000) 
            for threshold_name in self.current_thresholds.keys()
        }
        
        # Plages adaptation (illimitées)
        self.adaptation_ranges = {
            'confidence': (0.001, 0.999),      # Plage quasi-complète
            'uncertainty': (0.001, 0.999),     # Plage quasi-complète
            'prediction': (0.001, 0.999),      # Plage quasi-complète
            'accuracy': (0.0, 1.0),           # Plage complète
            'precision': (0.0, 1.0),          # Plage complète
            'recall': (0.0, 1.0)              # Plage complète
        }
        
        # ═══════════════════════════════════════════════════════════════════
        # PARAMÈTRES OPTIMISATION ILLIMITÉE
        # ═══════════════════════════════════════════════════════════════════
        
        # Apprentissage adaptatif
        self.learning_rate = global_config.azr.adaptive_threshold_learning_rate
        self.optimization_frequency = global_config.azr.threshold_optimization_frequency
        
        # Métriques performance
        self.performance_history = deque(maxlen=5000)
        self.optimization_history = deque(maxlen=1000)
        
        # État optimisation
        self.optimization_count = 0
        self.last_optimization_round = 0
        self.best_performance_achieved = 0.0
        self.performance_plateau_count = 0
        
        # Mode performance illimitée
        self.unlimited_mode = global_config.azr.baccarat_unlimited_mode
        self.aggressive_optimization = True
        self.no_performance_limits = True
        
        logger.info("UnlimitedThresholdManager initialisé - Mode performance illimitée activé")
    
    def update_thresholds_realtime(self, round_number: int, prediction_result: Dict[str, Any], 
                                  actual_outcome: int) -> Dict[str, Any]:
        """
        Met à jour seuils en temps réel pour performance maximale
        
        Args:
            round_number: Numéro de manche
            prediction_result: Résultat prédiction
            actual_outcome: Résultat réel
            
        Returns:
            Dict contenant nouveaux seuils optimisés
        """
        try:
            # Enregistrement performance
            performance_entry = {
                'round': round_number,
                'predicted': prediction_result.get('predicted_outcome', 0),
                'actual': actual_outcome,
                'correct': prediction_result.get('predicted_outcome', 0) == actual_outcome,
                'confidence': prediction_result.get('confidence', 0.5),
                'uncertainty': prediction_result.get('uncertainty', 0.5),
                'current_thresholds': self.current_thresholds.copy(),
                'timestamp': time.time()
            }
            
            self.performance_history.append(performance_entry)
            
            # Optimisation seuils si nécessaire
            optimization_triggered = self._should_optimize_thresholds(round_number)
            
            if optimization_triggered:
                optimization_result = self._optimize_thresholds_unlimited()
                
                return {
                    'success': True,
                    'round': round_number,
                    'optimization_triggered': True,
                    'optimization_result': optimization_result,
                    'new_thresholds': self.current_thresholds.copy(),
                    'performance_improvement': optimization_result.get('improvement', 0.0)
                }
            else:
                return {
                    'success': True,
                    'round': round_number,
                    'optimization_triggered': False,
                    'current_thresholds': self.current_thresholds.copy()
                }
                
        except Exception as e:
            logger.error(f"Erreur mise à jour seuils round {round_number}: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_current_thresholds(self) -> Dict[str, float]:
        """Obtient seuils actuels optimisés"""
        return self.current_thresholds.copy()
    
    def get_threshold_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques des seuils"""
        return {
            'current_thresholds': self.current_thresholds.copy(),
            'adaptation_ranges': self.adaptation_ranges.copy(),
            'unlimited_mode': self.unlimited_mode,
            'optimization_count': self.optimization_count,
            'last_optimization_round': self.last_optimization_round,
            'best_performance_achieved': self.best_performance_achieved,
            'performance_plateau_count': self.performance_plateau_count,
            'performance_history_size': len(self.performance_history),
            'optimization_history_size': len(self.optimization_history),
            'aggressive_optimization': self.aggressive_optimization,
            'no_performance_limits': self.no_performance_limits
        }
    
    def _should_optimize_thresholds(self, round_number: int) -> bool:
        """Détermine si optimisation seuils nécessaire"""
        rounds_since_last = round_number - self.last_optimization_round
        
        # Optimisation fréquente en mode illimité
        if rounds_since_last >= self.optimization_frequency:
            return True
        
        # Optimisation immédiate si performance dégradée
        if len(self.performance_history) >= 5:
            recent_accuracy = self._calculate_recent_accuracy(5)
            if recent_accuracy < self.best_performance_achieved * 0.9:  # Dégradation 10%+
                return True
        
        return False
    
    def _optimize_thresholds_unlimited(self) -> Dict[str, Any]:
        """
        Optimise seuils sans limitations pour performance maximale
        """
        try:
            optimization_start = time.time()
            old_thresholds = self.current_thresholds.copy()
            
            # ═══════════════════════════════════════════════════════════════════
            # OPTIMISATION ILLIMITÉE HAUTE PERFORMANCE
            # ═══════════════════════════════════════════════════════════════════
            
            # Analyse performance actuelle
            current_performance = self._calculate_current_performance()
            
            # Test variations seuils (exploration agressive)
            best_thresholds = self._test_threshold_variations_unlimited()
            
            # Application meilleurs seuils trouvés
            if best_thresholds['performance'] > current_performance:
                self.current_thresholds.update(best_thresholds['thresholds'])
                improvement = best_thresholds['performance'] - current_performance
                
                # Mise à jour meilleure performance
                if best_thresholds['performance'] > self.best_performance_achieved:
                    self.best_performance_achieved = best_thresholds['performance']
                    self.performance_plateau_count = 0
                else:
                    self.performance_plateau_count += 1
            else:
                improvement = 0.0
                self.performance_plateau_count += 1
            
            # Adaptation agressive si plateau
            if self.performance_plateau_count >= 5:
                self._apply_aggressive_threshold_changes()
                self.performance_plateau_count = 0
            
            optimization_time = time.time() - optimization_start
            self.optimization_count += 1
            self.last_optimization_round = len(self.performance_history)
            
            # Enregistrement historique
            optimization_entry = {
                'optimization_count': self.optimization_count,
                'old_thresholds': old_thresholds,
                'new_thresholds': self.current_thresholds.copy(),
                'improvement': improvement,
                'optimization_time': optimization_time,
                'performance_plateau_count': self.performance_plateau_count,
                'timestamp': time.time()
            }
            self.optimization_history.append(optimization_entry)
            
            logger.info(f"Optimisation seuils illimitée - Amélioration: {improvement:.4f} - "
                       f"Temps: {optimization_time:.3f}s - Plateau: {self.performance_plateau_count}")
            
            return {
                'success': True,
                'old_thresholds': old_thresholds,
                'new_thresholds': self.current_thresholds.copy(),
                'improvement': improvement,
                'optimization_time': optimization_time,
                'performance_plateau_count': self.performance_plateau_count
            }
            
        except Exception as e:
            logger.error(f"Erreur optimisation seuils illimitée: {e}")
            return {'success': False, 'error': str(e)}
    
    def _test_threshold_variations_unlimited(self) -> Dict[str, Any]:
        """Teste variations seuils sans limitations"""
        try:
            current_performance = self._calculate_current_performance()
            best_performance = current_performance
            best_thresholds = self.current_thresholds.copy()
            
            # Test variations pour chaque seuil
            for threshold_name in self.current_thresholds.keys():
                current_value = self.current_thresholds[threshold_name]
                min_val, max_val = self.adaptation_ranges[threshold_name]
                
                # Variations agressives (pas de limitations)
                test_values = [
                    max(min_val, current_value - 0.1),  # Réduction agressive
                    max(min_val, current_value - 0.05), # Réduction modérée
                    min(max_val, current_value + 0.05), # Augmentation modérée
                    min(max_val, current_value + 0.1),  # Augmentation agressive
                    min_val + (max_val - min_val) * 0.1,  # Exploration bas
                    min_val + (max_val - min_val) * 0.9   # Exploration haut
                ]
                
                # Test chaque variation
                for test_value in test_values:
                    test_thresholds = self.current_thresholds.copy()
                    test_thresholds[threshold_name] = test_value
                    
                    # Simulation performance avec nouveaux seuils
                    simulated_performance = self._simulate_performance_with_thresholds(test_thresholds)
                    
                    if simulated_performance > best_performance:
                        best_performance = simulated_performance
                        best_thresholds = test_thresholds.copy()
            
            return {
                'thresholds': best_thresholds,
                'performance': best_performance
            }
            
        except Exception as e:
            logger.error(f"Erreur test variations seuils: {e}")
            return {
                'thresholds': self.current_thresholds.copy(),
                'performance': self._calculate_current_performance()
            }
    
    def _apply_aggressive_threshold_changes(self):
        """Applique changements agressifs pour sortir du plateau"""
        try:
            logger.info("Application changements agressifs - Sortie plateau performance")
            
            for threshold_name in self.current_thresholds.keys():
                min_val, max_val = self.adaptation_ranges[threshold_name]
                
                # Changements agressifs aléatoires
                if np.random.random() < 0.5:
                    # Exploration vers les extrêmes
                    if np.random.random() < 0.5:
                        new_value = min_val + (max_val - min_val) * 0.1  # Vers minimum
                    else:
                        new_value = min_val + (max_val - min_val) * 0.9  # Vers maximum
                else:
                    # Variation aléatoire importante
                    current_value = self.current_thresholds[threshold_name]
                    variation = (max_val - min_val) * 0.2 * (np.random.random() - 0.5)
                    new_value = np.clip(current_value + variation, min_val, max_val)
                
                self.current_thresholds[threshold_name] = new_value
            
        except Exception as e:
            logger.error(f"Erreur changements agressifs: {e}")
    
    def _calculate_current_performance(self) -> float:
        """Calcule performance actuelle"""
        try:
            if len(self.performance_history) < 5:
                return 0.5
            
            recent_results = list(self.performance_history)[-20:]
            accuracy = sum(1 for r in recent_results if r['correct']) / len(recent_results)
            
            # Score composite (accuracy + confiance + stabilité)
            confidences = [r['confidence'] for r in recent_results]
            avg_confidence = np.mean(confidences)
            confidence_stability = 1.0 - np.var(confidences)
            
            performance_score = (accuracy * 0.6 + avg_confidence * 0.3 + confidence_stability * 0.1)
            
            return np.clip(performance_score, 0.0, 1.0)
            
        except Exception as e:
            logger.error(f"Erreur calcul performance: {e}")
            return 0.5
    
    def _calculate_recent_accuracy(self, window_size: int) -> float:
        """Calcule accuracy récente"""
        try:
            if len(self.performance_history) < window_size:
                return 0.5
            
            recent_results = list(self.performance_history)[-window_size:]
            return sum(1 for r in recent_results if r['correct']) / len(recent_results)
            
        except Exception as e:
            logger.error(f"Erreur calcul accuracy récente: {e}")
            return 0.5
    
    def _simulate_performance_with_thresholds(self, test_thresholds: Dict[str, float]) -> float:
        """Simule performance avec jeu de seuils"""
        try:
            if len(self.performance_history) < 10:
                return 0.5
            
            # Simulation basée sur historique récent
            recent_results = list(self.performance_history)[-30:]
            
            # Calcul score basé sur nouveaux seuils
            base_accuracy = sum(1 for r in recent_results if r['correct']) / len(recent_results)
            
            # Ajustements selon seuils (simulation heuristique)
            confidence_factor = 1.0 + (test_thresholds['confidence'] - 0.5) * 0.2
            uncertainty_factor = 1.0 + (0.5 - test_thresholds['uncertainty']) * 0.2
            
            simulated_performance = base_accuracy * confidence_factor * uncertainty_factor
            
            return np.clip(simulated_performance, 0.0, 1.0)
            
        except Exception as e:
            logger.error(f"Erreur simulation performance: {e}")
            return 0.5
