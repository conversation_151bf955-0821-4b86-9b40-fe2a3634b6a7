# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 294 à 328
# Type: Méthode de la classe BaccaratPatternValidator

    def _validate_prediction(self, prediction: Dict, actual_outcome: int, pattern: Dict) -> Dict[str, Any]:
        """Valide une prédiction contre le résultat réel"""
        predicted_outcome = prediction.get('predicted_outcome')

        if predicted_outcome is None:
            return {
                'success': False,
                'accuracy': 0.0,
                'error_type': 'no_prediction',
                'confidence_penalty': 0.5
            }

        # Validation binaire
        success = (predicted_outcome == actual_outcome)
        accuracy = 1.0 if success else 0.0

        # Calcul pénalité confiance
        predicted_confidence = prediction.get('confidence', 0.5)
        confidence_penalty = self._calculate_confidence_penalty(predicted_confidence, success)

        # Mise à jour statistiques
        self.total_validations += 1
        if success:
            self.successful_validations += 1

        self.current_session_accuracy = self.successful_validations / self.total_validations

        return {
            'success': success,
            'accuracy': accuracy,
            'predicted_confidence': predicted_confidence,
            'confidence_penalty': confidence_penalty,
            'session_accuracy': self.current_session_accuracy,
            'prediction_details': prediction
        }