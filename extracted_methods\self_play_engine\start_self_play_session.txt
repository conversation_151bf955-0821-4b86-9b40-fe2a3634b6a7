# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 67 à 113
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def start_self_play_session(self, initial_history: List[int] = None) -> Dict[str, Any]:
        """
        Démarre une session de self-play

        Args:
            initial_history: Historique initial optionnel

        Returns:
            Dict contenant informations de session
        """
        try:
            if self.is_running:
                logger.warning("Session self-play déjà en cours")
                return {'success': False, 'message': 'Session déjà active'}

            # Initialisation session
            self.current_session = {
                'session_id': len(self.game_sessions) + 1,
                'start_time': time.time(),
                'initial_history': initial_history or [],
                'rounds_played': 0,
                'predictions_made': 0,
                'correct_predictions': 0,
                'patterns_discovered': 0,
                'adaptations_made': 0,
                'game_history': deque(maxlen=self.max_session_rounds),
                'session_performance': []
            }

            # Ajout historique initial
            if initial_history:
                self.current_session['game_history'].extend(initial_history)

            self.is_running = True
            self.is_paused = False

            logger.info(f"Session self-play démarrée - ID: {self.current_session['session_id']}")

            return {
                'success': True,
                'session_id': self.current_session['session_id'],
                'message': 'Session self-play démarrée'
            }

        except Exception as e:
            logger.error(f"Erreur démarrage self-play: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}