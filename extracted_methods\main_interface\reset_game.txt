# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 333 à 395
# Type: Méthode de la classe BaccaratPredictorApp

    def reset_game(self):
        """Remet à zéro le jeu"""
        try:
            self.results = []
            self.predictions = []
            self.current_round = 0

            # Reset paramètres modèle
            self.model_parameters = {
                'confidence_adjustment_factor': 0.0,
                'uncertainty_scaling': 1.0,
                'decision_threshold': 0.5,
                'prediction_bias': 0.0,
                'calibration_offset': 0.0
            }

            # 🔄 RESET COMPLET DU SYSTÈME AZR
            try:
                self.azr_system.reset_system()
                logger.info("✅ Système AZR réinitialisé avec succès")
            except Exception as azr_error:
                logger.warning(f"Erreur reset AZR (non critique): {azr_error}")
                # Réinitialisation manuelle des composants critiques
                if hasattr(self.azr_system, 'training_iterations'):
                    self.azr_system.training_iterations = 0
                if hasattr(self.azr_system, 'shared_parameters'):
                    self.azr_system.shared_parameters = {
                        'confidence_adjustment': 0.0,
                        'prediction_bias': 0.0,
                        'decision_threshold': 0.5,
                        'pattern_weights': {
                            'frequency': 0.4,
                            'streaks': 0.3,
                            'alternation': 0.3
                        }
                    }
                if hasattr(self.azr_system, 'training_history'):
                    for key in self.azr_system.training_history:
                        self.azr_system.training_history[key].clear()

            # Reset interface
            self.prediction_label.config(text="Cliquez sur 'COMMENCER' pour débuter")
            self.info_text.delete(1.0, tk.END)
            self.method_label.config(text="Méthode: AZR Authentique")
            self.azr_info_label.config(text="AZR: Système réinitialisé")

            # Reset boutons
            self.predict_btn.config(state='normal', text="🎯 COMMENCER")
            self.player_btn.config(state='disabled')
            self.banker_btn.config(state='disabled')
            self.status_label.config(text="Cliquez sur 'COMMENCER' pour débuter")

            # Message reset
            self.info_text.insert(tk.END, "🔄 Système réinitialisé - Prêt pour une nouvelle session\n")
            self.info_text.insert(tk.END, "✅ AZR: Historique d'apprentissage effacé\n")
            self.info_text.insert(tk.END, "✅ AZR: Paramètres partagés réinitialisés\n")
            self.info_text.see(tk.END)

            logger.info("🔄 Jeu réinitialisé")

        except Exception as e:
            logger.error(f"Erreur reset: {e}")
            messagebox.showerror("Erreur", f"Erreur reset: {e}")