# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 1653 à 1680
# Type: Méthode de la classe AZRSystem

    def _calculate_improvement_reward(self) -> float:
        """Calcule récompense basée sur amélioration/dégradation des performances"""
        try:
            if len(self.training_history['prediction_accuracy']) < self.performance_window:
                return 0.0

            # Performance actuelle vs précédente
            current_window = self.training_history['prediction_accuracy'][-self.performance_window:]
            previous_window = self.training_history['prediction_accuracy'][-2*self.performance_window:-self.performance_window]

            if len(previous_window) < self.performance_window:
                return 0.0

            current_accuracy = np.mean(current_window)
            previous_accuracy = np.mean(previous_window)
            improvement = current_accuracy - previous_accuracy

            # Récompense/pénalise l'amélioration/dégradation
            if improvement > 0:
                # Amélioration : récompense proportionnelle
                return min(1.5, improvement * 3.0)
            else:
                # Dégradation : pénalité proportionnelle (NÉGATIVE)
                return max(-1.5, improvement * 2.0)  # ✅ PERMET RÉCOMPENSES NÉGATIVES

        except Exception as e:
            logger.error(f"Erreur calcul récompense amélioration: {e}")
            return -0.2