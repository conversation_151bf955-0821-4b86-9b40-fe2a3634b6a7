# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 359 à 403
# Type: Méthode de la classe AZRSolver

    def _solve_pattern_task(self, task: AZRTask) -> AZRSolution:
        """Résout tâche de prédiction de pattern"""
        sequence = task.input_data['sequence']
        reasoning_steps = []

        # Étape 1: Analyse fréquence
        player_count = sum(1 for x in sequence if x == 0)
        player_freq = player_count / len(sequence)
        reasoning_steps.append(f"Fréquence Player: {player_freq:.2f}")

        # Étape 2: Analyse streaks
        current_streak = self._analyze_current_streak(sequence)
        reasoning_steps.append(f"Streak actuelle: {current_streak}")

        # Étape 3: Analyse alternance
        alternation_rate = self._calculate_alternation_rate(sequence)
        reasoning_steps.append(f"Taux alternance: {alternation_rate:.2f}")

        # Étape 4: Prédiction basée sur analyse
        prediction_score = self._calculate_prediction_score(
            player_freq, current_streak, alternation_rate
        )

        predicted_outcome = 0 if prediction_score > 0.5 else 1
        confidence = abs(prediction_score - 0.5) * 2  # Normalise confiance

        reasoning_steps.append(f"Score prédiction: {prediction_score:.3f}")
        reasoning_steps.append(f"Prédiction finale: {'Player' if predicted_outcome == 0 else 'Banker'}")

        solution = AZRSolution(
            solution_id=f"sol_{task.task_id}",
            task_id=task.task_id,
            predicted_output=predicted_outcome,
            confidence=confidence,
            reasoning_steps=reasoning_steps
        )

        # Évaluation par environnement
        quality_score = self.environment.evaluate_solution(task, solution)
        solution.quality_score = quality_score

        self.solution_history.append(solution)
        self._update_performance_metrics(solution)

        return solution