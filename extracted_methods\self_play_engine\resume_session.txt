# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 377 à 384
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def resume_session(self) -> Dict[str, Any]:
        """Reprend la session"""
        if not self.is_running:
            return {'success': False, 'message': 'Aucune session active'}

        self.is_paused = False
        logger.info("Session self-play reprise")
        return {'success': True, 'message': 'Session reprise'}