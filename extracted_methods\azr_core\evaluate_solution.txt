# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 100 à 114
# Type: Méthode de la classe AZREnvironment

    def evaluate_solution(self, task: A<PERSON>RTask, solution: AZRSolution) -> float:
        """Évalue qualité d'une solution (0.0 à 1.0)"""
        try:
            if task.task_type == 'pattern_prediction':
                return self._evaluate_pattern_solution(task, solution)
            elif task.task_type == 'sequence_analysis':
                return self._evaluate_sequence_solution(task, solution)
            elif task.task_type == 'trend_detection':
                return self._evaluate_trend_solution(task, solution)

            return 0.0

        except Exception as e:
            logger.error(f"Erreur évaluation solution: {e}")
            return 0.0