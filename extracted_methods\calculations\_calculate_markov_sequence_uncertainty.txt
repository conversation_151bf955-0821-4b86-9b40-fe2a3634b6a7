# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 706 à 740
# Type: Méthode de la classe UncertaintyCalculator

    def _calculate_markov_sequence_uncertainty(self, model, sequence: np.ndarray) -> float:
        """Calcule incertitude pour une séquence Markov unique"""
        try:
            with model.lock:
                # Collecter prédictions de tous les ordres disponibles
                predictions = []

                for order in range(1, min(model.max_order, len(sequence)) + 1):
                    if len(sequence) >= order:
                        state = tuple(sequence[-order:])

                        if state in model.models[order]:
                            total = sum(model.models[order][state].values())
                            count_1 = model.models[order][state].get(1, 0)
                            prob = (count_1 + model.smoothing) / (total + 2 * model.smoothing)
                            predictions.append(prob)

                if not predictions:
                    return 1.0  # Incertitude maximale si aucune prédiction

                # ✅ Incertitude stabilisée pour Markov
                if len(predictions) > 1:
                    # Utilise écart-type au lieu de variance pour réduire amplification
                    uncertainty = np.std(predictions) * 2.0  # Facteur réduit
                else:
                    # Pour un seul ordre, incertitude basée sur entropie
                    prob = predictions[0]
                    entropy = -(prob * np.log2(prob + 1e-10) + (1-prob) * np.log2(1-prob + 1e-10))
                    uncertainty = entropy  # Entropie normalisée pour binaire

                return np.clip(uncertainty, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Erreur incertitude séquence Markov: {e}")
            return 1.0