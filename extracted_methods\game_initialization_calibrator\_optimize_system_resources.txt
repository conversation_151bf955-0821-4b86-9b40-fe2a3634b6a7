# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\game_initialization_calibrator.py
# Lignes: 207 à 238
# Type: Méthode de la classe GameInitializationCalibrator

    def _optimize_system_resources(self) -> Dict[str, Any]:
        """Optimise ressources système pour partie 60 manches"""
        try:
            logger.info("⚡ Optimisation ressources système")
            
            # Configuration haute performance
            resource_config = {
                'ram_allocation': min(self.max_ram_gb, self.available_ram * 0.9),
                'cpu_cores_used': self.cpu_cores,
                'parallel_processing': True,
                'memory_optimization': 'high_performance',
                'cache_sizes': {
                    'pattern_cache': 25000,  # Optimisé pour 60 manches
                    'performance_cache': 5000,
                    'calibration_cache': 2000
                }
            }
            
            # Validation ressources suffisantes
            if resource_config['ram_allocation'] < 20.0:
                logger.warning(f"RAM disponible faible: {resource_config['ram_allocation']:.1f}GB")
            
            return {
                'success': True,
                'resource_config': resource_config,
                'performance_mode': 'optimized',
                'ready_for_60_rounds': True
            }
            
        except Exception as e:
            logger.error(f"Erreur optimisation ressources: {e}")
            return {'success': False, 'error': str(e)}