# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 252 à 296
# Type: Méthode de la classe UnlimitedThresholdManager

    def _test_threshold_variations_unlimited(self) -> Dict[str, Any]:
        """Teste variations seuils sans limitations"""
        try:
            current_performance = self._calculate_current_performance()
            best_performance = current_performance
            best_thresholds = self.current_thresholds.copy()
            
            # Test variations pour chaque seuil
            for threshold_name in self.current_thresholds.keys():
                current_value = self.current_thresholds[threshold_name]
                min_val, max_val = self.adaptation_ranges[threshold_name]
                
                # Variations agressives (pas de limitations)
                test_values = [
                    max(min_val, current_value - 0.1),  # Réduction agressive
                    max(min_val, current_value - 0.05), # Réduction modérée
                    min(max_val, current_value + 0.05), # Augmentation modérée
                    min(max_val, current_value + 0.1),  # Augmentation agressive
                    min_val + (max_val - min_val) * 0.1,  # Exploration bas
                    min_val + (max_val - min_val) * 0.9   # Exploration haut
                ]
                
                # Test chaque variation
                for test_value in test_values:
                    test_thresholds = self.current_thresholds.copy()
                    test_thresholds[threshold_name] = test_value
                    
                    # Simulation performance avec nouveaux seuils
                    simulated_performance = self._simulate_performance_with_thresholds(test_thresholds)
                    
                    if simulated_performance > best_performance:
                        best_performance = simulated_performance
                        best_thresholds = test_thresholds.copy()
            
            return {
                'thresholds': best_thresholds,
                'performance': best_performance
            }
            
        except Exception as e:
            logger.error(f"Erreur test variations seuils: {e}")
            return {
                'thresholds': self.current_thresholds.copy(),
                'performance': self._calculate_current_performance()
            }