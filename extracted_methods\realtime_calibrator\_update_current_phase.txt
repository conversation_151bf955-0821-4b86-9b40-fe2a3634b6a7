# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 235 à 246
# Type: Méthode de la classe RealtimeCalibrator

    def _update_current_phase(self):
        """Met à jour phase actuelle selon numéro de manche"""
        if self.current_round <= global_config.azr.warmup_phase_rounds:
            self.current_phase = 'warmup'
            self.warmup_complete = False
        elif self.current_round <= global_config.azr.prediction_end_round:
            if not self.warmup_complete:
                self.warmup_complete = True
                logger.info(f"Phase échauffement terminée - Manche {self.current_round}")
            self.current_phase = 'optimal'
        else:
            self.current_phase = 'post_optimal'