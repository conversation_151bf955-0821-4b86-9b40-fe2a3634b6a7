# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\parameters.py
# Lignes: 272 à 274
# Type: Méthode de la classe GlobalConfig

    def is_prediction_round(self, round_number: int) -> bool:
        """Vérifie si la manche est dans la fenêtre de prédiction"""
        return self.azr.prediction_start_round <= round_number <= self.azr.prediction_end_round