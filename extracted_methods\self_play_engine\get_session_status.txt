# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 463 à 483
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def get_session_status(self) -> Dict[str, Any]:
        """Obtient statut de la session courante"""
        if not self.current_session:
            return {
                'active': False,
                'message': 'Aucune session active'
            }

        return {
            'active': self.is_running,
            'paused': self.is_paused,
            'session_id': self.current_session['session_id'],
            'rounds_played': self.current_session['rounds_played'],
            'predictions_made': self.current_session['predictions_made'],
            'correct_predictions': self.current_session['correct_predictions'],
            'session_accuracy': (self.current_session['correct_predictions'] /
                               max(self.current_session['predictions_made'], 1)),
            'adaptations_made': self.current_session['adaptations_made'],
            'performance_metrics': self._calculate_performance_metrics(),
            'reasoner_stats': self.adaptive_reasoner.get_reasoning_statistics()
        }