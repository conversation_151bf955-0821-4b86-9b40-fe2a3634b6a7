# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 748 à 751
# Type: Méthode de la classe BaccaratPatternProposer

    def _get_exploration_sequence_length(self) -> int:
        """Obtient longueur de séquence pour exploration"""
        min_len, max_len = self.sequence_length_range
        return np.random.randint(min_len, max_len + 1)