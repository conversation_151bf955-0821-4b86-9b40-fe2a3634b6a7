# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 180 à 210
# Type: Méthode de la classe BaccaratPatternValidator

    def _apply_trend_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern de tendance"""
        segment_size = pattern.get('segment_size', 10)
        direction = pattern.get('direction', 0)

        if len(test_data) < segment_size:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        # Vérification continuation de la tendance
        recent_segment = test_data[-segment_size:]
        x = np.arange(len(recent_segment))
        current_slope = np.polyfit(x, recent_segment, 1)[0]

        expected_direction = 1 if pattern.get('slope', 0) > 0 else 0
        current_direction = 1 if current_slope > 0 else 0

        if current_direction == expected_direction:
            # Tendance continue
            predicted_outcome = direction
            confidence = pattern.get('confidence', 0.5)
        else:
            # Tendance s'inverse
            predicted_outcome = 1 - direction
            confidence = pattern.get('confidence', 0.5) * 0.6

        return {
            'predicted_outcome': predicted_outcome,
            'confidence': confidence,
            'reason': 'trend_analysis',
            'current_slope': current_slope
        }