# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 50 à 101
# Type: Méthode de la classe BaccaratPatternValidator

    def validate_pattern(self, pattern: Dict, test_data: List[int],
                        actual_outcome: Optional[int] = None) -> Dict[str, Any]:
        """
        Valide un pattern sur nouvelles données réelles

        Args:
            pattern: Pattern à valider
            test_data: Données de test (historique récent)
            actual_outcome: Résultat réel observé (si disponible)

        Returns:
            Dict contenant résultats de validation et récompense
        """
        try:
            validation_start = time.time()

            # Génération prédiction basée sur pattern
            prediction = self._apply_pattern(pattern, test_data)

            # Validation si résultat réel disponible
            if actual_outcome is not None:
                validation_result = self._validate_prediction(
                    prediction, actual_outcome, pattern
                )
            else:
                validation_result = self._prepare_validation(prediction, pattern)

            # Calcul récompense AZR
            reward = self._calculate_azr_reward(validation_result, pattern)

            # Mise à jour historique
            self._update_validation_history(pattern, validation_result, reward)

            validation_time = time.time() - validation_start

            result = {
                'pattern': pattern,
                'prediction': prediction,
                'actual_outcome': actual_outcome,
                'validation_result': validation_result,
                'reward': reward,
                'validation_time': validation_time,
                'confidence_adjusted': self._adjust_confidence(pattern, validation_result),
                'pattern_reliability': self._calculate_pattern_reliability(pattern)
            }

            logger.debug(f"Pattern validé: {pattern['type']} - Récompense: {reward:.3f}")
            return result

        except Exception as e:
            logger.error(f"Erreur validation pattern: {e}")
            return self._create_error_validation(pattern, str(e))