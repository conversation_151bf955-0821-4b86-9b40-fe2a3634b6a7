# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 572 à 614
# Type: Méthode de la classe MarkovModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def predict(self, sequence):
        """Prédiction Markov avec confiance basée sur fréquence états"""
        try:
            if len(sequence) < self.order:
                return 0.5, 0.3

            # 📊 MISE À JOUR MATRICE TRANSITIONS
            self._update_transitions(sequence)

            # 🔗 ÉTAT ACTUEL POUR PRÉDICTION
            current_state = tuple(sequence[-self.order:])

            if current_state in self.transition_matrix:
                transitions = self.transition_matrix[current_state]
                total_transitions = sum(transitions.values())

                if total_transitions > 0:
                    # 📈 PROBABILITÉ MARKOV
                    prob_player = transitions[0] / total_transitions
                    prob_banker = transitions[1] / total_transitions

                    markov_prediction = prob_player  # Probabilité Player

                    # 📊 CONFIANCE BASÉE SUR NOMBRE D'OBSERVATIONS
                    observation_confidence = min(0.8, total_transitions / 10)
                    markov_confidence = max(0.2, observation_confidence)
                else:
                    markov_prediction = 0.5
                    markov_confidence = 0.2
            else:
                # État jamais vu : prédiction basée sur fréquence globale
                if len(sequence) > 0:
                    markov_prediction = sum(1 for x in sequence if x == 0) / len(sequence)
                else:
                    markov_prediction = 0.5
                markov_confidence = 0.3

            self.prediction_history.append(markov_prediction)
            return float(markov_prediction), float(markov_confidence)

        except Exception as e:
            logger.error(f"Erreur Markov predict: {e}")
            return 0.5, 0.3