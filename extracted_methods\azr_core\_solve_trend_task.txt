# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 441 à 489
# Type: Méthode de la classe AZRSolver

    def _solve_trend_task(self, task: AZRTask) -> AZRSolution:
        """Résout tâche de détection de tendance"""
        sequence = task.input_data['sequence']
        window_size = task.input_data['window_size']
        reasoning_steps = []

        # Analyse tendances par fenêtres glissantes
        trends = []
        for i in range(len(sequence) - window_size + 1):
            window = sequence[i:i + window_size]
            player_count = sum(1 for x in window if x == 0)
            trend_score = player_count / window_size
            trends.append(trend_score)

        reasoning_steps.append(f"Tendances par fenêtre {window_size}: {trends}")

        # Détection tendance globale
        if len(trends) > 1:
            trend_direction = "croissante" if trends[-1] > trends[0] else "décroissante"
            trend_strength = abs(trends[-1] - trends[0])
        else:
            trend_direction = "stable"
            trend_strength = 0.0

        reasoning_steps.append(f"Tendance: {trend_direction}, force: {trend_strength:.2f}")

        result = {
            'direction': trend_direction,
            'strength': trend_strength,
            'trends': trends
        }

        confidence = min(0.9, 0.5 + trend_strength)

        solution = AZRSolution(
            solution_id=f"sol_{task.task_id}",
            task_id=task.task_id,
            predicted_output=result,
            confidence=confidence,
            reasoning_steps=reasoning_steps
        )

        quality_score = self.environment.evaluate_solution(task, solution)
        solution.quality_score = quality_score

        self.solution_history.append(solution)
        self._update_performance_metrics(solution)

        return solution