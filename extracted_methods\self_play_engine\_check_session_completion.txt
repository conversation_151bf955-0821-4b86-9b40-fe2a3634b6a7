# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 312 à 329
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def _check_session_completion(self) -> bool:
        """Vérifie si session doit se terminer"""
        if not self.current_session:
            return False

        # Critères de fin de session
        max_rounds_reached = (self.current_session['rounds_played'] >=
                            self.max_session_rounds)

        # Session peut se terminer si performance stable
        stable_performance = False
        if len(self.current_session['session_performance']) >= 20:
            recent_accuracy = [p['correct'] for p in
                             self.current_session['session_performance'][-20:]]
            accuracy_variance = np.var(recent_accuracy)
            stable_performance = accuracy_variance < 0.1

        return max_rounds_reached or stable_performance