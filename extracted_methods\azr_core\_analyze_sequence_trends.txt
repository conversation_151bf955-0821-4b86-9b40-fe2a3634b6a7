# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 1245 à 1285
# Type: Méthode de la classe AZRSystem

    def _analyze_sequence_trends(self, sequence: List[int], window_size: int) -> Dict[str, Any]:
        """Analyse tendances par fenêtres glissantes"""
        try:
            if len(sequence) < window_size:
                return {'trend_prediction': 0.5, 'trend_strength': 0.0}

            # Calcul tendances par fenêtres
            trends = []
            for i in range(len(sequence) - window_size + 1):
                window = sequence[i:i + window_size]
                player_ratio = sum(1 for x in window if x == 0) / window_size
                trends.append(player_ratio)

            if len(trends) < 2:
                return {'trend_prediction': 0.5, 'trend_strength': 0.0}

            # Analyse évolution tendance
            recent_trend = trends[-1]
            trend_evolution = recent_trend - trends[0]
            trend_strength = abs(trend_evolution)

            # Prédiction basée sur continuation tendance
            if trend_strength > 0.2:  # Tendance significative
                if trend_evolution > 0:  # Tendance croissante vers Player
                    trend_prediction = min(0.8, 0.5 + trend_strength)
                else:  # Tendance décroissante vers Banker
                    trend_prediction = max(0.2, 0.5 - trend_strength)
            else:
                trend_prediction = 0.5  # Pas de tendance claire

            return {
                'trend_prediction': trend_prediction,
                'trend_strength': trend_strength,
                'trend_evolution': trend_evolution,
                'recent_trend': recent_trend,
                'trends': trends
            }

        except Exception as e:
            logger.error(f"Erreur analyse tendances: {e}")
            return {'trend_prediction': 0.5, 'trend_strength': 0.0}