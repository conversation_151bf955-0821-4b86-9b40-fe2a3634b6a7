# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 41 à 46
# Type: Méthode

    def precision_score(y_true, y_pred, average='binary', zero_division=0):
        if not y_true or not y_pred:
            return 0.5
        tp = sum(1 for t, p in zip(y_true, y_pred) if t == 1 and p == 1)
        fp = sum(1 for t, p in zip(y_true, y_pred) if t == 0 and p == 1)
        return tp / (tp + fp) if (tp + fp) > 0 else zero_division