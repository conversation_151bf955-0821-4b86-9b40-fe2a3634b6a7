# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 475 à 499
# Type: Méthode de la classe BaccaratPatternProposer

    def _calculate_streak_continuation_probability(self, current_streak: Dict, recent_streaks: List[Dict]) -> float:
        """
        Calcule probabilité de continuation d'une série - LOGIQUE BACCARAT PURE
        SUPPRESSION des moyennes statistiques inadaptées au Baccarat
        """
        if not recent_streaks:
            return 0.5  # Probabilité neutre

        # ═══════════════════════════════════════════════════════════════════
        # LOGIQUE BACCARAT SPÉCIFIQUE (pas de moyennes statistiques)
        # ═══════════════════════════════════════════════════════════════════

        current_length = current_streak['length']

        # Règles empiriques Baccarat (pas de statistiques)
        if current_length == 2:
            return 0.65  # Séries de 2 tendent à continuer (observation Baccarat)
        elif current_length == 3:
            return 0.55  # Séries de 3 encore probables
        elif current_length == 4:
            return 0.45  # Séries de 4 commencent à faiblir
        elif current_length >= 5:
            return 0.35  # Séries longues tendent à se casser
        else:
            return 0.5   # Cas par défaut