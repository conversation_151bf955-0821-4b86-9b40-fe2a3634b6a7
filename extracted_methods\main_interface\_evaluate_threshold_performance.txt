# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 942 à 968
# Type: Méthode de la classe BaccaratPredictorApp

    def _evaluate_threshold_performance(self, threshold):
        """Évalue performance d'un seuil de décision"""
        try:
            if len(self.predictions) < 5 or len(self.results) < 5:
                return 0.5

            # Simule prédictions avec ce seuil
            correct_count = 0
            min_len = min(len(self.predictions), len(self.results))

            for i in range(-min_len, 0):
                pred = self.predictions[i]
                result = self.results[i]

                player_prob = pred.get('player_probability', 0.5)
                simulated_outcome = 0 if player_prob > threshold else 1

                if simulated_outcome == result:
                    correct_count += 1

            performance = correct_count / min_len
            self._last_threshold_perf = performance
            return performance

        except Exception as e:
            logger.error(f"Erreur évaluation seuil: {e}")
            return 0.5