2025-05-27 22:27:15,258 - __main__ - INFO - Chemins Python configurés avec succès
2025-05-27 22:27:15,629 - __main__ - INFO - Toutes les dépendances sont disponibles
2025-05-27 22:27:15,631 - __main__ - INFO - Ressources système: RAM 30.7GB, CPU 8 cœurs
2025-05-27 22:27:15,632 - __main__ - INFO - Initialisation système AZR Baccarat
2025-05-27 22:27:15,642 - __main__ - INFO - Configuration globale importée (fallback)
2025-05-27 22:27:17,224 - pattern_proposer - INFO - BaccaratPatternProposer initialisé avec paradigme AZR + Auto-optimisation longueurs
2025-05-27 22:27:17,224 - __main__ - ERROR - Erreur initialisation AZR: 'AZRConfig' object has no attribute 'validation_window'
2025-05-27 22:27:42,982 - __main__ - INFO - Chemins Python configurés avec succès
2025-05-27 22:27:43,284 - __main__ - INFO - Toutes les dépendances sont disponibles
2025-05-27 22:27:43,286 - __main__ - INFO - Ressources système: RAM 30.7GB, CPU 8 cœurs
2025-05-27 22:27:43,287 - __main__ - INFO - Initialisation système AZR Baccarat
2025-05-27 22:27:43,301 - __main__ - INFO - Configuration globale importée (fallback)
2025-05-27 22:27:44,671 - pattern_proposer - INFO - BaccaratPatternProposer initialisé avec paradigme AZR + Auto-optimisation longueurs
2025-05-27 22:27:44,672 - pattern_validator - INFO - BaccaratPatternValidator initialisé avec paradigme AZR
2025-05-27 22:27:44,672 - __main__ - ERROR - Erreur initialisation AZR: 'AZRConfig' object has no attribute 'max_game_history'
2025-05-27 22:28:02,977 - __main__ - INFO - Chemins Python configurés avec succès
2025-05-27 22:28:03,271 - __main__ - INFO - Toutes les dépendances sont disponibles
2025-05-27 22:28:03,273 - __main__ - INFO - Ressources système: RAM 30.7GB, CPU 8 cœurs
2025-05-27 22:28:03,273 - __main__ - INFO - Initialisation système AZR Baccarat
2025-05-27 22:28:03,282 - __main__ - INFO - Configuration globale importée (fallback)
2025-05-27 22:28:04,644 - pattern_proposer - INFO - BaccaratPatternProposer initialisé avec paradigme AZR + Auto-optimisation longueurs
2025-05-27 22:28:04,644 - pattern_validator - INFO - BaccaratPatternValidator initialisé avec paradigme AZR
2025-05-27 22:28:04,645 - adaptive_reasoner - INFO - BaccaratAdaptiveReasoner initialisé avec paradigme AZR + Optimisation méta-paramètres
2025-05-27 22:28:04,645 - pattern_proposer - INFO - BaccaratPatternProposer initialisé avec paradigme AZR + Auto-optimisation longueurs
2025-05-27 22:28:04,646 - pattern_validator - INFO - BaccaratPatternValidator initialisé avec paradigme AZR
2025-05-27 22:28:04,646 - adaptive_reasoner - INFO - BaccaratAdaptiveReasoner initialisé avec paradigme AZR + Optimisation méta-paramètres
2025-05-27 22:28:04,646 - self_play_engine - INFO - BaccaratSelfPlayEngine initialisé avec paradigme AZR
2025-05-27 22:28:04,647 - models - INFO - AZRBaccaratPredictor initialisé avec paradigme Absolute Zero
2025-05-27 22:28:04,649 - __main__ - WARNING - Import AZR core échoué: No module named 'azr_core.adaptive_reasoner'; 'azr_core' is not a package
2025-05-27 22:28:04,649 - __main__ - ERROR - Erreur import modules AZR: No module named 'gui_isolated'
2025-05-27 22:32:18,539 - __main__ - INFO - Chemins Python configurés avec succès
2025-05-27 22:32:18,835 - __main__ - INFO - Toutes les dépendances sont disponibles
2025-05-27 22:32:18,837 - __main__ - INFO - Ressources système: RAM 30.7GB, CPU 8 cœurs
2025-05-27 22:32:18,838 - __main__ - INFO - Initialisation système AZR Baccarat
2025-05-27 22:32:18,844 - __main__ - INFO - Configuration globale importée (fallback)
2025-05-27 22:32:20,120 - pattern_proposer - INFO - BaccaratPatternProposer initialisé avec paradigme AZR + Auto-optimisation longueurs
2025-05-27 22:32:20,120 - pattern_validator - INFO - BaccaratPatternValidator initialisé avec paradigme AZR
2025-05-27 22:32:20,121 - adaptive_reasoner - INFO - BaccaratAdaptiveReasoner initialisé avec paradigme AZR + Optimisation méta-paramètres
2025-05-27 22:32:20,121 - pattern_proposer - INFO - BaccaratPatternProposer initialisé avec paradigme AZR + Auto-optimisation longueurs
2025-05-27 22:32:20,122 - pattern_validator - INFO - BaccaratPatternValidator initialisé avec paradigme AZR
2025-05-27 22:32:20,122 - adaptive_reasoner - INFO - BaccaratAdaptiveReasoner initialisé avec paradigme AZR + Optimisation méta-paramètres
2025-05-27 22:32:20,123 - self_play_engine - INFO - BaccaratSelfPlayEngine initialisé avec paradigme AZR
2025-05-27 22:32:20,123 - models - INFO - AZRBaccaratPredictor initialisé avec paradigme Absolute Zero
2025-05-27 22:32:20,125 - __main__ - WARNING - Import AZR core échoué: No module named 'azr_core.adaptive_reasoner'; 'azr_core' is not a package
2025-05-27 22:32:20,125 - __main__ - ERROR - Erreur import modules AZR: No module named 'gui_isolated'
2025-05-27 22:33:06,525 - __main__ - INFO - Chemins Python configurés avec succès
2025-05-27 22:33:06,826 - __main__ - INFO - Toutes les dépendances sont disponibles
2025-05-27 22:33:06,828 - __main__ - INFO - Ressources système: RAM 30.7GB, CPU 8 cœurs
2025-05-27 22:33:06,828 - __main__ - INFO - Initialisation système AZR Baccarat
2025-05-27 22:33:06,834 - __main__ - INFO - Configuration globale importée
2025-05-27 22:33:08,295 - __main__ - INFO - Modules AZR core importés avec succès
2025-05-27 22:33:08,297 - pattern_proposer - INFO - BaccaratPatternProposer initialisé avec paradigme AZR + Auto-optimisation longueurs
2025-05-27 22:33:08,297 - pattern_validator - INFO - BaccaratPatternValidator initialisé avec paradigme AZR
2025-05-27 22:33:08,297 - adaptive_reasoner - INFO - BaccaratAdaptiveReasoner initialisé avec paradigme AZR + Optimisation méta-paramètres
2025-05-27 22:33:08,298 - pattern_proposer - INFO - BaccaratPatternProposer initialisé avec paradigme AZR + Auto-optimisation longueurs
2025-05-27 22:33:08,298 - pattern_validator - INFO - BaccaratPatternValidator initialisé avec paradigme AZR
2025-05-27 22:33:08,298 - adaptive_reasoner - INFO - BaccaratAdaptiveReasoner initialisé avec paradigme AZR + Optimisation méta-paramètres
2025-05-27 22:33:08,299 - self_play_engine - INFO - BaccaratSelfPlayEngine initialisé avec paradigme AZR
2025-05-27 22:33:08,299 - models - INFO - AZRBaccaratPredictor initialisé avec paradigme Absolute Zero
2025-05-27 22:33:08,300 - __main__ - INFO - Système AZR complet importé avec succès
2025-05-27 22:33:08,300 - __main__ - INFO - Modules AZR importés avec succès
2025-05-27 22:33:08,301 - __main__ - INFO - Configuration AZR validée
2025-05-27 22:33:08,301 - __main__ - INFO - Lancement interface graphique
2025-05-27 22:33:08,391 - azr_core - INFO - 🚀 AZR initialisé avec ensemble LSTM+LGBM+Markov
2025-05-27 22:33:08,770 - main_interface - INFO - 🚀 Prédicteur Baccarat AZR authentique initialisé
2025-05-27 22:33:08,770 - __main__ - INFO - Interface graphique démarrée
2025-05-27 22:33:15,737 - azr_core - INFO - Itération AZR 1: L_reward=0.840, S_reward=0.150, Combined=0.357
2025-05-27 22:33:15,737 - main_interface - ERROR - Erreur génération prédiction AZR: 'learned_bias'
2025-05-27 22:33:15,825 - main_interface - INFO - Prédiction manche 1: 👤 PLAYER (confiance: 40.0%)
2025-05-27 22:33:16,555 - azr_core - INFO - 🎯 AZR apprentissage optimisé: correct=True, reward_total=0.580, biais=0.000, conf_adj=0.000
2025-05-27 22:33:16,581 - main_interface - INFO - Résultat manche 1: 👤 PLAYER - ✅ CORRECT
2025-05-27 22:33:17,407 - azr_core - INFO - Itération AZR 2: L_reward=0.990, S_reward=0.150, Combined=0.402
2025-05-27 22:33:17,408 - azr_core - INFO - 🎯 AZR+Ensemble: Analyse séquence complète [1...1] → Prédiction manche 2
2025-05-27 22:33:17,412 - main_interface - INFO - Prédiction auto manche 2: 👤 PLAYER (confiance: 61.3%)
2025-05-27 22:33:18,285 - azr_core - INFO - 🎯 AZR apprentissage optimisé: correct=False, reward_total=-0.530, biais=-0.002, conf_adj=-0.003
2025-05-27 22:33:18,293 - main_interface - INFO - Résultat manche 2: 🏦 BANKER - ❌ INCORRECT
2025-05-27 22:33:19,098 - azr_core - INFO - Itération AZR 3: L_reward=0.640, S_reward=0.150, Combined=0.297
2025-05-27 22:33:19,098 - azr_core - INFO - 🎯 AZR+Ensemble: Analyse séquence complète [1...2] → Prédiction manche 3
2025-05-27 22:33:19,104 - main_interface - INFO - Prédiction auto manche 3: 🏦 BANKER (confiance: 77.9%)
2025-05-27 22:33:19,494 - azr_core - INFO - 🎯 AZR apprentissage optimisé: correct=False, reward_total=-0.680, biais=0.001, conf_adj=-0.007
2025-05-27 22:33:19,508 - main_interface - INFO - Résultat manche 3: 👤 PLAYER - ❌ INCORRECT
2025-05-27 22:33:20,323 - azr_core - INFO - Itération AZR 4: L_reward=0.990, S_reward=0.150, Combined=0.402
2025-05-27 22:33:20,324 - azr_core - INFO - 🎯 AZR+Ensemble: Analyse séquence complète [1...3] → Prédiction manche 4
2025-05-27 22:33:20,326 - main_interface - INFO - Prédiction auto manche 4: 👤 PLAYER (confiance: 75.2%)
2025-05-27 22:33:20,805 - azr_core - WARNING - 🚨 Pénalité escaladante appliquée: streak=3, factor=1.50
2025-05-27 22:33:20,805 - azr_core - INFO - 🎯 AZR apprentissage optimisé: correct=False, reward_total=-0.767, biais=-0.002, conf_adj=-0.162
2025-05-27 22:33:20,807 - main_interface - INFO - Résultat manche 4: 🏦 BANKER - ❌ INCORRECT
2025-05-27 22:33:21,622 - azr_core - INFO - Itération AZR 5: L_reward=0.990, S_reward=0.150, Combined=0.402
2025-05-27 22:33:21,623 - azr_core - INFO - 🎯 AZR+Ensemble: Analyse séquence complète [1...4] → Prédiction manche 5
2025-05-27 22:33:21,625 - main_interface - INFO - Prédiction auto manche 5: 👤 PLAYER (confiance: 57.6%)
2025-05-27 22:33:22,621 - azr_core - INFO - 🔄 Début réinitialisation complète système AZR
2025-05-27 22:33:22,665 - azr_core - INFO - ✅ Système AZR réinitialisé complètement
2025-05-27 22:33:22,665 - main_interface - INFO - ✅ Système AZR réinitialisé avec succès
2025-05-27 22:33:22,671 - main_interface - INFO - 🔄 Jeu réinitialisé
