# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 758 à 774
# Type: Méthode de la classe BaccaratPatternProposer

    def get_length_optimization_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques d'optimisation des longueurs"""
        return {
            'optimal_lengths': {
                'sequences': self.optimal_sequence_lengths.copy(),
                'cycles': self.optimal_cycle_lengths.copy(),
                'streaks': self.optimal_streak_lengths.copy()
            },
            'performance_data': {
                'sequences': dict(self.sequence_length_performance),
                'cycles': dict(self.cycle_length_performance),
                'streaks': dict(self.streak_length_performance)
            },
            'optimization_history_count': len(self.length_optimization_history),
            'pattern_attempts_count': self.pattern_attempts_count,
            'next_optimization_in': self.optimization_frequency - (self.pattern_attempts_count % self.optimization_frequency)
        }