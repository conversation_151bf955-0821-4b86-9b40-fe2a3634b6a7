"""
PROGRAMME PRINCIPAL AZR BACCARAT
================================

Point d'entrée principal du programme de prédiction Baccarat AZR.
Objectif: Prédire avec précision maximale 0 (Player) ou 1 (Banker).
"""

import sys
import os
import logging
from pathlib import Path

# Configuration du logging avec support Unicode
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('azr_baccarat.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def setup_python_path():
    """Configure le chemin Python pour les imports"""
    try:
        # Ajout du répertoire courant au path (tous les fichiers sont maintenant ici)
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))

        logger.info("Chemins Python configurés avec succès")
        return True

    except Exception as e:
        logger.error(f"Erreur configuration chemins Python: {e}")
        return False

def check_dependencies():
    """Vérifie les dépendances requises"""
    try:
        required_modules = [
            'numpy', 'tkinter', 'matplotlib', 'psutil'
        ]

        missing_modules = []

        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)

        if missing_modules:
            logger.error(f"Modules manquants: {missing_modules}")
            print(f"❌ Modules Python manquants: {', '.join(missing_modules)}")
            print("Installez-les avec: pip install " + " ".join(missing_modules))
            return False

        logger.info("Toutes les dépendances sont disponibles")
        return True

    except Exception as e:
        logger.error(f"Erreur vérification dépendances: {e}")
        return False

def check_system_resources():
    """Vérifie les ressources système"""
    try:
        import psutil

        # Vérification RAM
        memory = psutil.virtual_memory()
        ram_gb = memory.total / (1024**3)

        # Vérification CPU
        cpu_count = psutil.cpu_count()

        logger.info(f"Ressources système: RAM {ram_gb:.1f}GB, CPU {cpu_count} cœurs")

        if ram_gb < 8:
            logger.warning(f"RAM faible: {ram_gb:.1f}GB (recommandé: 16GB+)")

        if cpu_count < 4:
            logger.warning(f"CPU limité: {cpu_count} cœurs (recommandé: 8+)")

        return True

    except Exception as e:
        logger.error(f"Erreur vérification ressources: {e}")
        return False

def initialize_azr_system():
    """Initialise le système AZR"""
    try:
        logger.info("Initialisation système AZR Baccarat")

        # Import des modules AZR (version flat - tous dans le même dossier)
        from parameters import global_config
        logger.info("Configuration globale importée")

        # Import modules AZR principaux (version flat)
        from adaptive_reasoner import BaccaratAdaptiveReasoner
        from pattern_proposer import BaccaratPatternProposer
        from pattern_validator import BaccaratPatternValidator
        from self_play_engine import BaccaratSelfPlayEngine
        logger.info("Modules AZR core importés avec succès")

        # Import système AZR complet
        from azr_core import AZRSystem
        logger.info("Système AZR complet importé avec succès")

        logger.info("Modules AZR importés avec succès")

        # Vérification configuration (optionnelle)
        try:
            if hasattr(global_config, 'azr'):
                logger.info("Configuration AZR validée")
            else:
                logger.info("Configuration AZR par défaut utilisée")
        except:
            logger.info("Configuration AZR par défaut utilisée")

        return True

    except ImportError as e:
        logger.error(f"Erreur import modules AZR: {e}")
        return False
    except Exception as e:
        logger.error(f"Erreur initialisation AZR: {e}")
        return False

def launch_gui():
    """Lance l'interface graphique"""
    try:
        logger.info("Lancement interface graphique")

        # Import interface (maintenant dans le même dossier)
        from main_interface import BaccaratPredictorApp

        # Création et lancement interface
        app = BaccaratPredictorApp()

        logger.info("Interface graphique démarrée")

        # Démarrage boucle principale
        app.run()

        return True

    except ImportError as e:
        logger.error(f"Erreur import interface: {e}")
        return False
    except Exception as e:
        logger.error(f"Erreur lancement interface: {e}")
        return False

def main():
    """Fonction principale"""
    try:
        print("🎯 AZR BACCARAT - Prédicteur Binaire Haute Performance")
        print("=" * 60)

        # 1. Configuration chemins Python
        print("🔧 Configuration chemins Python...")
        if not setup_python_path():
            print("❌ Erreur configuration chemins")
            return 1
        print("✅ Chemins configurés")

        # 2. Vérification dépendances
        print("📦 Vérification dépendances...")
        if not check_dependencies():
            print("❌ Dépendances manquantes")
            return 1
        print("✅ Dépendances OK")

        # 3. Vérification ressources système
        print("💾 Vérification ressources système...")
        if not check_system_resources():
            print("❌ Erreur ressources système")
            return 1
        print("✅ Ressources OK")

        # 4. Initialisation système AZR
        print("🧠 Initialisation système AZR...")
        if not initialize_azr_system():
            print("❌ Erreur initialisation AZR")
            return 1
        print("✅ Système AZR prêt")

        # 5. Lancement interface graphique
        print("🖥️ Lancement interface graphique...")
        print("=" * 60)

        if not launch_gui():
            print("❌ Erreur lancement interface")
            return 1

        print("✅ Programme terminé avec succès")
        return 0

    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par utilisateur")
        logger.info("Arrêt programme par utilisateur")
        return 0
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale programme principal: {e}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
