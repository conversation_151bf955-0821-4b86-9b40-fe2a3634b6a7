# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 106 à 210
# Type: Méthode de la classe AdaptiveReasoner

    def reason_next_outcome(self, game_history: List[int],
                          current_round: int = None) -> Dict[str, Any]:
        """
        Raisonne sur prochaine issue sans modèle pré-entraîné
        CALIBRATION OPTIMALE: Fenêtre prédiction manches 31-60

        Args:
            game_history: Historique complet du jeu
            current_round: Numéro de manche actuel

        Returns:
            Dict contenant prédiction, confiance et raisonnement
        """
        try:
            reasoning_start = time.time()
            self.reasoning_sessions += 1

            # ═══════════════════════════════════════════════════════════════════
            # VÉRIFICATION FENÊTRE PRÉDICTION OPTIMALE (31-60)
            # ═══════════════════════════════════════════════════════════════════

            # Vérification si dans fenêtre de prédiction optimale
            if current_round is not None:
                if current_round < global_config.azr.prediction_start_round:
                    # Avant manche 31 - Pas de prédiction, seulement observation
                    return self._create_observation_mode_result(game_history, current_round,
                                                              "Observation - Avant fenêtre prédiction optimale")

                elif current_round > global_config.azr.prediction_end_round:
                    # Après manche 60 - Prédiction avec confiance réduite
                    return self._create_post_optimal_window_result(game_history, current_round,
                                                                 "Post-fenêtre optimale - Confiance réduite")

            # Vérification historique minimum
            if len(game_history) < global_config.azr.min_history_for_prediction:
                return self._create_insufficient_history_result(game_history, current_round)

            # ═══════════════════════════════════════════════════════════════════
            # TRAITEMENT FENÊTRE OPTIMALE (31-60)
            # ═══════════════════════════════════════════════════════════════════

            # Mise à jour historique avec fenêtre optimisée
            optimal_history_length = global_config.azr.max_history_length
            self.game_history.extend(game_history[-optimal_history_length:])

            # Fenêtre d'analyse adaptée à la position dans le jeu
            analysis_window = self._calculate_optimal_analysis_window(current_round, len(game_history))
            recent_history = list(self.game_history)[-analysis_window:]

            # 1. Proposition de nouveaux patterns avec calibration
            proposed_patterns = self.pattern_proposer.propose_patterns(recent_history)

            # 2. Combinaison avec patterns validés
            all_patterns = self._combine_patterns(proposed_patterns, self.successful_patterns)

            # 3. Validation et scoring des patterns
            validated_patterns = self._validate_and_score_patterns(all_patterns, recent_history)

            # 4. Calcul confiance adaptative
            confidence_scores = self._calculate_adaptive_confidence(validated_patterns)

            # 5. Prédiction finale par ensemble
            ensemble_prediction = self._make_ensemble_prediction(validated_patterns, confidence_scores)

            # 6. Calcul incertitude
            uncertainty = self._calculate_prediction_uncertainty(validated_patterns, confidence_scores)

            # 7. Génération recommandation
            recommendation = self._generate_recommendation(ensemble_prediction, confidence_scores, uncertainty)

            reasoning_time = time.time() - reasoning_start

            # Résultat final
            result = {
                'prediction': ensemble_prediction,
                'confidence': confidence_scores['overall'],
                'uncertainty': uncertainty,
                'recommendation': recommendation,
                'reasoning_details': {
                    'patterns_used': len(validated_patterns),
                    'pattern_types': list(set(p['type'] for p in validated_patterns)),
                    'reasoning_time': reasoning_time,
                    'session_number': self.reasoning_sessions,
                    'active_patterns': len(self.active_patterns)
                },
                'pattern_breakdown': self._create_pattern_breakdown(validated_patterns),
                'meta_info': {
                    'round': current_round,
                    'history_length': len(recent_history),
                    'total_predictions': self.total_predictions,
                    'session_accuracy': self._calculate_session_accuracy()
                }
            }

            # Mise à jour historique confiance
            self.confidence_history.append(confidence_scores['overall'])

            logger.info(f"Raisonnement AZR - Confiance: {confidence_scores['overall']:.3f}, "
                       f"Patterns: {len(validated_patterns)}, Temps: {reasoning_time:.3f}s")

            return result

        except Exception as e:
            logger.error(f"Erreur raisonnement adaptatif: {e}")
            return self._create_fallback_reasoning(game_history)