# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 627 à 655
# Type: Méthode de la classe BaccaratPredictorApp

    def _calculate_signal_consensus(self, pattern_adj, streak_adj, alternation_adj):
        """Calcule consensus entre différents signaux pour boost confiance"""
        try:
            signals = [pattern_adj, streak_adj, alternation_adj]

            # Filtre signaux significatifs (> 0.02 en valeur absolue)
            significant_signals = [s for s in signals if abs(s) > 0.02]

            if len(significant_signals) < 2:
                return 0.0  # Pas assez de signaux

            # Vérifie si signaux pointent dans même direction
            positive_signals = sum(1 for s in significant_signals if s > 0)
            negative_signals = sum(1 for s in significant_signals if s < 0)

            # Consensus fort si tous signaux dans même direction
            if positive_signals == len(significant_signals):
                consensus_strength = len(significant_signals) / 3.0  # Max 1.0
                return consensus_strength
            elif negative_signals == len(significant_signals):
                consensus_strength = len(significant_signals) / 3.0  # Max 1.0
                return consensus_strength
            else:
                # Signaux contradictoires → Faible consensus
                return 0.1

        except Exception as e:
            logger.error(f"Erreur calcul consensus: {e}")
            return 0.0