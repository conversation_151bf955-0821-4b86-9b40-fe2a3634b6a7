# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 189 à 220
# Type: Méthode de la classe BaccaratPatternProposer

    def _propose_trend_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns de tendances"""
        patterns = []

        if len(results) < 6:
            return patterns

        # Analyse tendances par segments
        segment_sizes = [6, 9, 12, 15]

        for segment_size in segment_sizes:
            if len(results) >= segment_size:
                segment = results[-segment_size:]

                # Calcul tendance (pente)
                x = np.arange(len(segment))
                trend_slope = np.polyfit(x, segment, 1)[0]

                # Confiance basée sur consistance de la tendance
                confidence = self._calculate_trend_confidence(segment, trend_slope)

                if confidence > self.confidence_threshold:
                    patterns.append({
                        'type': 'trend',
                        'pattern': 'increasing' if trend_slope > 0 else 'decreasing',
                        'slope': trend_slope,
                        'segment_size': segment_size,
                        'confidence': confidence,
                        'direction': 1 if trend_slope > 0 else 0
                    })

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:3]