# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 386 à 416
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def stop_session(self) -> Dict[str, Any]:
        """Arrête la session courante"""
        if not self.is_running or not self.current_session:
            return {'success': False, 'message': 'Aucune session active'}

        # Finalisation session
        self.current_session['end_time'] = time.time()
        self.current_session['duration'] = (self.current_session['end_time'] -
                                           self.current_session['start_time'])

        # Calcul statistiques finales
        final_stats = self._calculate_final_session_stats()
        self.current_session['final_stats'] = final_stats

        # Sauvegarde session
        self.game_sessions.append(self.current_session.copy())

        # Reset état
        self.is_running = False
        self.is_paused = False
        session_id = self.current_session['session_id']
        self.current_session = None

        logger.info(f"Session self-play arrêtée - ID: {session_id}")

        return {
            'success': True,
            'session_id': session_id,
            'final_stats': final_stats,
            'message': 'Session arrêtée'
        }