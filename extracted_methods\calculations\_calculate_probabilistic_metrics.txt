# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 356 à 375
# Type: Méthode de la classe MetricsCalculator

    def _calculate_probabilistic_metrics(self, y_true: List[int], y_proba: List[float]) -> Dict[str, float]:
        """Calcule métriques probabilistes"""
        try:
            # Log-loss
            epsilon = global_config.calculations.epsilon_value
            y_proba_clipped = np.clip(y_proba, epsilon, global_config.calculations.uncertainty_base - epsilon)
            log_loss = -np.mean(y_true * np.log(y_proba_clipped) +
                               (1 - y_true) * np.log(1 - y_proba_clipped))

            # Brier Score
            brier_score = np.mean((y_proba - y_true) ** global_config.calculations.brier_score_exponent)

            return {
                global_config.calculations.probabilistic_metric_names[0]: log_loss,
                global_config.calculations.probabilistic_metric_names[1]: brier_score
            }

        except Exception as e:
            logger.error(f"Erreur métriques probabilistes: {e}")
            return {}