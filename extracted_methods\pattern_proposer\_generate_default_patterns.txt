# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 534 à 543
# Type: Méthode de la classe BaccaratPatternProposer

    def _generate_default_patterns(self) -> Dict[str, List[Dict]]:
        """Génère patterns par défaut si historique insuffisant"""
        return {
            'sequences': [],
            'frequencies': [],
            'trends': [],
            'alternations': [],
            'streaks': [],
            'cycles': []
        }