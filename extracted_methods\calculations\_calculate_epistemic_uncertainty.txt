# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 551 à 575
# Type: Méthode de la classe UncertaintyCalculator

    def _calculate_epistemic_uncertainty(self, predictions: Dict[str, Dict[str, float]],
                                       weights: Dict[str, float] = None) -> float:
        """Calcule incertitude épistémique (variance entre modèles)"""
        try:
            # Extraire probabilités Banker de chaque modèle
            banker_probs = [pred.get('banker', 0.5) for pred in predictions.values()]

            if weights and len(weights) == len(banker_probs):
                # Variance pondérée
                weight_values = list(weights.values())
                weighted_mean = np.average(banker_probs, weights=weight_values)
                weighted_variance = np.average((np.array(banker_probs) - weighted_mean)**2, weights=weight_values)
                epistemic = weighted_variance
            else:
                # Variance simple
                epistemic = np.var(banker_probs)

            # ✅ Normalisation stabilisée (évite amplification excessive)
            # Utilise fonction racine carrée pour réduire l'amplification du bruit
            normalized_epistemic = math.sqrt(epistemic * 4.0) / 2.0  # Racine carrée pour stabiliser
            return min(normalized_epistemic, 1.0)

        except Exception as e:
            logger.error(f"Erreur incertitude épistémique: {e}")
            return 0.5