# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 541 à 557
# Type: Méthode de la classe MarkovModel

    def _update_transitions(self, sequence):
        """Met à jour matrice de transition"""
        if len(sequence) < self.order + 1:
            return

        for i in range(len(sequence) - self.order):
            # État actuel (ordre n)
            current_state = tuple(sequence[i:i+self.order])
            next_state = sequence[i+self.order]

            # Mise à jour compteurs
            if current_state not in self.transition_matrix:
                self.transition_matrix[current_state] = {0: 0, 1: 0}
                self.state_counts[current_state] = 0

            self.transition_matrix[current_state][next_state] += 1
            self.state_counts[current_state] += 1