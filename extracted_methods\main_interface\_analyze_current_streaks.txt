# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 553 à 589
# Type: Méthode de la classe BaccaratPredictorApp

    def _analyze_current_streaks(self):
        """Analyse streaks actuelles pour anti-streak bias"""
        try:
            if len(self.results) < 2:
                return {'anti_streak_bias': 0.0}

            # Détection streak actuelle
            current_streak = 1
            last_result = self.results[-1]

            for i in range(len(self.results) - 2, -1, -1):
                if self.results[i] == last_result:
                    current_streak += 1
                else:
                    break

            # Anti-streak bias (plus la streak est longue, plus on prédit l'opposé)
            if current_streak >= 3:
                # Streak de 3+ → Forte tendance anti-streak
                anti_bias_strength = min(0.15, current_streak * 0.03)
                anti_streak_bias = anti_bias_strength if last_result == 1 else -anti_bias_strength
            elif current_streak == 2:
                # Streak de 2 → Tendance anti-streak modérée
                anti_bias_strength = 0.05
                anti_streak_bias = anti_bias_strength if last_result == 1 else -anti_bias_strength
            else:
                anti_streak_bias = 0.0

            return {
                'anti_streak_bias': anti_streak_bias,
                'current_streak': current_streak,
                'streak_type': 'Player' if last_result == 0 else 'Banker'
            }

        except Exception as e:
            logger.error(f"Erreur analyse streaks: {e}")
            return {'anti_streak_bias': 0.0}