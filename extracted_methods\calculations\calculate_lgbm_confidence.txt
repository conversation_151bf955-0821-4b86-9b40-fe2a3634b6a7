# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 198 à 220
# Type: Méthode de la classe ConfidenceCalculator

    def calculate_lgbm_confidence(self, probas: np.ndarray, feature_importance: Dict[str, float] = None) -> np.ndarray:
        """Calcule confiance LGBM basée sur distance à 0.5 et importance features"""
        try:
            banker_probs = probas[:, 1] if probas.ndim > 1 else probas

            # Confiance de base (distance à 0.5)
            base_confidence = np.abs(banker_probs - 0.5) * 2.0

            # Facteur d'importance des features si disponible
            importance_factor = 1.0
            if feature_importance:
                # Moyenne des importances comme facteur de confiance
                importance_factor = min(2.0, np.mean(list(feature_importance.values())) / 100.0 + 0.5)

            confidence = base_confidence * importance_factor

            return np.clip(confidence,
                          global_config.calculations.confidence_min,
                          global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur confiance LGBM: {e}")
            return np.full(len(probas), global_config.calculations.default_confidence)