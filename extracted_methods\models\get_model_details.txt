# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 794 à 812
# Type: Méthode de la classe EnsembleModel

    def get_model_details(self):
        """Retourne détails des modèles pour diagnostic AZR"""
        return {
            'model_weights': self.model_weights.copy(),
            'performance_history': {k: v[-5:] for k, v in self.performance_history.items()},
            'ensemble_predictions_recent': self.ensemble_predictions[-5:],
            'confidence_history_recent': self.confidence_history[-5:],
            'epistemic_variance_recent': self.epistemic_variance_history[-5:],
            'aleatoric_variance_recent': self.aleatoric_variance_history[-5:],
            'lstm_predictions': getattr(self.lstm_model, 'prediction_history', [])[-5:],
            'lgbm_predictions': getattr(self.lgbm_model, 'prediction_history', [])[-5:],
            'markov_predictions': getattr(self.markov_model, 'prediction_history', [])[-5:],
            'ensemble_type': 'LSTM+LGBM+Markov_Autonomous',
            'scientific_metrics': {
                'avg_epistemic_variance': np.mean(self.epistemic_variance_history[-10:]) if self.epistemic_variance_history else 0.0,
                'avg_aleatoric_variance': np.mean(self.aleatoric_variance_history[-10:]) if self.aleatoric_variance_history else 0.0,
                'model_agreement_trend': 1.0 - np.mean(self.epistemic_variance_history[-5:]) if len(self.epistemic_variance_history) >= 5 else 0.5
            }
        }