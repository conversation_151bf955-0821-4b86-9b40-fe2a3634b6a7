# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 824 à 839
# Type: Méthode

def smooth_probabilities(probabilities: Dict[str, float], alpha: float = None) -> Dict[str, float]:
    """Lissage Laplace des probabilités"""
    try:
        if alpha is None:
            alpha = global_config.calculations.laplace_smoothing_alpha

        n_classes = len(probabilities)
        smoothed = {}

        for k, v in probabilities.items():
            smoothed[k] = (v + alpha) / (1 + alpha * n_classes)

        return normalize_probabilities(smoothed)
    except Exception as e:
        logger.error(f"Erreur lissage: {e}")
        return probabilities