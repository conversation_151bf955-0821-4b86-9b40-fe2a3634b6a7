# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 780 à 794
# Type: Méthode de la classe AdaptiveReasoner

    def _adjust_parameters_for_improvement(self):
        """Ajuste paramètres pour améliorer performance"""
        # Réduction learning rate si performance dégradée
        self.adaptive_learning_rate *= 0.9
        self.adaptive_learning_rate = max(self.adaptive_learning_rate, self.learning_rate_range[0])

        # Augmentation seuil confiance (plus conservateur)
        self.adaptive_confidence_threshold *= 1.1
        self.adaptive_confidence_threshold = min(self.adaptive_confidence_threshold, self.confidence_threshold_range[1])

        # Réduction exploration (plus exploitation)
        self.adaptive_exploration_rate *= 0.85
        self.adaptive_exploration_rate = max(self.adaptive_exploration_rate, self.exploration_rate_range[0])

        self._update_current_parameter_set()