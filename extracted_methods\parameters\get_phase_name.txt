# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\parameters.py
# Lignes: 329 à 336
# Type: Méthode

def get_phase_name(round_number: int) -> str:
    """Obtient le nom de la phase selon la manche"""
    if round_number <= global_config.azr.warmup_phase_rounds:
        return "warmup"
    elif round_number <= global_config.azr.prediction_end_round:
        return "optimal"
    else:
        return "post_optimal"