# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 480 à 490
# Type: Méthode de la classe BaccaratPatternValidator

    def _calculate_recent_alternation_score(self, recent_data: List[int]) -> float:
        """Calcule score d'alternance récent"""
        if len(recent_data) < 2:
            return 0.0

        alternations = 0
        for i in range(1, len(recent_data)):
            if recent_data[i] != recent_data[i-1]:
                alternations += 1

        return alternations / (len(recent_data) - 1)