# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 612 à 668
# Type: Méthode de la classe AdaptiveReasoner

    def _create_post_optimal_window_result(self, game_history: List[int],
                                         current_round: int, message: str) -> Dict[str, Any]:
        """Crée résultat après fenêtre optimale (après manche 60)"""
        # Prédiction avec confiance réduite
        recent_history = game_history[-15:]  # Historique très récent
        basic_patterns = self.pattern_proposer.propose_patterns(recent_history)

        # Prédiction simple basée sur tendance récente
        if len(recent_history) >= 5:
            recent_player = recent_history[-5:].count(0)
            recent_banker = recent_history[-5:].count(1)

            if recent_player > recent_banker:
                player_prob, banker_prob = 0.55, 0.45
                predicted_outcome = 0
            elif recent_banker > recent_player:
                player_prob, banker_prob = 0.45, 0.55
                predicted_outcome = 1
            else:
                player_prob, banker_prob = 0.5, 0.5
                predicted_outcome = 0
        else:
            player_prob, banker_prob = 0.5, 0.5
            predicted_outcome = 0

        # Confiance réduite après fenêtre optimale
        base_confidence = 0.3
        uncertainty = 0.7

        return {
            'prediction': {
                'player_probability': player_prob,
                'banker_probability': banker_prob,
                'predicted_outcome': predicted_outcome,
                'prediction_strength': abs(player_prob - banker_prob)
            },
            'confidence': base_confidence,
            'uncertainty': uncertainty,
            'recommendation': f"Prudence - Post-fenêtre optimale (Manche {current_round})",
            'reasoning_details': {
                'mode': 'post_optimal',
                'current_round': current_round,
                'optimal_window_end': global_config.azr.prediction_end_round,
                'confidence_reduction': 'Applied',
                'message': message
            },
            'pattern_breakdown': {
                'total_patterns': len(basic_patterns.get('trend', [])),
                'post_optimal_mode': True
            },
            'meta_info': {
                'round': current_round,
                'history_length': len(game_history),
                'in_optimal_window': False,
                'mode': 'post_optimal'
            }
        }