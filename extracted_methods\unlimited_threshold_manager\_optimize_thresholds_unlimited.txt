# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 182 à 250
# Type: Méthode de la classe UnlimitedThresholdManager

    def _optimize_thresholds_unlimited(self) -> Dict[str, Any]:
        """
        Optimise seuils sans limitations pour performance maximale
        """
        try:
            optimization_start = time.time()
            old_thresholds = self.current_thresholds.copy()
            
            # ═══════════════════════════════════════════════════════════════════
            # OPTIMISATION ILLIMITÉE HAUTE PERFORMANCE
            # ═══════════════════════════════════════════════════════════════════
            
            # Analyse performance actuelle
            current_performance = self._calculate_current_performance()
            
            # Test variations seuils (exploration agressive)
            best_thresholds = self._test_threshold_variations_unlimited()
            
            # Application meilleurs seuils trouvés
            if best_thresholds['performance'] > current_performance:
                self.current_thresholds.update(best_thresholds['thresholds'])
                improvement = best_thresholds['performance'] - current_performance
                
                # Mise à jour meilleure performance
                if best_thresholds['performance'] > self.best_performance_achieved:
                    self.best_performance_achieved = best_thresholds['performance']
                    self.performance_plateau_count = 0
                else:
                    self.performance_plateau_count += 1
            else:
                improvement = 0.0
                self.performance_plateau_count += 1
            
            # Adaptation agressive si plateau
            if self.performance_plateau_count >= 5:
                self._apply_aggressive_threshold_changes()
                self.performance_plateau_count = 0
            
            optimization_time = time.time() - optimization_start
            self.optimization_count += 1
            self.last_optimization_round = len(self.performance_history)
            
            # Enregistrement historique
            optimization_entry = {
                'optimization_count': self.optimization_count,
                'old_thresholds': old_thresholds,
                'new_thresholds': self.current_thresholds.copy(),
                'improvement': improvement,
                'optimization_time': optimization_time,
                'performance_plateau_count': self.performance_plateau_count,
                'timestamp': time.time()
            }
            self.optimization_history.append(optimization_entry)
            
            logger.info(f"Optimisation seuils illimitée - Amélioration: {improvement:.4f} - "
                       f"Temps: {optimization_time:.3f}s - Plateau: {self.performance_plateau_count}")
            
            return {
                'success': True,
                'old_thresholds': old_thresholds,
                'new_thresholds': self.current_thresholds.copy(),
                'improvement': improvement,
                'optimization_time': optimization_time,
                'performance_plateau_count': self.performance_plateau_count
            }
            
        except Exception as e:
            logger.error(f"Erreur optimisation seuils illimitée: {e}")
            return {'success': False, 'error': str(e)}