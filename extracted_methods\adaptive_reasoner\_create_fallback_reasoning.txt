# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 496 à 522
# Type: Méthode de la classe AdaptiveReasoner

    def _create_fallback_reasoning(self, game_history: List[int]) -> Dict[str, Any]:
        """Crée raisonnement de fallback en cas d'erreur"""
        return {
            'prediction': {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,
                'prediction_strength': 0.0
            },
            'confidence': 0.5,
            'uncertainty': 0.8,
            'recommendation': "Attendre - Erreur système",
            'reasoning_details': {
                'patterns_used': 0,
                'pattern_types': [],
                'reasoning_time': 0.0,
                'session_number': self.reasoning_sessions,
                'error': True
            },
            'pattern_breakdown': {'total_patterns': 0},
            'meta_info': {
                'round': None,
                'history_length': len(game_history),
                'total_predictions': self.total_predictions,
                'session_accuracy': 0.0
            }
        }