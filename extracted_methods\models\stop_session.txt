# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 196 à 215
# Type: Méthode de la classe AZRBaccaratPredictor

    def stop_session(self) -> Dict[str, Any]:
        """Arrête la session courante"""
        try:
            if not self.is_active:
                return {'success': False, 'message': 'Aucune session active'}

            # Arrêt session self-play
            stop_result = self.self_play_engine.stop_session()

            if stop_result['success']:
                self._end_session()

                logger.info(f"Session AZR arrêtée - ID: {stop_result['session_id']}")
                return stop_result
            else:
                return stop_result

        except Exception as e:
            logger.error(f"Erreur arrêt session: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}