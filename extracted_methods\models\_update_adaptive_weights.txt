# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 748 à 783
# Type: Méthode de la classe EnsembleModel

    def _update_adaptive_weights(self, sequence, predictions, confidences):
        """Met à jour poids adaptatifs basés sur performance récente"""
        try:
            # Évaluation simple basée sur cohérence avec tendance récente
            if len(sequence) >= 3:
                recent_trend = np.mean(sequence[-3:])

                # Calcul erreurs relatives à la tendance
                lstm_error = abs(predictions[0] - recent_trend)
                lgbm_error = abs(predictions[1] - recent_trend)
                markov_error = abs(predictions[2] - recent_trend)

                # Mise à jour historique performance
                self.performance_history['lstm'].append(1.0 - lstm_error)
                self.performance_history['lgbm'].append(1.0 - lgbm_error)
                self.performance_history['markov'].append(1.0 - markov_error)

                # Limitation historique
                for model in self.performance_history:
                    if len(self.performance_history[model]) > 10:
                        self.performance_history[model] = self.performance_history[model][-10:]

                # Calcul nouveaux poids basés sur performance moyenne
                if all(len(hist) >= 3 for hist in self.performance_history.values()):
                    lstm_perf = np.mean(self.performance_history['lstm'][-5:])
                    lgbm_perf = np.mean(self.performance_history['lgbm'][-5:])
                    markov_perf = np.mean(self.performance_history['markov'][-5:])

                    total_perf = lstm_perf + lgbm_perf + markov_perf
                    if total_perf > 0:
                        self.model_weights['lstm'] = lstm_perf / total_perf
                        self.model_weights['lgbm'] = lgbm_perf / total_perf
                        self.model_weights['markov'] = markov_perf / total_perf

        except Exception as e:
            logger.error(f"Erreur mise à jour poids adaptatifs: {e}")