# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 89 à 120
# Type: Méthode de la classe ConfidenceCalculator

    def calculate_prediction_confidence(self, probabilities: Dict[str, float],
                                      model_weights: Dict[str, float] = None) -> float:
        """
        Calcule confiance prédiction (adaptée de l'ancien système)
        """
        try:
            if not probabilities:
                return global_config.calculations.default_confidence

            # Probabilités principales
            player_prob = probabilities.get('player', self.max_prob_center)
            banker_prob = probabilities.get('banker', self.max_prob_center)

            # ✅ CONFIANCE STABILISÉE (sans amplification excessive)
            max_prob = max(player_prob, banker_prob)
            # Utilise fonction sigmoïde pour éviter amplification linéaire du bruit
            raw_confidence = abs(max_prob - self.max_prob_center) * 2.0  # Facteur réduit de 2.0 au lieu de multiplier

            # Fonction sigmoïde pour stabiliser : f(x) = 1 / (1 + e^(-k*(x-0.5)))
            k = 6.0  # Paramètre de pente
            confidence = 1.0 / (1.0 + math.exp(-k * (raw_confidence - 0.5)))

            # Ajustement selon poids modèles si disponibles (limité pour éviter bruit)
            if model_weights:
                weight_factor = self._calculate_weight_factor(model_weights)
                confidence = confidence * 0.8 + weight_factor * 0.2  # Mélange conservateur

            return np.clip(confidence, global_config.calculations.confidence_min, global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur calcul confiance: {e}")
            return global_config.calculations.default_confidence