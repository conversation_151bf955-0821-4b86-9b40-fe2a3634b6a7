# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 604 à 612
# Type: Méthode de la classe BaccaratPatternProposer

    def _record_cycle_attempt(self, length: int):
        """Enregistre tentative pour une longueur de cycle"""
        if length not in self.cycle_length_performance:
            self.cycle_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }
        self.cycle_length_performance[length]['attempts'] += 1