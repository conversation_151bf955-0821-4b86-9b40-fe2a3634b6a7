# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 1345 à 1375
# Type: Méthode de la classe AZRSystem

    def _detect_cycle_pattern(self, sequence: List[int], cycle_length: int) -> float:
        """Détecte la force d'un pattern cyclique de longueur donnée"""
        try:
            if len(sequence) < cycle_length * 2:
                return 0.0

            # Compare segments de même longueur
            matches = 0
            comparisons = 0

            for i in range(len(sequence) - cycle_length):
                if i + cycle_length * 2 <= len(sequence):
                    segment1 = sequence[i:i + cycle_length]
                    segment2 = sequence[i + cycle_length:i + cycle_length * 2]

                    # Compare éléments
                    for j in range(cycle_length):
                        if segment1[j] == segment2[j]:
                            matches += 1
                        comparisons += 1

            if comparisons == 0:
                return 0.0

            # Confiance = ratio de correspondances
            confidence = matches / comparisons
            return confidence

        except Exception as e:
            logger.error(f"Erreur détection cycle: {e}")
            return 0.0