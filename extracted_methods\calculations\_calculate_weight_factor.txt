# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 272 à 286
# Type: Méthode de la classe ConfidenceCalculator

    def _calculate_weight_factor(self, weights: Dict[str, float]) -> float:
        """Calcule facteur de pondération"""
        try:
            # Entropie des poids (diversité)
            entropy = -sum(w * math.log(w + global_config.calculations.log_epsilon) for w in weights.values() if w > 0)
            max_entropy = math.log(len(weights))

            # Facteur basé sur diversité
            diversity_factor = entropy / max_entropy if max_entropy > 0 else global_config.calculations.default_diversity_factor

            return self.confidence_base_factor + self.confidence_diversity_factor * diversity_factor

        except Exception as e:
            logger.error(f"Erreur facteur poids: {e}")
            return global_config.calculations.default_weight_factor