# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 389 à 420
# Type: Méthode de la classe PredictionCalculator

    def calculate_final_prediction(self, model_predictions: Dict[str, Dict[str, float]],
                                 model_weights: Dict[str, float]) -> Dict[str, Any]:
        """
        Calcule prédiction finale ensemble
        """
        try:
            if not model_predictions or not model_weights:
                return self._default_prediction()

            # Prédiction pondérée
            weighted_pred = self._calculate_weighted_prediction(model_predictions, model_weights)

            # Confiance
            confidence = self.confidence_calc.calculate_ensemble_confidence(
                model_predictions, model_weights
            )

            # Recommandation
            recommendation = self._determine_recommendation(weighted_pred, confidence)

            return {
                'player': weighted_pred.get('player', global_config.calculations.default_probability),
                'banker': weighted_pred.get('banker', global_config.calculations.default_probability),
                'confidence': confidence,
                'recommendation': recommendation,
                'model_predictions': model_predictions,
                'model_weights': model_weights
            }

        except Exception as e:
            logger.error(f"Erreur prédiction finale: {e}")
            return self._default_prediction()