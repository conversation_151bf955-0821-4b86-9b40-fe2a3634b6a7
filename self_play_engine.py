"""
SELF-PLAY ENGINE - AZR CORE
============================

Moteur d'auto-apprentissage pour Baccarat basé sur le paradigme AZR.
Implémente le cycle propose-validate-adapt sans entraînement externe.
"""

import numpy as np
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import time
import threading
from collections import deque, defaultdict

# Import modules AZR
from .adaptive_reasoner import BaccaratAdaptiveReasoner

# Import configuration centralisée
sys.path.insert(0, str(Path(__file__).parent.parent / "parameters"))
from parameters import global_config

# Configuration logging
logger = logging.getLogger(__name__)

class BaccaratSelfPlayEngine:
    """
    Moteur Self-Play pour Baccarat - Paradigme AZR
    
    Implémente le cycle complet d'auto-apprentissage :
    1. Propose patterns
    2. Valide sur données réelles
    3. Adapte stratégie
    4. Répète en continu
    """
    
    def __init__(self):
        """Initialisation du moteur self-play"""
        self.adaptive_reasoner = BaccaratAdaptiveReasoner()
        
        # État du moteur
        self.is_running = False
        self.is_paused = False
        self.self_play_thread = None
        
        # Historique et métriques
        self.game_sessions = []
        self.current_session = None
        self.total_rounds_played = 0
        self.learning_cycles = 0
        
        # Performance tracking
        self.performance_history = deque(maxlen=1000)
        self.adaptation_events = []
        self.pattern_evolution = defaultdict(list)
        
        # Paramètres self-play
        self.learning_rate = global_config.azr.learning_rate
        self.exploration_rate = global_config.azr.exploration_rate
        self.adaptation_threshold = global_config.azr.adaptation_threshold
        self.max_session_rounds = global_config.azr.max_session_rounds
        
        logger.info("BaccaratSelfPlayEngine initialisé avec paradigme AZR")
    
    def start_self_play_session(self, initial_history: List[int] = None) -> Dict[str, Any]:
        """
        Démarre une session de self-play
        
        Args:
            initial_history: Historique initial optionnel
            
        Returns:
            Dict contenant informations de session
        """
        try:
            if self.is_running:
                logger.warning("Session self-play déjà en cours")
                return {'success': False, 'message': 'Session déjà active'}
            
            # Initialisation session
            self.current_session = {
                'session_id': len(self.game_sessions) + 1,
                'start_time': time.time(),
                'initial_history': initial_history or [],
                'rounds_played': 0,
                'predictions_made': 0,
                'correct_predictions': 0,
                'patterns_discovered': 0,
                'adaptations_made': 0,
                'game_history': deque(maxlen=self.max_session_rounds),
                'session_performance': []
            }
            
            # Ajout historique initial
            if initial_history:
                self.current_session['game_history'].extend(initial_history)
            
            self.is_running = True
            self.is_paused = False
            
            logger.info(f"Session self-play démarrée - ID: {self.current_session['session_id']}")
            
            return {
                'success': True,
                'session_id': self.current_session['session_id'],
                'message': 'Session self-play démarrée'
            }
            
        except Exception as e:
            logger.error(f"Erreur démarrage self-play: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}
    
    def process_new_round(self, actual_outcome: int, round_number: int = None) -> Dict[str, Any]:
        """
        Traite un nouveau round de jeu
        
        Args:
            actual_outcome: Résultat réel (0=Player, 1=Banker)
            round_number: Numéro de round optionnel
            
        Returns:
            Dict contenant prédiction et analyse
        """
        try:
            if not self.is_running or not self.current_session:
                return {'success': False, 'message': 'Aucune session active'}
            
            if self.is_paused:
                return {'success': False, 'message': 'Session en pause'}
            
            round_start = time.time()
            
            # 1. Raisonnement sur prochaine issue (avant de connaître le résultat)
            current_history = list(self.current_session['game_history'])
            reasoning_result = self.adaptive_reasoner.reason_next_outcome(
                current_history, round_number
            )
            
            # 2. Ajout du résultat réel à l'historique
            self.current_session['game_history'].append(actual_outcome)
            self.current_session['rounds_played'] += 1
            self.total_rounds_played += 1
            
            # 3. Évaluation de la prédiction
            predicted_outcome = reasoning_result['prediction']['predicted_outcome']
            prediction_correct = (predicted_outcome == actual_outcome)
            
            # 4. Mise à jour statistiques session
            self.current_session['predictions_made'] += 1
            if prediction_correct:
                self.current_session['correct_predictions'] += 1
            
            # 5. Mise à jour du raisonneur avec le résultat
            patterns_used = reasoning_result.get('pattern_breakdown', {}).get('top_patterns', [])
            self.adaptive_reasoner.update_with_outcome(
                predicted_outcome, actual_outcome, patterns_used
            )
            
            # 6. Calcul métriques de performance
            session_accuracy = (self.current_session['correct_predictions'] / 
                              self.current_session['predictions_made'])
            
            # 7. Détection besoin d'adaptation
            adaptation_needed = self._check_adaptation_needed(session_accuracy, reasoning_result)
            
            # 8. Exécution adaptation si nécessaire
            adaptation_result = None
            if adaptation_needed:
                adaptation_result = self._execute_adaptation(reasoning_result, session_accuracy)
                self.current_session['adaptations_made'] += 1
            
            # 9. Mise à jour historique performance
            round_performance = {
                'round': round_number or self.current_session['rounds_played'],
                'predicted': predicted_outcome,
                'actual': actual_outcome,
                'correct': prediction_correct,
                'confidence': reasoning_result['confidence'],
                'uncertainty': reasoning_result['uncertainty'],
                'patterns_used': len(patterns_used),
                'reasoning_time': time.time() - round_start
            }
            
            self.current_session['session_performance'].append(round_performance)
            self.performance_history.append(round_performance)
            
            # 10. Incrémentation cycles d'apprentissage
            self.learning_cycles += 1
            
            # 11. Vérification fin de session
            session_complete = self._check_session_completion()
            
            # Résultat du round
            result = {
                'success': True,
                'round_number': round_number or self.current_session['rounds_played'],
                'prediction': reasoning_result['prediction'],
                'actual_outcome': actual_outcome,
                'prediction_correct': prediction_correct,
                'confidence': reasoning_result['confidence'],
                'uncertainty': reasoning_result['uncertainty'],
                'recommendation': reasoning_result['recommendation'],
                'session_accuracy': session_accuracy,
                'adaptation_executed': adaptation_needed,
                'adaptation_result': adaptation_result,
                'session_complete': session_complete,
                'reasoning_details': reasoning_result['reasoning_details'],
                'pattern_breakdown': reasoning_result['pattern_breakdown'],
                'performance_metrics': self._calculate_performance_metrics()
            }
            
            logger.debug(f"Round traité - Prédiction: {predicted_outcome}, "
                        f"Réel: {actual_outcome}, Correct: {prediction_correct}")
            
            return result
            
        except Exception as e:
            logger.error(f"Erreur traitement round: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}
    
    def _check_adaptation_needed(self, session_accuracy: float, 
                               reasoning_result: Dict[str, Any]) -> bool:
        """Vérifie si adaptation est nécessaire"""
        # Critères d'adaptation
        low_accuracy = session_accuracy < self.adaptation_threshold
        low_confidence = reasoning_result['confidence'] < 0.4
        high_uncertainty = reasoning_result['uncertainty'] > 0.7
        few_patterns = reasoning_result['reasoning_details']['patterns_used'] < 2
        
        # Adaptation nécessaire si plusieurs critères remplis
        criteria_met = sum([low_accuracy, low_confidence, high_uncertainty, few_patterns])
        
        return criteria_met >= 2
    
    def _execute_adaptation(self, reasoning_result: Dict[str, Any], 
                          session_accuracy: float) -> Dict[str, Any]:
        """Exécute adaptation de la stratégie"""
        try:
            adaptation_start = time.time()
            
            # Types d'adaptation possibles
            adaptations_applied = []
            
            # 1. Ajustement seuils de confiance
            if reasoning_result['confidence'] < 0.4:
                self.adaptive_reasoner.confidence_threshold *= 0.9  # Réduction seuil
                adaptations_applied.append('confidence_threshold_reduced')
            
            # 2. Augmentation exploration si peu de patterns
            if reasoning_result['reasoning_details']['patterns_used'] < 2:
                self.exploration_rate = min(self.exploration_rate * 1.1, 0.8)
                adaptations_applied.append('exploration_increased')
            
            # 3. Ajustement taux d'apprentissage basé sur performance
            if session_accuracy < 0.4:
                self.learning_rate = min(self.learning_rate * 1.2, 0.3)
                adaptations_applied.append('learning_rate_increased')
            elif session_accuracy > 0.7:
                self.learning_rate = max(self.learning_rate * 0.9, 0.05)
                adaptations_applied.append('learning_rate_decreased')
            
            # 4. Reset patterns si performance très faible
            if session_accuracy < 0.3 and self.current_session['predictions_made'] > 10:
                self.adaptive_reasoner.successful_patterns = []
                adaptations_applied.append('patterns_reset')
            
            # 5. Ajustement decay patterns
            if reasoning_result['uncertainty'] > 0.7:
                self.adaptive_reasoner.pattern_decay_rate = min(
                    self.adaptive_reasoner.pattern_decay_rate * 1.1, 0.2
                )
                adaptations_applied.append('pattern_decay_increased')
            
            adaptation_time = time.time() - adaptation_start
            
            # Enregistrement événement d'adaptation
            adaptation_event = {
                'timestamp': time.time(),
                'session_id': self.current_session['session_id'],
                'round': self.current_session['rounds_played'],
                'session_accuracy': session_accuracy,
                'adaptations_applied': adaptations_applied,
                'adaptation_time': adaptation_time,
                'reasoning_context': {
                    'confidence': reasoning_result['confidence'],
                    'uncertainty': reasoning_result['uncertainty'],
                    'patterns_used': reasoning_result['reasoning_details']['patterns_used']
                }
            }
            
            self.adaptation_events.append(adaptation_event)
            
            logger.info(f"Adaptation exécutée - {len(adaptations_applied)} changements")
            
            return {
                'adaptations_applied': adaptations_applied,
                'adaptation_time': adaptation_time,
                'new_parameters': {
                    'confidence_threshold': self.adaptive_reasoner.confidence_threshold,
                    'exploration_rate': self.exploration_rate,
                    'learning_rate': self.learning_rate,
                    'pattern_decay_rate': self.adaptive_reasoner.pattern_decay_rate
                }
            }
            
        except Exception as e:
            logger.error(f"Erreur adaptation: {e}")
            return {'error': str(e)}
    
    def _check_session_completion(self) -> bool:
        """Vérifie si session doit se terminer"""
        if not self.current_session:
            return False
        
        # Critères de fin de session
        max_rounds_reached = (self.current_session['rounds_played'] >= 
                            self.max_session_rounds)
        
        # Session peut se terminer si performance stable
        stable_performance = False
        if len(self.current_session['session_performance']) >= 20:
            recent_accuracy = [p['correct'] for p in 
                             self.current_session['session_performance'][-20:]]
            accuracy_variance = np.var(recent_accuracy)
            stable_performance = accuracy_variance < 0.1
        
        return max_rounds_reached or stable_performance
    
    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """Calcule métriques de performance"""
        if not self.current_session:
            return {}
        
        session_perf = self.current_session['session_performance']
        
        if not session_perf:
            return {'session_accuracy': 0.0}
        
        # Métriques session
        session_accuracy = np.mean([p['correct'] for p in session_perf])
        avg_confidence = np.mean([p['confidence'] for p in session_perf])
        avg_uncertainty = np.mean([p['uncertainty'] for p in session_perf])
        avg_patterns_used = np.mean([p['patterns_used'] for p in session_perf])
        
        # Tendances récentes (10 derniers rounds)
        recent_perf = session_perf[-10:] if len(session_perf) >= 10 else session_perf
        recent_accuracy = np.mean([p['correct'] for p in recent_perf])
        
        # Métriques globales
        global_accuracy = 0.0
        if self.performance_history:
            global_accuracy = np.mean([p['correct'] for p in self.performance_history])
        
        return {
            'session_accuracy': session_accuracy,
            'recent_accuracy': recent_accuracy,
            'global_accuracy': global_accuracy,
            'avg_confidence': avg_confidence,
            'avg_uncertainty': avg_uncertainty,
            'avg_patterns_used': avg_patterns_used,
            'total_rounds': self.current_session['rounds_played'],
            'learning_cycles': self.learning_cycles,
            'adaptations_made': self.current_session['adaptations_made']
        }
    
    def pause_session(self) -> Dict[str, Any]:
        """Met en pause la session"""
        if not self.is_running:
            return {'success': False, 'message': 'Aucune session active'}
        
        self.is_paused = True
        logger.info("Session self-play mise en pause")
        return {'success': True, 'message': 'Session mise en pause'}
    
    def resume_session(self) -> Dict[str, Any]:
        """Reprend la session"""
        if not self.is_running:
            return {'success': False, 'message': 'Aucune session active'}
        
        self.is_paused = False
        logger.info("Session self-play reprise")
        return {'success': True, 'message': 'Session reprise'}
    
    def stop_session(self) -> Dict[str, Any]:
        """Arrête la session courante"""
        if not self.is_running or not self.current_session:
            return {'success': False, 'message': 'Aucune session active'}
        
        # Finalisation session
        self.current_session['end_time'] = time.time()
        self.current_session['duration'] = (self.current_session['end_time'] - 
                                           self.current_session['start_time'])
        
        # Calcul statistiques finales
        final_stats = self._calculate_final_session_stats()
        self.current_session['final_stats'] = final_stats
        
        # Sauvegarde session
        self.game_sessions.append(self.current_session.copy())
        
        # Reset état
        self.is_running = False
        self.is_paused = False
        session_id = self.current_session['session_id']
        self.current_session = None
        
        logger.info(f"Session self-play arrêtée - ID: {session_id}")
        
        return {
            'success': True,
            'session_id': session_id,
            'final_stats': final_stats,
            'message': 'Session arrêtée'
        }
    
    def _calculate_final_session_stats(self) -> Dict[str, Any]:
        """Calcule statistiques finales de session"""
        if not self.current_session:
            return {}
        
        session = self.current_session
        performance = session['session_performance']
        
        if not performance:
            return {'accuracy': 0.0}
        
        # Statistiques de base
        total_rounds = len(performance)
        correct_predictions = sum(p['correct'] for p in performance)
        accuracy = correct_predictions / total_rounds if total_rounds > 0 else 0.0
        
        # Métriques avancées
        avg_confidence = np.mean([p['confidence'] for p in performance])
        avg_uncertainty = np.mean([p['uncertainty'] for p in performance])
        confidence_variance = np.var([p['confidence'] for p in performance])
        
        # Analyse tendances
        if total_rounds >= 10:
            first_half = performance[:total_rounds//2]
            second_half = performance[total_rounds//2:]
            
            first_half_accuracy = np.mean([p['correct'] for p in first_half])
            second_half_accuracy = np.mean([p['correct'] for p in second_half])
            improvement = second_half_accuracy - first_half_accuracy
        else:
            improvement = 0.0
        
        return {
            'total_rounds': total_rounds,
            'accuracy': accuracy,
            'correct_predictions': correct_predictions,
            'avg_confidence': avg_confidence,
            'avg_uncertainty': avg_uncertainty,
            'confidence_variance': confidence_variance,
            'improvement': improvement,
            'adaptations_made': session['adaptations_made'],
            'patterns_discovered': session['patterns_discovered'],
            'duration': session.get('duration', 0.0)
        }
    
    def get_session_status(self) -> Dict[str, Any]:
        """Obtient statut de la session courante"""
        if not self.current_session:
            return {
                'active': False,
                'message': 'Aucune session active'
            }
        
        return {
            'active': self.is_running,
            'paused': self.is_paused,
            'session_id': self.current_session['session_id'],
            'rounds_played': self.current_session['rounds_played'],
            'predictions_made': self.current_session['predictions_made'],
            'correct_predictions': self.current_session['correct_predictions'],
            'session_accuracy': (self.current_session['correct_predictions'] / 
                               max(self.current_session['predictions_made'], 1)),
            'adaptations_made': self.current_session['adaptations_made'],
            'performance_metrics': self._calculate_performance_metrics(),
            'reasoner_stats': self.adaptive_reasoner.get_reasoning_statistics()
        }
    
    def get_learning_history(self) -> Dict[str, Any]:
        """Obtient historique d'apprentissage"""
        return {
            'total_sessions': len(self.game_sessions),
            'total_rounds_played': self.total_rounds_played,
            'learning_cycles': self.learning_cycles,
            'adaptation_events': len(self.adaptation_events),
            'recent_adaptations': self.adaptation_events[-5:],
            'performance_trend': [p['correct'] for p in self.performance_history[-50:]],
            'session_summaries': [
                {
                    'session_id': s['session_id'],
                    'rounds': s['rounds_played'],
                    'accuracy': s.get('final_stats', {}).get('accuracy', 0.0),
                    'duration': s.get('duration', 0.0)
                }
                for s in self.game_sessions[-10:]  # 10 dernières sessions
            ]
        }
