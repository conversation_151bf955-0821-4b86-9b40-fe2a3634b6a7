# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main.py
# Lignes: 159 à 211
# Type: Méthode

def main():
    """Fonction principale"""
    try:
        print("🎯 AZR BACCARAT - Prédicteur Binaire Haute Performance")
        print("=" * 60)

        # 1. Configuration chemins Python
        print("🔧 Configuration chemins Python...")
        if not setup_python_path():
            print("❌ Erreur configuration chemins")
            return 1
        print("✅ Chemins configurés")

        # 2. Vérification dépendances
        print("📦 Vérification dépendances...")
        if not check_dependencies():
            print("❌ Dépendances manquantes")
            return 1
        print("✅ Dépendances OK")

        # 3. Vérification ressources système
        print("💾 Vérification ressources système...")
        if not check_system_resources():
            print("❌ Erreur ressources système")
            return 1
        print("✅ Ressources OK")

        # 4. Initialisation système AZR
        print("🧠 Initialisation système AZR...")
        if not initialize_azr_system():
            print("❌ Erreur initialisation AZR")
            return 1
        print("✅ Système AZR prêt")

        # 5. Lancement interface graphique
        print("🖥️ Lancement interface graphique...")
        print("=" * 60)

        if not launch_gui():
            print("❌ Erreur lancement interface")
            return 1

        print("✅ Programme terminé avec succès")
        return 0

    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par utilisateur")
        logger.info("Arrêt programme par utilisateur")
        return 0
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        logger.error(f"Erreur fatale programme principal: {e}")
        return 1