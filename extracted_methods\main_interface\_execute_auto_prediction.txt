# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 283 à 331
# Type: Méthode de la classe BaccaratPredictorApp

    def _execute_auto_prediction(self):
        """Exécute la génération automatique de prédiction"""
        try:
            self.current_round += 1

            # Génération prédiction AZR authentique
            prediction = self._generate_azr_prediction()

            if prediction:
                # Affichage prédiction
                outcome_text = "👤 PLAYER" if prediction['predicted_outcome'] == 0 else "🏦 BANKER"
                confidence = prediction['confidence']
                method = prediction.get('method', 'azr_authentic')

                pred_text = f"Manche {self.current_round}: {outcome_text}\n"
                pred_text += f"Confiance: {confidence:.1%}\n"
                pred_text += f"Méthode: {method}"

                self.prediction_label.config(text=pred_text)

                # Stockage prédiction
                self.predictions.append(prediction)

                # Affichage détails
                self._show_prediction_details(prediction)

                # Affichage statut AZR
                self._show_azr_status()

                # Log
                logger.info(f"Prédiction auto manche {self.current_round}: {outcome_text} (confiance: {confidence:.1%})")

                # Mise à jour interface pour indiquer prédiction automatique
                self.info_text.insert(tk.END, f"🤖 Prédiction automatique générée pour manche {self.current_round}\n")
                self.info_text.see(tk.END)

                # Réactiver boutons résultats
                self.player_btn.config(state='normal')
                self.banker_btn.config(state='normal')

                # Mise à jour statut
                self.status_label.config(text="Sélectionnez le résultat réel de la manche")

        except Exception as e:
            logger.error(f"Erreur exécution auto prédiction: {e}")
            # Réactiver boutons en cas d'erreur
            self.player_btn.config(state='normal')
            self.banker_btn.config(state='normal')
            self.status_label.config(text="Erreur - Sélectionnez le résultat")