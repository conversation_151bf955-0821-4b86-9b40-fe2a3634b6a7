# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 454 à 465
# Type: Méthode de la classe BaccaratPatternValidator

    def _create_error_validation(self, pattern: Dict, error_msg: str) -> Dict[str, Any]:
        """Crée validation d'erreur"""
        return {
            'pattern': pattern,
            'prediction': {'predicted_outcome': None, 'confidence': 0.0},
            'actual_outcome': None,
            'validation_result': {'success': False, 'error': error_msg},
            'reward': -0.1,
            'validation_time': 0.0,
            'confidence_adjusted': 0.1,
            'pattern_reliability': 0.0
        }