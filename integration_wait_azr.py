"""
INTÉGRATION SYSTÈME WAIT STRATÉGIQUE DANS MODÈLE AZR
====================================================

Intégration du moteur de décision WAIT stratégique dans le modèle AZR
pour améliorer significativement la précision des prédictions NON-WAIT.
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from conditions_wait_strategiques_azr import StrategicWAITDecisionEngine

logger = logging.getLogger(__name__)

class AZRWithStrategicWAIT:
    """
    Modèle AZR amélioré avec système WAIT stratégique
    
    OBJECTIF: Maximiser la précision des prédictions Player/Banker
    en utilisant des recommandations WAIT conditionnelles intelligentes
    """
    
    def __init__(self, azr_system):
        """
        Initialise AZR avec WAIT stratégique
        
        Args:
            azr_system: Instance du système AZR existant
        """
        self.azr_system = azr_system
        self.wait_engine = StrategicWAITDecisionEngine()
        
        # Métriques de performance
        self.performance_metrics = {
            'total_predictions': 0,
            'wait_recommendations': 0,
            'player_banker_predictions': 0,
            'correct_predictions': 0,
            'wait_accuracy_improvement': 0.0,
            'coverage_rate': 0.0,
            'precision_on_predictions': 0.0
        }
        
        # Historique pour analyse
        self.prediction_history = []
        self.wait_history = []
        
    def predict_with_strategic_wait(self, game_history: List[int], 
                                  round_number: int = None) -> Dict[str, Any]:
        """
        Prédiction AZR avec décision WAIT stratégique
        
        Returns:
            Dict contenant soit une prédiction Player/Banker soit une recommandation WAIT
        """
        try:
            # ═══════════════════════════════════════════════════════════════════
            # 1. PRÉDICTION AZR STANDARD
            # ═══════════════════════════════════════════════════════════════════
            
            azr_prediction = self.azr_system.predict_sequence(game_history)
            
            # ═══════════════════════════════════════════════════════════════════
            # 2. PRÉDICTION ENSEMBLE (si disponible)
            # ═══════════════════════════════════════════════════════════════════
            
            ensemble_prediction = None
            if hasattr(self.azr_system, 'ensemble_model') and self.azr_system.ensemble_model:
                try:
                    ensemble_prediction = self.azr_system.ensemble_model.predict(
                        np.array(game_history[-8:]).reshape(1, -1)
                    )
                except Exception as e:
                    logger.warning(f"Erreur prédiction ensemble: {e}")
            
            # ═══════════════════════════════════════════════════════════════════
            # 3. ANALYSE CONTEXTE JEU
            # ═══════════════════════════════════════════════════════════════════
            
            game_context = self._analyze_game_context(game_history, round_number)
            
            # ═══════════════════════════════════════════════════════════════════
            # 4. DÉCISION WAIT STRATÉGIQUE
            # ═══════════════════════════════════════════════════════════════════
            
            should_wait, wait_details = self.wait_engine.should_wait(
                azr_prediction, ensemble_prediction, game_context
            )
            
            # ═══════════════════════════════════════════════════════════════════
            # 5. GÉNÉRATION RÉSULTAT FINAL
            # ═══════════════════════════════════════════════════════════════════
            
            if should_wait:
                result = self._create_wait_recommendation(
                    azr_prediction, wait_details, game_context
                )
                self.performance_metrics['wait_recommendations'] += 1
            else:
                result = self._create_enhanced_prediction(
                    azr_prediction, ensemble_prediction, wait_details, game_context
                )
                self.performance_metrics['player_banker_predictions'] += 1
            
            # Mise à jour métriques
            self.performance_metrics['total_predictions'] += 1
            self._update_performance_metrics(result)
            
            # Sauvegarde historique
            self._save_prediction_to_history(result, should_wait, wait_details)
            
            return result
            
        except Exception as e:
            logger.error(f"Erreur prédiction AZR avec WAIT: {e}")
            return self._create_fallback_result()
    
    def _analyze_game_context(self, game_history: List[int], round_number: int) -> Dict[str, Any]:
        """Analyse le contexte actuel du jeu"""
        if len(game_history) < 5:
            return {'insufficient_history': True}
        
        # Calculs contextuels
        recent_sequence = game_history[-10:] if len(game_history) >= 10 else game_history
        
        # Streak actuel
        current_streak = self._calculate_current_streak(game_history)
        
        # Volatilité récente
        volatility = self._calculate_volatility(recent_sequence)
        
        # Taux d'alternance
        alternation_rate = self._calculate_alternation_rate(recent_sequence)
        
        # Fréquences
        player_freq = game_history.count(0) / len(game_history)
        banker_freq = game_history.count(1) / len(game_history)
        
        return {
            'round_number': round_number,
            'history_length': len(game_history),
            'current_streak': current_streak,
            'volatility': volatility,
            'alternation_rate': alternation_rate,
            'player_frequency': player_freq,
            'banker_frequency': banker_freq,
            'recent_sequence_length': len(recent_sequence),
            'in_optimal_window': 31 <= (round_number or 30) <= 60
        }
    
    def _calculate_current_streak(self, history: List[int]) -> int:
        """Calcule le streak actuel"""
        if len(history) < 2:
            return 0
        
        current_outcome = history[-1]
        streak = 1
        
        for i in range(len(history) - 2, -1, -1):
            if history[i] == current_outcome:
                streak += 1
            else:
                break
        
        return streak if current_outcome == 0 else -streak  # Positif pour Player, négatif pour Banker
    
    def _calculate_volatility(self, sequence: List[int]) -> float:
        """Calcule la volatilité de la séquence"""
        if len(sequence) < 3:
            return 0.0
        
        # Nombre de changements
        changes = sum(1 for i in range(1, len(sequence)) if sequence[i] != sequence[i-1])
        max_changes = len(sequence) - 1
        
        return changes / max_changes if max_changes > 0 else 0.0
    
    def _calculate_alternation_rate(self, sequence: List[int]) -> float:
        """Calcule le taux d'alternance"""
        if len(sequence) < 2:
            return 0.5
        
        alternations = sum(1 for i in range(1, len(sequence)) if sequence[i] != sequence[i-1])
        possible_alternations = len(sequence) - 1
        
        return alternations / possible_alternations if possible_alternations > 0 else 0.5
    
    def _create_wait_recommendation(self, azr_pred: Dict, wait_details: Dict, 
                                  context: Dict) -> Dict[str, Any]:
        """Crée une recommandation WAIT stratégique"""
        return {
            # Prédiction WAIT
            'player_probability': 0.5,
            'banker_probability': 0.5,
            'predicted_outcome': None,  # Pas de prédiction
            'prediction_strength': 0.0,
            
            # Métriques WAIT
            'confidence': 0.0,
            'uncertainty': 1.0,
            'recommendation': 'WAIT',
            
            # Détails WAIT stratégique
            'wait_reasoning': wait_details.get('reasoning', 'Conditions défavorables'),
            'wait_score': wait_details.get('wait_score', 0.0),
            'triggered_conditions': wait_details.get('triggered_conditions', []),
            'adaptive_threshold': wait_details.get('adaptive_threshold', 0.5),
            
            # Contexte
            'game_context': context,
            'round_number': context.get('round_number'),
            
            # Méta-informations
            'model_type': 'AZR_STRATEGIC_WAIT',
            'decision_type': 'WAIT',
            'azr_base_confidence': azr_pred.get('confidence', 0.0),
            'strategic_wait_active': True,
            
            # Détails techniques
            'reasoning_details': {
                'wait_engine_active': True,
                'conditions_evaluated': list(wait_details.get('condition_values', {}).keys()),
                'wait_decision_details': wait_details
            }
        }
    
    def _create_enhanced_prediction(self, azr_pred: Dict, ensemble_pred: Optional[Dict],
                                  wait_details: Dict, context: Dict) -> Dict[str, Any]:
        """Crée une prédiction améliorée (NON-WAIT)"""
        # Base sur prédiction AZR
        result = azr_pred.copy()
        
        # Améliorations stratégiques
        result.update({
            'recommendation': 'Player' if azr_pred.get('predicted_outcome') == 0 else 'Banker',
            'strategic_wait_active': True,
            'decision_type': 'PREDICT',
            'wait_score': wait_details.get('wait_score', 0.0),
            'wait_threshold': wait_details.get('adaptive_threshold', 0.5),
            'conditions_favorable': True,
            
            # Contexte enrichi
            'game_context': context,
            'round_number': context.get('round_number'),
            
            # Confiance ajustée (bonus pour avoir passé le filtre WAIT)
            'confidence_boost': 0.05,  # Bonus confiance pour prédictions sélectionnées
            
            # Méta-informations
            'model_type': 'AZR_STRATEGIC_ENHANCED',
            'ensemble_available': ensemble_pred is not None,
            
            # Détails techniques
            'reasoning_details': {
                **azr_pred.get('reasoning_details', {}),
                'wait_filter_passed': True,
                'strategic_enhancement_active': True,
                'wait_decision_details': wait_details
            }
        })
        
        # Ajustement confiance finale
        original_confidence = result.get('confidence', 0.5)
        enhanced_confidence = min(0.95, original_confidence + result['confidence_boost'])
        result['confidence'] = enhanced_confidence
        
        return result
    
    def _create_fallback_result(self) -> Dict[str, Any]:
        """Crée résultat de fallback en cas d'erreur"""
        return {
            'player_probability': 0.5,
            'banker_probability': 0.5,
            'predicted_outcome': None,
            'prediction_strength': 0.0,
            'confidence': 0.0,
            'uncertainty': 1.0,
            'recommendation': 'WAIT',
            'wait_reasoning': 'Erreur système - Abstention sécuritaire',
            'model_type': 'AZR_STRATEGIC_FALLBACK',
            'decision_type': 'WAIT',
            'error': True
        }
    
    def _update_performance_metrics(self, result: Dict):
        """Met à jour les métriques de performance"""
        # Calcul taux de couverture
        total = self.performance_metrics['total_predictions']
        wait_count = self.performance_metrics['wait_recommendations']
        predict_count = self.performance_metrics['player_banker_predictions']
        
        self.performance_metrics['coverage_rate'] = predict_count / total if total > 0 else 0.0
        
        # Autres métriques calculées lors de la validation des résultats
    
    def _save_prediction_to_history(self, result: Dict, should_wait: bool, wait_details: Dict):
        """Sauvegarde la prédiction dans l'historique"""
        history_entry = {
            'timestamp': len(self.prediction_history),
            'should_wait': should_wait,
            'result': result,
            'wait_details': wait_details,
            'round_number': result.get('round_number')
        }
        
        self.prediction_history.append(history_entry)
        
        # Limite taille historique
        if len(self.prediction_history) > 200:
            self.prediction_history = self.prediction_history[-100:]
    
    def validate_prediction(self, actual_outcome: int):
        """Valide la dernière prédiction avec le résultat réel"""
        if not self.prediction_history:
            return
        
        last_prediction = self.prediction_history[-1]
        result = last_prediction['result']
        
        # Si c'était une prédiction (non-WAIT)
        if not last_prediction['should_wait']:
            predicted_outcome = result.get('predicted_outcome')
            if predicted_outcome is not None:
                is_correct = (predicted_outcome == actual_outcome)
                
                # Mise à jour métriques
                if is_correct:
                    self.performance_metrics['correct_predictions'] += 1
                
                # Mise à jour moteur WAIT
                self.wait_engine.update_performance(is_correct, 
                    self.performance_metrics['correct_predictions'] / 
                    max(1, self.performance_metrics['player_banker_predictions'])
                )
                
                # Calcul précision sur prédictions
                self.performance_metrics['precision_on_predictions'] = (
                    self.performance_metrics['correct_predictions'] / 
                    max(1, self.performance_metrics['player_banker_predictions'])
                )
    
    def get_strategic_wait_statistics(self) -> Dict[str, Any]:
        """Retourne statistiques du système WAIT stratégique"""
        base_stats = self.wait_engine.get_wait_statistics()
        
        performance_stats = {
            'total_predictions': self.performance_metrics['total_predictions'],
            'wait_rate': (self.performance_metrics['wait_recommendations'] / 
                         max(1, self.performance_metrics['total_predictions'])),
            'coverage_rate': self.performance_metrics['coverage_rate'],
            'precision_on_predictions': self.performance_metrics['precision_on_predictions'],
            'accuracy_improvement': self._calculate_accuracy_improvement()
        }
        
        return {
            'strategic_wait_stats': base_stats,
            'performance_metrics': performance_stats,
            'system_active': True
        }
    
    def _calculate_accuracy_improvement(self) -> float:
        """Calcule l'amélioration de précision grâce au système WAIT"""
        if self.performance_metrics['player_banker_predictions'] == 0:
            return 0.0
        
        # Précision actuelle avec WAIT
        current_precision = self.performance_metrics['precision_on_predictions']
        
        # Estimation précision sans WAIT (baseline ~50%)
        baseline_precision = 0.50
        
        return current_precision - baseline_precision
