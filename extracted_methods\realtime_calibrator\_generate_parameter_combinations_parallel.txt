# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 480 à 521
# Type: Méthode de la classe RealtimeCalibrator

    def _generate_parameter_combinations_parallel(self, exploration_factor: float,
                                                 num_combinations: int) -> List[Dict[str, float]]:
        """Génère combinaisons paramètres en parallèle (haute capacité mémoire)"""
        try:
            combinations = []

            # Génération parallélisée avec tous les cœurs
            def generate_batch(batch_size: int) -> List[Dict[str, float]]:
                batch_combinations = []
                for _ in range(batch_size):
                    combination = {}
                    for param, (min_val, max_val) in self.parameter_ranges.items():
                        # Exploration élargie en phase warmup
                        if self.current_phase == 'warmup':
                            range_expansion = (max_val - min_val) * exploration_factor
                            expanded_min = max(min_val, min_val - range_expansion)
                            expanded_max = min(max_val, max_val + range_expansion)
                        else:
                            expanded_min, expanded_max = min_val, max_val

                        combination[param] = np.random.uniform(expanded_min, expanded_max)
                    batch_combinations.append(combination)
                return batch_combinations

            # Traitement parallèle par batch
            batch_size = num_combinations // self.cpu_cores
            futures = []

            with ThreadPoolExecutor(max_workers=self.cpu_cores) as executor:
                for _ in range(self.cpu_cores):
                    future = executor.submit(generate_batch, batch_size)
                    futures.append(future)

                # Collecte résultats
                for future in futures:
                    combinations.extend(future.result())

            return combinations[:num_combinations]

        except Exception as e:
            logger.error(f"Erreur génération combinaisons: {e}")
            return []