# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 157 à 177
# Type: Méthode de la classe ConfidenceCalculator

    def _calculate_model_consensus(self, predictions: Dict[str, Dict[str, float]]) -> float:
        """Calcule le consensus entre modèles (1 = accord parfait, 0 = désaccord total)"""
        try:
            if len(predictions) < 2:
                return 1.0  # Consensus parfait avec un seul modèle

            # Extraire probabilités Banker de chaque modèle
            banker_probs = []
            for pred in predictions.values():
                banker_probs.append(pred.get('banker', 0.5))

            # Consensus = 1 - variance normalisée
            variance = np.var(banker_probs)
            max_variance = 0.25  # Variance maximale pour probabilités entre 0 et 1
            consensus = 1.0 - min(variance / max_variance, 1.0)

            return consensus

        except Exception as e:
            logger.error(f"Erreur calcul consensus: {e}")
            return 0.5