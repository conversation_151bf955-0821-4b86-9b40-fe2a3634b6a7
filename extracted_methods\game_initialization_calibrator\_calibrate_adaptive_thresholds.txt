# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\game_initialization_calibrator.py
# Lignes: 325 à 352
# Type: Méthode de la classe GameInitializationCalibrator

    def _calibrate_adaptive_thresholds(self) -> Dict[str, Any]:
        """Calibre seuils adaptatifs pour début de partie"""
        try:
            logger.info("🎚️ Calibration seuils adaptatifs")
            
            # Seuils optimaux début de partie
            adaptive_thresholds = {
                'confidence_start': 0.1,         # Départ bas
                'uncertainty_start': 0.9,        # Départ haut
                'adaptation_rate': 0.1,          # Apprentissage rapide
                'optimization_frequency': 1,     # Chaque manche
                
                # Plages adaptation
                'confidence_range': (0.01, 0.99),
                'uncertainty_range': (0.01, 0.99),
                'unlimited_mode': True
            }
            
            return {
                'success': True,
                'adaptive_thresholds': adaptive_thresholds,
                'unlimited_performance_mode': True,
                'ready_for_adaptation': True
            }
            
        except Exception as e:
            logger.error(f"Erreur calibration seuils: {e}")
            return {'success': False, 'error': str(e)}