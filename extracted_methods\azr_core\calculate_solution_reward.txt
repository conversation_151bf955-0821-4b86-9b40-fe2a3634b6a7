# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 604 à 637
# Type: Méthode de la classe AZRSolver

    def calculate_solution_reward(self, solution: AZRSolution, actual_outcome: int = None,
                                 predicted_outcome: int = None, confidence: float = None) -> float:
        """Calcule récompense solution optimisée pour Baccarat AZR"""
        try:
            # 🎯 RÉCOMPENSE DE BASE (Précision)
            if actual_outcome is not None and predicted_outcome is not None:
                # Récompense basée sur précision réelle
                base_reward = 1.0 if predicted_outcome == actual_outcome else 0.0
            else:
                # Fallback sur quality_score si pas de résultat réel
                base_reward = 1.0 if solution.quality_score > 0.6 else 0.0

            # 🎯 BONUS/MALUS DE CONFIANCE CALIBRÉE
            confidence_adjustment = 0.0
            if confidence is not None and actual_outcome is not None and predicted_outcome is not None:
                if predicted_outcome == actual_outcome:
                    # Prédiction correcte : bonus proportionnel à la confiance
                    confidence_adjustment = confidence * 0.3
                else:
                    # Prédiction incorrecte : malus proportionnel à la confiance
                    confidence_adjustment = -confidence * 0.2

            # 🎯 BONUS QUALITÉ RAISONNEMENT
            reasoning_bonus = 0.0
            if hasattr(solution, 'reasoning_steps') and solution.reasoning_steps:
                reasoning_bonus = min(0.15, len(solution.reasoning_steps) * 0.03)

            # 🎯 RÉCOMPENSE FINALE (NON-NÉGATIVE)
            total_reward = base_reward + confidence_adjustment + reasoning_bonus
            return max(0.0, min(2.0, total_reward))  # Limité entre 0.0 et 2.0

        except Exception as e:
            logger.error(f"Erreur calcul solution reward: {e}")
            return 0.0