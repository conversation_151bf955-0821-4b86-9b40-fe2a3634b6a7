# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 95 à 98
# Type: Méthode de la classe AZREnvironment

    def _validate_trend_task(self, task: AZRTask) -> bool:
        """Valide tâche de détection de tendance"""
        required_keys = ['sequence', 'window_size']
        return all(key in task.input_data for key in required_keys)