# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 174 à 198
# Type: Méthode de la classe NewGameHandler

    def _configure_interface_for_new_game(self):
        """Configure interface pour nouvelle partie"""
        try:
            # Activation boutons prédiction
            if hasattr(self.main_interface, 'player_button'):
                self.main_interface.player_button.config(state='normal')

            if hasattr(self.main_interface, 'banker_button'):
                self.main_interface.banker_button.config(state='normal')

            # Mise à jour titre
            if hasattr(self.main_interface, 'root'):
                self.main_interface.root.title("🎯 AZR Baccarat - Nouvelle Partie (60 manches)")

            # Reset affichage prédiction
            if hasattr(self.main_interface, 'prediction_vars'):
                self.main_interface.prediction_vars['player'].set("Player: --%")
                self.main_interface.prediction_vars['banker'].set("Banker: --%")
                self.main_interface.prediction_vars['confidence'].set("Confiance: En attente")
                self.main_interface.prediction_vars['recommendation'].set("🎯 Prêt pour première manche")

            logger.info("Interface configurée pour nouvelle partie")

        except Exception as e:
            logger.error(f"Erreur configuration interface: {e}")