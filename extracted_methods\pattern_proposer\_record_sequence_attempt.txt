# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 594 à 602
# Type: Méthode de la classe BaccaratPatternProposer

    def _record_sequence_attempt(self, length: int):
        """Enregistre tentative pour une longueur de séquence"""
        if length not in self.sequence_length_performance:
            self.sequence_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }
        self.sequence_length_performance[length]['attempts'] += 1