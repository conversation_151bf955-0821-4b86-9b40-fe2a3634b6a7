# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 249 à 263
# Type: Méthode de la classe AZRBaccaratPredictor

    def _update_prediction_history(self, prediction_result: Dict[str, Any], round_number: int):
        """Met à jour historique des prédictions"""
        history_entry = {
            'timestamp': time.time(),
            'round_number': round_number,
            'prediction': prediction_result,
            'session_id': self.current_session
        }

        self.prediction_history.append(history_entry)

        # Limitation taille historique
        max_history = global_config.azr.max_validation_history
        if len(self.prediction_history) > max_history:
            self.prediction_history = self.prediction_history[-max_history:]