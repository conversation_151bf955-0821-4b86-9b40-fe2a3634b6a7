# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 34 à 90
# Type: Méthode de la classe UnlimitedThresholdManager

    def __init__(self):
        """Initialisation gestionnaire seuils illimités"""
        
        # ═══════════════════════════════════════════════════════════════════
        # SEUILS ADAPTATIFS ILLIMITÉS
        # ═══════════════════════════════════════════════════════════════════
        
        # Seuils actuels (départ conservateur, optimisation vers performance max)
        self.current_thresholds = {
            'confidence': 0.1,      # Départ très bas - monte vers optimum
            'uncertainty': 0.9,     # Départ très haut - descend vers optimum
            'prediction': 0.5,      # Neutre - auto-calibré
            'accuracy': 0.0,        # Aucune limite minimale
            'precision': 0.0,       # Aucune limite minimale
            'recall': 0.0          # Aucune limite minimale
        }
        
        # Historique performance par seuil
        self.threshold_performance_history = {
            threshold_name: deque(maxlen=1000) 
            for threshold_name in self.current_thresholds.keys()
        }
        
        # Plages adaptation (illimitées)
        self.adaptation_ranges = {
            'confidence': (0.001, 0.999),      # Plage quasi-complète
            'uncertainty': (0.001, 0.999),     # Plage quasi-complète
            'prediction': (0.001, 0.999),      # Plage quasi-complète
            'accuracy': (0.0, 1.0),           # Plage complète
            'precision': (0.0, 1.0),          # Plage complète
            'recall': (0.0, 1.0)              # Plage complète
        }
        
        # ═══════════════════════════════════════════════════════════════════
        # PARAMÈTRES OPTIMISATION ILLIMITÉE
        # ═══════════════════════════════════════════════════════════════════
        
        # Apprentissage adaptatif
        self.learning_rate = global_config.azr.adaptive_threshold_learning_rate
        self.optimization_frequency = global_config.azr.threshold_optimization_frequency
        
        # Métriques performance
        self.performance_history = deque(maxlen=5000)
        self.optimization_history = deque(maxlen=1000)
        
        # État optimisation
        self.optimization_count = 0
        self.last_optimization_round = 0
        self.best_performance_achieved = 0.0
        self.performance_plateau_count = 0
        
        # Mode performance illimitée
        self.unlimited_mode = global_config.azr.baccarat_unlimited_mode
        self.aggressive_optimization = True
        self.no_performance_limits = True
        
        logger.info("UnlimitedThresholdManager initialisé - Mode performance illimitée activé")