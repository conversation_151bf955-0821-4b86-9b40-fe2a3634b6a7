# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 39 à 80
# Type: Méthode de la classe BaccaratPredictorApp

    def __init__(self):
        """Initialise l'application avec système AZR authentique"""

        # Interface graphique
        self.root = tk.Tk()
        self.root.title("🎯 Prédicteur Baccarat AZR Authentique")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a2e')

        # Données de jeu
        self.results = []           # Résultats réels (0=Player, 1=Banker)
        self.predictions = []       # Prédictions générées
        self.current_round = 0      # Manche actuelle

        # 🚀 SYSTÈME AZR 100% OPÉRATIONNEL - BASÉ SUR RECHERCHES APPROFONDIES
        self.azr_system = AZRSystem()

        # MÉTRIQUES APPRENTISSAGE RÉEL
        self.learning_metrics = {
            'accuracy_history': [],           # Historique précision prédictions
            'confidence_calibration': [],    # Historique calibration confiance
            'task_difficulty_evolution': [], # Évolution difficulté tâches
            'capability_improvement': [],    # Amélioration capacités
            'learnability_scores': [],       # Scores learnability
            'solution_quality': []           # Qualité solutions
        }

        # PARAMÈTRES MODÈLE ÉVOLUTIFS
        self.model_parameters = {
            'confidence_adjustment_factor': 0.0,  # Facteur ajustement confiance
            'uncertainty_scaling': 1.0,          # Échelle incertitude
            'decision_threshold': 0.5,           # Seuil décision
            'prediction_bias': 0.0,              # Biais prédiction
            'calibration_offset': 0.0            # Offset calibration
        }

        # Interface utilisateur
        self.setup_ui()

        # Système AZR déjà initialisé dans AZRSystem()

        logger.info("🚀 Prédicteur Baccarat AZR authentique initialisé")