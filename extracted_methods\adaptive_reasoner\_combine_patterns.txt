# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 212 à 230
# Type: Méthode de la classe AdaptiveReasoner

    def _combine_patterns(self, proposed_patterns: Dict[str, List[Dict]],
                         successful_patterns: List[Dict]) -> List[Dict]:
        """Combine patterns proposés avec patterns validés"""
        all_patterns = []

        # Ajout patterns proposés
        for pattern_type, patterns in proposed_patterns.items():
            all_patterns.extend(patterns)

        # Ajout patterns validés récents (avec decay)
        for pattern in successful_patterns[-10:]:  # 10 derniers patterns validés
            # Application decay temporel
            age_factor = self._calculate_pattern_age_factor(pattern)
            if age_factor > 0.3:  # Seuil de pertinence
                pattern_copy = pattern.copy()
                pattern_copy['confidence'] *= age_factor
                all_patterns.append(pattern_copy)

        return all_patterns