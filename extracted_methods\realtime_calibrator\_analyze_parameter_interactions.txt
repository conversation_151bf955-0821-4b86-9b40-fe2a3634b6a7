# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 666 à 701
# Type: Méthode de la classe RealtimeCalibrator

    def _analyze_parameter_interactions(self) -> Dict[str, Any]:
        """Analyse interactions entre paramètres (haute capacité mémoire)"""
        try:
            # Matrice d'interactions (exploite 28GB RAM)
            param_names = list(self.calibrated_parameters.keys())
            interaction_matrix = np.zeros((len(param_names), len(param_names)))

            # Calcul interactions par paires
            for i, param1 in enumerate(param_names):
                for j, param2 in enumerate(param_names):
                    if i != j:
                        interaction_score = self._calculate_parameter_interaction(param1, param2)
                        interaction_matrix[i, j] = interaction_score

            # Identification interactions significatives
            significant_interactions = []
            threshold = 0.1

            for i, param1 in enumerate(param_names):
                for j, param2 in enumerate(param_names):
                    if i < j and abs(interaction_matrix[i, j]) > threshold:
                        significant_interactions.append({
                            'param1': param1,
                            'param2': param2,
                            'interaction_strength': interaction_matrix[i, j]
                        })

            return {
                'interaction_matrix': interaction_matrix,
                'significant_interactions': significant_interactions,
                'max_interaction': np.max(np.abs(interaction_matrix))
            }

        except Exception as e:
            logger.error(f"Erreur analyse interactions: {e}")
            return {'interaction_matrix': np.zeros((5, 5)), 'significant_interactions': []}