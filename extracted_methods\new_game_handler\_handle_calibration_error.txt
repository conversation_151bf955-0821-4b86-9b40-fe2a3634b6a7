# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 240 à 254
# Type: Méthode de la classe NewGameHandler

    def _handle_calibration_error(self, message: str, error_details: Dict[str, Any]):
        """Gère erreurs de calibration"""
        try:
            error_msg = f"{message}: {error_details.get('error', 'Erreur inconnue')}"
            logger.error(error_msg)

            # Mise à jour interface
            self._update_interface_status(f"❌ {message}")

            # Reset état
            self.calibration_in_progress = False
            self.game_active = False

        except Exception as e:
            logger.error(f"Erreur gestion erreur calibration: {e}")