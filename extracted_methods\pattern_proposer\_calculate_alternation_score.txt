# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 414 à 425
# Type: Méthode de la classe BaccaratPatternProposer

    def _calculate_alternation_score(self, segment: List[int]) -> float:
        """Calcule score d'alternance d'un segment"""
        if len(segment) < 2:
            return 0.0

        alternations = 0
        for i in range(1, len(segment)):
            if segment[i] != segment[i-1]:
                alternations += 1

        max_alternations = len(segment) - 1
        return alternations / max_alternations if max_alternations > 0 else 0.0