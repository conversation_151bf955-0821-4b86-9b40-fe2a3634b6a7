"""
CALIBRATEUR TEMPS RÉEL AZR - HAUTE PERFORMANCE 28GB RAM
=======================================================

Module de calibration temps réel exploitant 28GB RAM et tous les cœurs CPU.
Auto-calibration continue avec phase d'échauffement optimisée (manches 1-30).
"""

import numpy as np
import sys
import os
import time
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import logging
import psutil
import threading
from collections import deque
import gc

# Import configuration centralisée
sys.path.insert(0, str(Path(__file__).parent.parent / "parameters"))
from parameters import global_config

# Configuration logging
logger = logging.getLogger(__name__)


class RealtimeCalibrator:
    """
    Calibrateur temps réel haute performance pour AZR

    Exploite 28GB RAM et tous les cœurs CPU pour auto-calibration continue
    avec phase d'échauffement optimisée (manches 1-30).
    """

    def __init__(self):
        """Initialisation calibrateur haute performance"""

        # ═══════════════════════════════════════════════════════════════════
        # CONFIGURATION HAUTE PERFORMANCE
        # ═══════════════════════════════════════════════════════════════════

        # Ressources système
        self.max_ram_gb = global_config.azr.max_ram_usage_gb
        self.cpu_cores = mp.cpu_count() if global_config.azr.cpu_cores_usage == -1 else global_config.azr.cpu_cores_usage
        self.parallel_enabled = global_config.azr.parallel_processing_enabled

        # Pools de traitement parallèle
        self.thread_pool = ThreadPoolExecutor(max_workers=self.cpu_cores)
        self.process_pool = ProcessPoolExecutor(max_workers=self.cpu_cores // 2)

        # ═══════════════════════════════════════════════════════════════════
        # ÉTAT CALIBRATION
        # ═══════════════════════════════════════════════════════════════════

        # Phase actuelle
        self.current_round = 0
        self.current_phase = 'warmup'  # warmup, optimal, post_optimal
        self.warmup_complete = False

        # Paramètres auto-calibrés (valeurs initiales)
        self.calibrated_parameters = {
            'learning_rate': 0.12,
            'confidence_threshold': 0.35,
            'exploration_rate': 0.25,
            'adaptation_rate': 0.15,
            'pattern_decay_rate': 0.03
        }

        # ═══════════════════════════════════════════════════════════════════
        # MÉMOIRE HAUTE CAPACITÉ (28GB RAM)
        # ═══════════════════════════════════════════════════════════════════

        # Caches haute performance
        self.pattern_performance_cache = {}  # Cache performance patterns
        self.parameter_combination_cache = {}  # Cache combinaisons paramètres
        self.calibration_results_cache = {}  # Cache résultats calibrations

        # Historiques étendus
        self.performance_history = deque(maxlen=global_config.azr.performance_history_size)
        self.calibration_history = deque(maxlen=global_config.azr.calibration_history_size)
        self.parameter_evolution_history = deque(maxlen=5000)

        # Matrices de performance (haute capacité mémoire)
        self.parameter_interaction_matrix = np.zeros((100, 100))  # Interactions paramètres
        self.performance_prediction_matrix = np.zeros((1000, 50))  # Prédictions performance

        # ═══════════════════════════════════════════════════════════════════
        # CALIBRATION TEMPS RÉEL
        # ═══════════════════════════════════════════════════════════════════

        # Fréquences calibration
        self.warmup_calibration_freq = global_config.azr.warmup_calibration_frequency
        self.optimal_calibration_freq = global_config.azr.calibration_frequency_optimal_window
        self.post_optimal_calibration_freq = global_config.azr.calibration_frequency_post_optimal

        # Plages paramètres auto-calibrés
        self.parameter_ranges = {
            'learning_rate': global_config.azr.auto_learning_rate_range,
            'confidence_threshold': global_config.azr.auto_confidence_threshold_range,
            'exploration_rate': global_config.azr.auto_exploration_rate_range,
            'adaptation_rate': global_config.azr.auto_adaptation_rate_range,
            'pattern_decay_rate': global_config.azr.auto_pattern_decay_rate_range
        }

        # État calibration
        self.calibration_active = False
        self.last_calibration_round = 0
        self.calibration_thread = None

        # Métriques performance temps réel
        self.realtime_accuracy = 0.5
        self.realtime_confidence = 0.5
        self.realtime_stability = 0.5

        logger.info(f"RealtimeCalibrator initialisé - RAM: {self.max_ram_gb}GB, CPU: {self.cpu_cores} cœurs")

    def start_realtime_calibration(self):
        """Démarre calibration temps réel"""
        try:
            if self.calibration_active:
                return {'success': False, 'message': 'Calibration déjà active'}

            self.calibration_active = True

            # Démarrage thread calibration temps réel
            self.calibration_thread = threading.Thread(
                target=self._realtime_calibration_loop,
                daemon=True
            )
            self.calibration_thread.start()

            logger.info("Calibration temps réel démarrée")
            return {'success': True, 'message': 'Calibration temps réel active'}

        except Exception as e:
            logger.error(f"Erreur démarrage calibration: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}

    def process_round_result(self, round_number: int, prediction_result: Dict[str, Any],
                           actual_outcome: int) -> Dict[str, Any]:
        """
        Traite résultat d'une manche pour calibration temps réel

        Args:
            round_number: Numéro de manche
            prediction_result: Résultat prédiction AZR
            actual_outcome: Résultat réel (0=Player, 1=Banker)

        Returns:
            Dict contenant résultats calibration
        """
        try:
            self.current_round = round_number

            # Mise à jour phase
            self._update_current_phase()

            # Enregistrement performance
            performance_entry = {
                'round': round_number,
                'predicted': prediction_result.get('predicted_outcome', 0),
                'actual': actual_outcome,
                'correct': prediction_result.get('predicted_outcome', 0) == actual_outcome,
                'confidence': prediction_result.get('confidence', 0.5),
                'timestamp': time.time(),
                'phase': self.current_phase,
                'parameters': self.calibrated_parameters.copy()
            }

            self.performance_history.append(performance_entry)

            # Mise à jour métriques temps réel
            self._update_realtime_metrics()

            # Déclenchement calibration si nécessaire
            calibration_triggered = self._should_trigger_calibration()

            if calibration_triggered:
                calibration_result = self._trigger_immediate_calibration()
                return {
                    'success': True,
                    'round': round_number,
                    'phase': self.current_phase,
                    'calibration_triggered': True,
                    'calibration_result': calibration_result,
                    'updated_parameters': self.calibrated_parameters.copy(),
                    'realtime_metrics': self._get_realtime_metrics()
                }
            else:
                return {
                    'success': True,
                    'round': round_number,
                    'phase': self.current_phase,
                    'calibration_triggered': False,
                    'current_parameters': self.calibrated_parameters.copy(),
                    'realtime_metrics': self._get_realtime_metrics()
                }

        except Exception as e:
            logger.error(f"Erreur traitement round {round_number}: {e}")
            return {'success': False, 'error': str(e)}

    def get_current_parameters(self) -> Dict[str, float]:
        """Obtient paramètres actuels calibrés"""
        return self.calibrated_parameters.copy()

    def get_calibration_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques de calibration"""
        return {
            'current_round': self.current_round,
            'current_phase': self.current_phase,
            'warmup_complete': self.warmup_complete,
            'calibration_active': self.calibration_active,
            'last_calibration_round': self.last_calibration_round,
            'calibrated_parameters': self.calibrated_parameters.copy(),
            'realtime_metrics': self._get_realtime_metrics(),
            'system_resources': {
                'ram_usage_gb': psutil.virtual_memory().used / (1024**3),
                'cpu_usage_percent': psutil.cpu_percent(),
                'available_cores': self.cpu_cores
            },
            'performance_history_size': len(self.performance_history),
            'calibration_history_size': len(self.calibration_history),
            'cache_sizes': {
                'pattern_performance': len(self.pattern_performance_cache),
                'parameter_combinations': len(self.parameter_combination_cache),
                'calibration_results': len(self.calibration_results_cache)
            }
        }

    def _update_current_phase(self):
        """Met à jour phase actuelle selon numéro de manche"""
        if self.current_round <= global_config.azr.warmup_phase_rounds:
            self.current_phase = 'warmup'
            self.warmup_complete = False
        elif self.current_round <= global_config.azr.prediction_end_round:
            if not self.warmup_complete:
                self.warmup_complete = True
                logger.info(f"Phase échauffement terminée - Manche {self.current_round}")
            self.current_phase = 'optimal'
        else:
            self.current_phase = 'post_optimal'

    def _update_realtime_metrics(self):
        """Met à jour métriques temps réel"""
        if len(self.performance_history) < 5:
            return

        # Calcul accuracy récente (10 dernières manches)
        recent_results = list(self.performance_history)[-10:]
        correct_predictions = sum(1 for r in recent_results if r['correct'])
        self.realtime_accuracy = correct_predictions / len(recent_results)

        # Calcul confiance moyenne récente
        recent_confidences = [r['confidence'] for r in recent_results]
        self.realtime_confidence = np.mean(recent_confidences)

        # Calcul stabilité (variance confiance)
        confidence_variance = np.var(recent_confidences)
        self.realtime_stability = max(0.0, 1.0 - confidence_variance)

    def _should_trigger_calibration(self) -> bool:
        """Détermine si calibration doit être déclenchée"""
        rounds_since_last = self.current_round - self.last_calibration_round

        if self.current_phase == 'warmup':
            return rounds_since_last >= self.warmup_calibration_freq
        elif self.current_phase == 'optimal':
            return rounds_since_last >= self.optimal_calibration_freq
        else:  # post_optimal
            return rounds_since_last >= self.post_optimal_calibration_freq

    def _get_realtime_metrics(self) -> Dict[str, float]:
        """Obtient métriques temps réel"""
        return {
            'accuracy': self.realtime_accuracy,
            'confidence': self.realtime_confidence,
            'stability': self.realtime_stability,
            'performance_score': (self.realtime_accuracy + self.realtime_confidence + self.realtime_stability) / 3
        }

    def _trigger_immediate_calibration(self) -> Dict[str, Any]:
        """Déclenche calibration immédiate haute performance"""
        try:
            calibration_start = time.time()

            if self.current_phase == 'warmup':
                # Calibration échauffement - Exploration intensive
                result = self._warmup_calibration()
            elif self.current_phase == 'optimal':
                # Calibration fenêtre optimale - Précision maximale
                result = self._optimal_window_calibration()
            else:
                # Calibration post-optimale - Stabilisation
                result = self._post_optimal_calibration()

            calibration_time = time.time() - calibration_start
            self.last_calibration_round = self.current_round

            # Enregistrement historique
            calibration_entry = {
                'round': self.current_round,
                'phase': self.current_phase,
                'calibration_time': calibration_time,
                'old_parameters': result.get('old_parameters', {}),
                'new_parameters': result.get('new_parameters', {}),
                'improvement': result.get('improvement', 0.0),
                'timestamp': time.time()
            }
            self.calibration_history.append(calibration_entry)

            logger.info(f"Calibration {self.current_phase} - Manche {self.current_round} - "
                       f"Amélioration: {result.get('improvement', 0.0):.3f} - "
                       f"Temps: {calibration_time:.3f}s")

            return result

        except Exception as e:
            logger.error(f"Erreur calibration immédiate: {e}")
            return {'success': False, 'error': str(e)}

    def _warmup_calibration(self) -> Dict[str, Any]:
        """
        Calibration phase échauffement (manches 1-30)
        Exploration intensive pour trouver meilleurs paramètres
        """
        try:
            old_parameters = self.calibrated_parameters.copy()

            # ═══════════════════════════════════════════════════════════════════
            # EXPLORATION INTENSIVE HAUTE PERFORMANCE (28GB RAM)
            # ═══════════════════════════════════════════════════════════════════

            # Génération combinaisons paramètres (parallélisé)
            parameter_combinations = self._generate_parameter_combinations_parallel(
                exploration_factor=global_config.azr.warmup_exploration_rate,
                num_combinations=500  # Haute capacité mémoire
            )

            # Évaluation parallèle toutes combinaisons
            evaluation_results = self._evaluate_parameter_combinations_parallel(
                parameter_combinations
            )

            # Sélection meilleure combinaison
            best_combination = max(evaluation_results, key=lambda x: x['performance_score'])

            # Mise à jour paramètres
            self.calibrated_parameters.update(best_combination['parameters'])

            improvement = best_combination['performance_score'] - self._calculate_current_performance()

            return {
                'success': True,
                'phase': 'warmup',
                'old_parameters': old_parameters,
                'new_parameters': self.calibrated_parameters.copy(),
                'improvement': improvement,
                'combinations_tested': len(parameter_combinations),
                'best_score': best_combination['performance_score']
            }

        except Exception as e:
            logger.error(f"Erreur calibration warmup: {e}")
            return {'success': False, 'error': str(e)}

    def _optimal_window_calibration(self) -> Dict[str, Any]:
        """
        Calibration fenêtre optimale (manches 31-60)
        Précision maximale avec ajustements fins
        """
        try:
            old_parameters = self.calibrated_parameters.copy()

            # ═══════════════════════════════════════════════════════════════════
            # CALIBRATION PRÉCISION MAXIMALE
            # ═══════════════════════════════════════════════════════════════════

            # Ajustements fins basés sur performance récente
            performance_trend = self._analyze_performance_trend()

            # Optimisation gradient parallélisée
            gradient_results = self._parallel_gradient_optimization()

            # Analyse interactions paramètres (haute capacité mémoire)
            interaction_analysis = self._analyze_parameter_interactions()

            # Application ajustements optimaux
            parameter_adjustments = self._calculate_optimal_adjustments(
                performance_trend, gradient_results, interaction_analysis
            )

            # Mise à jour paramètres avec ajustements fins
            for param, adjustment in parameter_adjustments.items():
                if param in self.calibrated_parameters:
                    old_value = self.calibrated_parameters[param]
                    new_value = np.clip(
                        old_value + adjustment,
                        self.parameter_ranges[param][0],
                        self.parameter_ranges[param][1]
                    )
                    self.calibrated_parameters[param] = new_value

            improvement = self._calculate_improvement(old_parameters, self.calibrated_parameters)

            return {
                'success': True,
                'phase': 'optimal',
                'old_parameters': old_parameters,
                'new_parameters': self.calibrated_parameters.copy(),
                'improvement': improvement,
                'adjustments_applied': parameter_adjustments,
                'performance_trend': performance_trend
            }

        except Exception as e:
            logger.error(f"Erreur calibration optimal: {e}")
            return {'success': False, 'error': str(e)}

    def _post_optimal_calibration(self) -> Dict[str, Any]:
        """
        Calibration post-optimale (manches 61+)
        Stabilisation et adaptation conservative
        """
        try:
            old_parameters = self.calibrated_parameters.copy()

            # ═══════════════════════════════════════════════════════════════════
            # STABILISATION CONSERVATIVE
            # ═══════════════════════════════════════════════════════════════════

            # Analyse stabilité performance
            stability_analysis = self._analyze_performance_stability()

            # Ajustements conservateurs seulement si dégradation
            if stability_analysis['performance_degrading']:
                # Ajustements minimaux pour stabilisation
                conservative_adjustments = self._calculate_conservative_adjustments(
                    stability_analysis
                )

                # Application ajustements conservateurs
                for param, adjustment in conservative_adjustments.items():
                    if param in self.calibrated_parameters:
                        old_value = self.calibrated_parameters[param]
                        new_value = np.clip(
                            old_value + adjustment * 0.5,  # Facteur conservateur
                            self.parameter_ranges[param][0],
                            self.parameter_ranges[param][1]
                        )
                        self.calibrated_parameters[param] = new_value

                improvement = self._calculate_improvement(old_parameters, self.calibrated_parameters)
            else:
                # Pas d'ajustement si performance stable
                improvement = 0.0

            return {
                'success': True,
                'phase': 'post_optimal',
                'old_parameters': old_parameters,
                'new_parameters': self.calibrated_parameters.copy(),
                'improvement': improvement,
                'stability_analysis': stability_analysis,
                'conservative_mode': True
            }

        except Exception as e:
            logger.error(f"Erreur calibration post-optimal: {e}")
            return {'success': False, 'error': str(e)}

    # ═══════════════════════════════════════════════════════════════════
    # MÉTHODES HAUTE PERFORMANCE (28GB RAM + TOUS CŒURS)
    # ═══════════════════════════════════════════════════════════════════

    def _generate_parameter_combinations_parallel(self, exploration_factor: float,
                                                 num_combinations: int) -> List[Dict[str, float]]:
        """Génère combinaisons paramètres en parallèle (haute capacité mémoire)"""
        try:
            combinations = []

            # Génération parallélisée avec tous les cœurs
            def generate_batch(batch_size: int) -> List[Dict[str, float]]:
                batch_combinations = []
                for _ in range(batch_size):
                    combination = {}
                    for param, (min_val, max_val) in self.parameter_ranges.items():
                        # Exploration élargie en phase warmup
                        if self.current_phase == 'warmup':
                            range_expansion = (max_val - min_val) * exploration_factor
                            expanded_min = max(min_val, min_val - range_expansion)
                            expanded_max = min(max_val, max_val + range_expansion)
                        else:
                            expanded_min, expanded_max = min_val, max_val

                        combination[param] = np.random.uniform(expanded_min, expanded_max)
                    batch_combinations.append(combination)
                return batch_combinations

            # Traitement parallèle par batch
            batch_size = num_combinations // self.cpu_cores
            futures = []

            with ThreadPoolExecutor(max_workers=self.cpu_cores) as executor:
                for _ in range(self.cpu_cores):
                    future = executor.submit(generate_batch, batch_size)
                    futures.append(future)

                # Collecte résultats
                for future in futures:
                    combinations.extend(future.result())

            return combinations[:num_combinations]

        except Exception as e:
            logger.error(f"Erreur génération combinaisons: {e}")
            return []

    def _evaluate_parameter_combinations_parallel(self, combinations: List[Dict[str, float]]) -> List[Dict[str, Any]]:
        """Évalue combinaisons paramètres en parallèle"""
        try:
            def evaluate_combination(combination: Dict[str, float]) -> Dict[str, Any]:
                # Simulation performance avec combinaison
                performance_score = self._simulate_performance_with_parameters(combination)

                return {
                    'parameters': combination,
                    'performance_score': performance_score,
                    'evaluation_time': time.time()
                }

            # Évaluation parallélisée
            results = []
            with ThreadPoolExecutor(max_workers=self.cpu_cores) as executor:
                futures = [executor.submit(evaluate_combination, combo) for combo in combinations]

                for future in futures:
                    try:
                        result = future.result(timeout=1.0)  # Timeout pour éviter blocage
                        results.append(result)
                    except Exception as e:
                        logger.warning(f"Erreur évaluation combinaison: {e}")

            return results

        except Exception as e:
            logger.error(f"Erreur évaluation parallèle: {e}")
            return []

    def _simulate_performance_with_parameters(self, parameters: Dict[str, float]) -> float:
        """Simule performance avec jeu de paramètres"""
        try:
            if len(self.performance_history) < 5:
                return 0.5  # Score neutre si pas assez d'historique

            # Simulation basée sur historique récent
            recent_results = list(self.performance_history)[-20:]

            # Calcul score basé sur paramètres
            base_accuracy = sum(1 for r in recent_results if r['correct']) / len(recent_results)

            # Ajustements selon paramètres
            learning_rate_factor = 1.0 + (parameters['learning_rate'] - 0.12) * 0.5
            confidence_factor = 1.0 + (0.35 - parameters['confidence_threshold']) * 0.3
            exploration_factor = 1.0 + (parameters['exploration_rate'] - 0.25) * 0.2

            # Score composite
            performance_score = base_accuracy * learning_rate_factor * confidence_factor * exploration_factor

            return np.clip(performance_score, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Erreur simulation performance: {e}")
            return 0.5

    def _analyze_performance_trend(self) -> Dict[str, Any]:
        """Analyse tendance performance récente"""
        try:
            if len(self.performance_history) < 10:
                return {'trend': 'insufficient_data', 'slope': 0.0}

            # Analyse sur 20 dernières manches
            recent_results = list(self.performance_history)[-20:]
            accuracies = []

            # Calcul accuracy par fenêtre glissante
            window_size = 5
            for i in range(len(recent_results) - window_size + 1):
                window = recent_results[i:i + window_size]
                accuracy = sum(1 for r in window if r['correct']) / len(window)
                accuracies.append(accuracy)

            if len(accuracies) < 2:
                return {'trend': 'insufficient_data', 'slope': 0.0}

            # Calcul tendance (régression linéaire simple)
            x = np.arange(len(accuracies))
            slope = np.polyfit(x, accuracies, 1)[0]

            # Classification tendance
            if slope > 0.02:
                trend = 'improving'
            elif slope < -0.02:
                trend = 'degrading'
            else:
                trend = 'stable'

            return {
                'trend': trend,
                'slope': slope,
                'recent_accuracy': accuracies[-1],
                'accuracy_variance': np.var(accuracies)
            }

        except Exception as e:
            logger.error(f"Erreur analyse tendance: {e}")
            return {'trend': 'error', 'slope': 0.0}

    def _parallel_gradient_optimization(self) -> Dict[str, float]:
        """Optimisation gradient parallélisée"""
        try:
            current_performance = self._calculate_current_performance()
            gradients = {}

            # Calcul gradient pour chaque paramètre en parallèle
            def calculate_parameter_gradient(param_name: str) -> Tuple[str, float]:
                current_value = self.calibrated_parameters[param_name]
                min_val, max_val = self.parameter_ranges[param_name]

                # Perturbation pour calcul gradient
                delta = (max_val - min_val) * 0.01  # 1% de la plage

                # Test valeur augmentée
                test_params = self.calibrated_parameters.copy()
                test_params[param_name] = min(current_value + delta, max_val)
                performance_plus = self._simulate_performance_with_parameters(test_params)

                # Test valeur diminuée
                test_params[param_name] = max(current_value - delta, min_val)
                performance_minus = self._simulate_performance_with_parameters(test_params)

                # Calcul gradient
                gradient = (performance_plus - performance_minus) / (2 * delta)

                return param_name, gradient

            # Calcul parallèle gradients
            with ThreadPoolExecutor(max_workers=len(self.calibrated_parameters)) as executor:
                futures = [executor.submit(calculate_parameter_gradient, param)
                          for param in self.calibrated_parameters.keys()]

                for future in futures:
                    param_name, gradient = future.result()
                    gradients[param_name] = gradient

            return gradients

        except Exception as e:
            logger.error(f"Erreur optimisation gradient: {e}")
            return {}

    def _analyze_parameter_interactions(self) -> Dict[str, Any]:
        """Analyse interactions entre paramètres (haute capacité mémoire)"""
        try:
            # Matrice d'interactions (exploite 28GB RAM)
            param_names = list(self.calibrated_parameters.keys())
            interaction_matrix = np.zeros((len(param_names), len(param_names)))

            # Calcul interactions par paires
            for i, param1 in enumerate(param_names):
                for j, param2 in enumerate(param_names):
                    if i != j:
                        interaction_score = self._calculate_parameter_interaction(param1, param2)
                        interaction_matrix[i, j] = interaction_score

            # Identification interactions significatives
            significant_interactions = []
            threshold = 0.1

            for i, param1 in enumerate(param_names):
                for j, param2 in enumerate(param_names):
                    if i < j and abs(interaction_matrix[i, j]) > threshold:
                        significant_interactions.append({
                            'param1': param1,
                            'param2': param2,
                            'interaction_strength': interaction_matrix[i, j]
                        })

            return {
                'interaction_matrix': interaction_matrix,
                'significant_interactions': significant_interactions,
                'max_interaction': np.max(np.abs(interaction_matrix))
            }

        except Exception as e:
            logger.error(f"Erreur analyse interactions: {e}")
            return {'interaction_matrix': np.zeros((5, 5)), 'significant_interactions': []}

    def _calculate_parameter_interaction(self, param1: str, param2: str) -> float:
        """Calcule interaction entre deux paramètres"""
        try:
            base_performance = self._calculate_current_performance()

            # Test modification simultanée
            test_params = self.calibrated_parameters.copy()

            # Perturbations
            range1 = self.parameter_ranges[param1]
            range2 = self.parameter_ranges[param2]

            delta1 = (range1[1] - range1[0]) * 0.05
            delta2 = (range2[1] - range2[0]) * 0.05

            # Test interaction positive
            test_params[param1] = np.clip(test_params[param1] + delta1, range1[0], range1[1])
            test_params[param2] = np.clip(test_params[param2] + delta2, range2[0], range2[1])

            interaction_performance = self._simulate_performance_with_parameters(test_params)

            # Score interaction
            interaction_score = interaction_performance - base_performance

            return interaction_score

        except Exception as e:
            logger.error(f"Erreur calcul interaction {param1}-{param2}: {e}")
            return 0.0
