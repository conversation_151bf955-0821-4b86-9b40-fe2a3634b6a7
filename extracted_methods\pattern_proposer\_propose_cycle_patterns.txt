# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 277 à 326
# Type: Méthode de la classe BaccaratPatternProposer

    def _propose_cycle_patterns(self, results: List[int]) -> List[Dict]:
        """
        Propose patterns cycliques - LONGUEURS AUTO-OPTIMISÉES
        Utilise les longueurs de cycles découvertes comme optimales
        """
        patterns = []

        if len(results) < 8:
            return patterns

        # ═══════════════════════════════════════════════════════════════════
        # UTILISATION LONGUEURS CYCLES OPTIMALES AUTO-DÉCOUVERTES
        # ═══════════════════════════════════════════════════════════════════

        # Test longueurs cycles optimales découvertes + exploration
        cycle_lengths_to_test = self.optimal_cycle_lengths.copy()

        # Ajout exploration nouvelles longueurs cycles (15% du temps)
        if self.pattern_attempts_count % 7 == 0:
            exploration_cycle_length = self._get_exploration_cycle_length()
            if exploration_cycle_length not in cycle_lengths_to_test:
                cycle_lengths_to_test.append(exploration_cycle_length)

        # Recherche cycles avec longueurs optimisées
        for cycle_length in cycle_lengths_to_test:
            if cycle_length <= len(results) // 2:
                cycle_confidence = self._detect_cycle_pattern(results, cycle_length)

                if cycle_confidence > self.confidence_threshold:
                    # Prédiction basée sur position dans le cycle
                    position_in_cycle = len(results) % cycle_length
                    cycle_pattern = results[-cycle_length:]
                    predicted_outcome = cycle_pattern[position_in_cycle] if position_in_cycle < len(cycle_pattern) else None

                    pattern = {
                        'type': 'cycle',
                        'pattern': f'cycle_{cycle_length}',
                        'cycle_length': cycle_length,
                        'cycle_pattern': cycle_pattern,
                        'position_in_cycle': position_in_cycle,
                        'predicted_outcome': predicted_outcome,
                        'confidence': cycle_confidence,
                        'optimal_length': cycle_length in self.optimal_cycle_lengths
                    }
                    patterns.append(pattern)

                    # Enregistrement tentative pour optimisation
                    self._record_cycle_attempt(cycle_length)

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:2]