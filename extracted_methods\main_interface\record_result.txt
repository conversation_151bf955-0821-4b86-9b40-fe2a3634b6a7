# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 220 à 264
# Type: Méthode de la classe BaccaratPredictorApp

    def record_result(self, outcome):
        """Enregistre le résultat réel d'une manche et génère automatiquement la prédiction suivante"""
        try:
            if not self.predictions:
                messagebox.showwarning("Attention", "Veuillez d'abord faire une prédiction")
                return

            if len(self.results) >= len(self.predictions):
                messagebox.showwarning("Attention", "Résultat déjà enregistré pour cette manche")
                return

            # Enregistrement résultat
            self.results.append(outcome)

            # Calcul précision
            if len(self.results) == len(self.predictions):
                last_prediction = self.predictions[-1]
                correct = (last_prediction['predicted_outcome'] == outcome)

                # Mise à jour affichage
                outcome_text = "👤 PLAYER" if outcome == 0 else "🏦 BANKER"
                result_text = "✅ CORRECT" if correct else "❌ INCORRECT"

                self.info_text.insert(tk.END, f"Manche {len(self.results)}: {outcome_text} - {result_text}\n")
                self.info_text.see(tk.END)

                # 🎯 APPRENTISSAGE AZR OPTIMISÉ AVEC RÉCOMPENSES ADAPTÉES
                self.azr_system.learn_from_result(
                    last_prediction['predicted_outcome'],
                    outcome,
                    last_prediction['confidence']
                )

                # 🎯 AFFICHAGE MÉTRIQUES RÉCOMPENSES OPTIMISÉES
                self._display_optimized_reward_metrics()

                # Log
                logger.info(f"Résultat manche {len(self.results)}: {outcome_text} - {result_text}")

                # 🚀 GÉNÉRATION AUTOMATIQUE PRÉDICTION SUIVANTE
                self._auto_generate_next_prediction()

        except Exception as e:
            logger.error(f"Erreur enregistrement résultat: {e}")
            messagebox.showerror("Erreur", f"Erreur enregistrement résultat: {e}")