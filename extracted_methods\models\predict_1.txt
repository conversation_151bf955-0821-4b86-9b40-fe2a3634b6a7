# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 480 à 522
# Type: Méthode de la classe LGBMModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def predict(self, sequence):
        """Prédiction LGBM avec confiance basée sur feature importance"""
        try:
            if len(sequence) < 2:
                return 0.5, 0.3

            # 📊 EXTRACTION FEATURES LGBM
            X = self._extract_features(sequence)

            # 🌳 SIMULATION LGBM : Gradient Boosting sur features
            features = X[0]

            # Arbre 1: Fréquence Player/Banker
            tree1_pred = features[0]  # player_freq

            # Arbre 2: Correction streak
            if features[1] > 0.4:  # Streak longue
                tree2_pred = 1.0 - tree1_pred  # Anti-streak
            else:
                tree2_pred = tree1_pred

            # Arbre 3: Correction alternance
            if features[2] > 0.6:  # Forte alternance
                tree3_pred = 1.0 - features[0]  # Favorise alternance
            else:
                tree3_pred = tree2_pred

            # 🎯 PRÉDICTION LGBM FINALE (Gradient Boosting)
            lgbm_prediction = np.clip(
                tree1_pred * 0.4 + tree2_pred * 0.3 + tree3_pred * 0.3,
                0.1, 0.9
            )

            # 📈 CONFIANCE BASÉE SUR COHÉRENCE FEATURES
            feature_consistency = 1.0 - np.std(features[:3])  # Cohérence 3 premières features
            lgbm_confidence = max(0.2, min(0.8, feature_consistency))

            self.prediction_history.append(lgbm_prediction)
            return float(lgbm_prediction), float(lgbm_confidence)

        except Exception as e:
            logger.error(f"Erreur LGBM predict: {e}")
            return 0.5, 0.3