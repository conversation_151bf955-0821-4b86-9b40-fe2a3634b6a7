"""
PATTERN VALIDATOR - AZR CORE
=============================

Valide les patterns proposés sur données réelles en temps réel.
Basé sur le paradigme Absolute Zero Reasoner.
"""

import numpy as np
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import time
from collections import defaultdict

# Import configuration centralisée (maintenant dans le même dossier)
from parameters import global_config

# Configuration logging
logger = logging.getLogger(__name__)

class BaccaratPatternValidator:
    """
    Validateur de patterns pour Baccarat - Paradigme AZR

    Valide les patterns proposés sur nouvelles données en temps réel.
    Fournit récompenses basées sur performance réelle sans entraînement.
    """

    def __init__(self):
        """Initialisation du validateur de patterns"""
        self.validation_history = []
        self.pattern_performance = defaultdict(list)
        self.real_time_results = []

        # Paramètres validation
        self.min_validation_samples = global_config.azr.min_validation_samples
        self.validation_window = global_config.azr.validation_window
        self.reward_decay = global_config.azr.reward_decay

        # Métriques temps réel
        self.current_session_accuracy = 0.0
        self.total_validations = 0
        self.successful_validations = 0

        logger.info("BaccaratPatternValidator initialisé avec paradigme AZR")

    def validate_pattern(self, pattern: Dict, test_data: List[int],
                        actual_outcome: Optional[int] = None) -> Dict[str, Any]:
        """
        Valide un pattern sur nouvelles données réelles

        Args:
            pattern: Pattern à valider
            test_data: Données de test (historique récent)
            actual_outcome: Résultat réel observé (si disponible)

        Returns:
            Dict contenant résultats de validation et récompense
        """
        try:
            validation_start = time.time()

            # Génération prédiction basée sur pattern
            prediction = self._apply_pattern(pattern, test_data)

            # Validation si résultat réel disponible
            if actual_outcome is not None:
                validation_result = self._validate_prediction(
                    prediction, actual_outcome, pattern
                )
            else:
                validation_result = self._prepare_validation(prediction, pattern)

            # Calcul récompense AZR
            reward = self._calculate_azr_reward(validation_result, pattern)

            # Mise à jour historique
            self._update_validation_history(pattern, validation_result, reward)

            validation_time = time.time() - validation_start

            result = {
                'pattern': pattern,
                'prediction': prediction,
                'actual_outcome': actual_outcome,
                'validation_result': validation_result,
                'reward': reward,
                'validation_time': validation_time,
                'confidence_adjusted': self._adjust_confidence(pattern, validation_result),
                'pattern_reliability': self._calculate_pattern_reliability(pattern)
            }

            logger.debug(f"Pattern validé: {pattern['type']} - Récompense: {reward:.3f}")
            return result

        except Exception as e:
            logger.error(f"Erreur validation pattern: {e}")
            return self._create_error_validation(pattern, str(e))

    def _apply_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique un pattern pour générer une prédiction"""
        pattern_type = pattern['type']

        if pattern_type == 'sequence':
            return self._apply_sequence_pattern(pattern, test_data)
        elif pattern_type == 'frequency':
            return self._apply_frequency_pattern(pattern, test_data)
        elif pattern_type == 'trend':
            return self._apply_trend_pattern(pattern, test_data)
        elif pattern_type == 'alternation':
            return self._apply_alternation_pattern(pattern, test_data)
        elif pattern_type == 'streak':
            return self._apply_streak_pattern(pattern, test_data)
        elif pattern_type == 'cycle':
            return self._apply_cycle_pattern(pattern, test_data)
        else:
            return self._apply_default_pattern(pattern, test_data)

    def _apply_sequence_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern de séquence"""
        sequence = pattern['pattern']
        seq_len = len(sequence)

        if len(test_data) < seq_len:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        # Vérification si fin de test_data correspond au pattern
        recent_sequence = test_data[-seq_len:]

        if recent_sequence == sequence:
            # Pattern détecté, prédire continuation ou rupture
            # Logique basée sur fréquence historique du pattern
            next_outcome = self._predict_sequence_continuation(sequence, test_data)
            confidence = pattern.get('confidence', 0.5)

            return {
                'predicted_outcome': next_outcome,
                'confidence': confidence,
                'reason': 'sequence_match',
                'matched_sequence': sequence
            }
        else:
            return {
                'predicted_outcome': None,
                'confidence': 0.0,
                'reason': 'sequence_no_match'
            }

    def _apply_frequency_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern de fréquence"""
        window_size = pattern.get('window_size', 20)
        dominant_outcome = pattern.get('dominant_outcome')

        if len(test_data) < window_size:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        # Analyse fréquence actuelle
        recent_window = test_data[-window_size:]
        current_freq = recent_window.count(dominant_outcome) / len(recent_window)
        expected_freq = pattern.get('frequency_ratio', 0.5)

        # Prédiction basée sur déséquilibre
        if current_freq > expected_freq * 0.8:  # Pattern toujours actif
            predicted_outcome = dominant_outcome
            confidence = pattern.get('confidence', 0.5) * (current_freq / expected_freq)
        else:  # Pattern s'affaiblit
            predicted_outcome = 1 - dominant_outcome
            confidence = pattern.get('confidence', 0.5) * 0.5

        return {
            'predicted_outcome': predicted_outcome,
            'confidence': min(confidence, 1.0),
            'reason': 'frequency_analysis',
            'current_frequency': current_freq
        }

    def _apply_trend_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern de tendance"""
        segment_size = pattern.get('segment_size', 10)
        direction = pattern.get('direction', 0)

        if len(test_data) < segment_size:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        # Vérification continuation de la tendance
        recent_segment = test_data[-segment_size:]
        x = np.arange(len(recent_segment))
        current_slope = np.polyfit(x, recent_segment, 1)[0]

        expected_direction = 1 if pattern.get('slope', 0) > 0 else 0
        current_direction = 1 if current_slope > 0 else 0

        if current_direction == expected_direction:
            # Tendance continue
            predicted_outcome = direction
            confidence = pattern.get('confidence', 0.5)
        else:
            # Tendance s'inverse
            predicted_outcome = 1 - direction
            confidence = pattern.get('confidence', 0.5) * 0.6

        return {
            'predicted_outcome': predicted_outcome,
            'confidence': confidence,
            'reason': 'trend_analysis',
            'current_slope': current_slope
        }

    def _apply_alternation_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern d'alternance"""
        if len(test_data) < 2:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        # Prédiction basée sur alternance
        last_outcome = test_data[-1]
        predicted_outcome = 1 - last_outcome  # Alternance

        # Vérification si alternance récente
        recent_alternation_score = self._calculate_recent_alternation_score(test_data[-6:])
        confidence = pattern.get('confidence', 0.5) * recent_alternation_score

        return {
            'predicted_outcome': predicted_outcome,
            'confidence': confidence,
            'reason': 'alternation_pattern',
            'recent_alternation_score': recent_alternation_score
        }

    def _apply_streak_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern de série"""
        if len(test_data) < 2:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        predicted_outcome = pattern.get('predicted_outcome')
        continuation_prob = pattern.get('continuation_probability', 0.5)

        # Vérification série actuelle
        current_streak = self._get_current_streak_info(test_data)

        if current_streak['length'] >= 2:
            # Ajustement confiance basé sur longueur série
            length_factor = min(current_streak['length'] / 5.0, 1.0)
            confidence = pattern.get('confidence', 0.5) * length_factor
        else:
            confidence = pattern.get('confidence', 0.5) * 0.5

        return {
            'predicted_outcome': predicted_outcome,
            'confidence': confidence,
            'reason': 'streak_analysis',
            'current_streak_length': current_streak['length']
        }

    def _apply_cycle_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern cyclique"""
        cycle_length = pattern.get('cycle_length', 3)
        predicted_outcome = pattern.get('predicted_outcome')

        if len(test_data) < cycle_length:
            return {'predicted_outcome': None, 'confidence': 0.0, 'reason': 'insufficient_data'}

        # Vérification position dans le cycle
        position = len(test_data) % cycle_length
        cycle_pattern = pattern.get('cycle_pattern', [])

        if position < len(cycle_pattern):
            expected_outcome = cycle_pattern[position]
            confidence = pattern.get('confidence', 0.5)

            return {
                'predicted_outcome': expected_outcome,
                'confidence': confidence,
                'reason': 'cycle_position',
                'cycle_position': position
            }

        return {
            'predicted_outcome': predicted_outcome,
            'confidence': pattern.get('confidence', 0.5) * 0.7,
            'reason': 'cycle_extrapolation'
        }

    def _apply_default_pattern(self, pattern: Dict, test_data: List[int]) -> Dict[str, Any]:
        """Applique pattern par défaut"""
        return {
            'predicted_outcome': 0 if len(test_data) % 2 == 0 else 1,
            'confidence': 0.5,
            'reason': 'default_pattern'
        }

    def _validate_prediction(self, prediction: Dict, actual_outcome: int, pattern: Dict) -> Dict[str, Any]:
        """Valide une prédiction contre le résultat réel"""
        predicted_outcome = prediction.get('predicted_outcome')

        if predicted_outcome is None:
            return {
                'success': False,
                'accuracy': 0.0,
                'error_type': 'no_prediction',
                'confidence_penalty': 0.5
            }

        # Validation binaire
        success = (predicted_outcome == actual_outcome)
        accuracy = 1.0 if success else 0.0

        # Calcul pénalité confiance
        predicted_confidence = prediction.get('confidence', 0.5)
        confidence_penalty = self._calculate_confidence_penalty(predicted_confidence, success)

        # Mise à jour statistiques
        self.total_validations += 1
        if success:
            self.successful_validations += 1

        self.current_session_accuracy = self.successful_validations / self.total_validations

        return {
            'success': success,
            'accuracy': accuracy,
            'predicted_confidence': predicted_confidence,
            'confidence_penalty': confidence_penalty,
            'session_accuracy': self.current_session_accuracy,
            'prediction_details': prediction
        }

    def _prepare_validation(self, prediction: Dict, pattern: Dict) -> Dict[str, Any]:
        """Prépare validation pour prédiction en attente"""
        return {
            'success': None,
            'accuracy': None,
            'predicted_confidence': prediction.get('confidence', 0.5),
            'awaiting_outcome': True,
            'prediction_details': prediction
        }

    def _calculate_azr_reward(self, validation_result: Dict, pattern: Dict) -> float:
        """Calcule récompense AZR basée sur validation"""
        if validation_result.get('awaiting_outcome'):
            # Récompense intermédiaire basée sur confiance
            confidence = validation_result.get('predicted_confidence', 0.5)
            return confidence * 0.5  # Récompense partielle

        success = validation_result.get('success', False)
        accuracy = validation_result.get('accuracy', 0.0)
        confidence = validation_result.get('predicted_confidence', 0.5)

        if success:
            # Récompense positive avec bonus confiance
            base_reward = 1.0
            confidence_bonus = confidence * 0.5
            pattern_complexity_bonus = self._calculate_pattern_complexity_bonus(pattern)

            total_reward = base_reward + confidence_bonus + pattern_complexity_bonus
        else:
            # Pénalité avec facteur confiance
            base_penalty = -0.5
            confidence_penalty = validation_result.get('confidence_penalty', 0.0)

            total_reward = base_penalty - confidence_penalty

        # Application decay temporel
        time_factor = self._calculate_time_decay_factor()
        final_reward = total_reward * time_factor

        return np.clip(final_reward, -2.0, 2.0)

    def _calculate_confidence_penalty(self, predicted_confidence: float, success: bool) -> float:
        """Calcule pénalité basée sur confiance et résultat"""
        if success:
            # Pas de pénalité si succès
            return 0.0
        else:
            # Pénalité proportionnelle à la confiance erronée
            return predicted_confidence * 0.5

    def _calculate_pattern_complexity_bonus(self, pattern: Dict) -> float:
        """Calcule bonus basé sur complexité du pattern"""
        pattern_type = pattern['type']

        complexity_scores = {
            'sequence': 0.1,
            'frequency': 0.05,
            'trend': 0.15,
            'alternation': 0.08,
            'streak': 0.12,
            'cycle': 0.2
        }

        return complexity_scores.get(pattern_type, 0.0)

    def _calculate_time_decay_factor(self) -> float:
        """Calcule facteur de decay temporel"""
        # Récompenses récentes plus importantes
        return max(0.5, 1.0 - (len(self.validation_history) * self.reward_decay))

    def _adjust_confidence(self, pattern: Dict, validation_result: Dict) -> float:
        """Ajuste confiance du pattern basée sur validation"""
        original_confidence = pattern.get('confidence', 0.5)

        if validation_result.get('awaiting_outcome'):
            return original_confidence

        success = validation_result.get('success', False)

        if success:
            # Augmentation confiance
            adjustment = 0.1
        else:
            # Diminution confiance
            adjustment = -0.15

        new_confidence = original_confidence + adjustment
        return np.clip(new_confidence, 0.1, 0.95)

    def _calculate_pattern_reliability(self, pattern: Dict) -> float:
        """Calcule fiabilité du pattern basée sur historique"""
        pattern_type = pattern['type']
        pattern_performances = self.pattern_performance[pattern_type]

        if len(pattern_performances) < 3:
            return 0.5  # Fiabilité neutre

        recent_performances = pattern_performances[-10:]  # 10 dernières validations
        success_rate = sum(recent_performances) / len(recent_performances)

        return success_rate

    def _update_validation_history(self, pattern: Dict, validation_result: Dict, reward: float):
        """Met à jour historique de validation"""
        validation_entry = {
            'timestamp': time.time(),
            'pattern_type': pattern['type'],
            'pattern': pattern,
            'validation_result': validation_result,
            'reward': reward
        }

        self.validation_history.append(validation_entry)

        # Limitation taille historique
        max_history = global_config.azr.max_validation_history
        if len(self.validation_history) > max_history:
            self.validation_history = self.validation_history[-max_history:]

        # Mise à jour performance par type
        if not validation_result.get('awaiting_outcome'):
            success = validation_result.get('success', False)
            self.pattern_performance[pattern['type']].append(1.0 if success else 0.0)

    def _create_error_validation(self, pattern: Dict, error_msg: str) -> Dict[str, Any]:
        """Crée validation d'erreur"""
        return {
            'pattern': pattern,
            'prediction': {'predicted_outcome': None, 'confidence': 0.0},
            'actual_outcome': None,
            'validation_result': {'success': False, 'error': error_msg},
            'reward': -0.1,
            'validation_time': 0.0,
            'confidence_adjusted': 0.1,
            'pattern_reliability': 0.0
        }

    def _predict_sequence_continuation(self, sequence: List[int], test_data: List[int]) -> int:
        """Prédit continuation d'une séquence"""
        # Logique simple: alternance ou continuation basée sur pattern
        if len(sequence) >= 2:
            if sequence[-1] == sequence[-2]:
                # Série en cours, prédire rupture
                return 1 - sequence[-1]
            else:
                # Alternance, prédire continuation
                return 1 - sequence[-1]

        return 1 - sequence[-1] if sequence else 0

    def _calculate_recent_alternation_score(self, recent_data: List[int]) -> float:
        """Calcule score d'alternance récent"""
        if len(recent_data) < 2:
            return 0.0

        alternations = 0
        for i in range(1, len(recent_data)):
            if recent_data[i] != recent_data[i-1]:
                alternations += 1

        return alternations / (len(recent_data) - 1)

    def _get_current_streak_info(self, data: List[int]) -> Dict:
        """Obtient informations sur la série actuelle"""
        if not data:
            return {'length': 0, 'outcome': None}

        current_outcome = data[-1]
        length = 1

        for i in range(len(data) - 2, -1, -1):
            if data[i] == current_outcome:
                length += 1
            else:
                break

        return {'length': length, 'outcome': current_outcome}

    def get_validation_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques de validation"""
        return {
            'total_validations': self.total_validations,
            'successful_validations': self.successful_validations,
            'current_session_accuracy': self.current_session_accuracy,
            'pattern_performance': dict(self.pattern_performance),
            'recent_rewards': [v['reward'] for v in self.validation_history[-10:]],
            'validation_history_length': len(self.validation_history)
        }
