# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 687 à 704
# Type: Méthode de la classe UncertaintyCalculator

    def calculate_markov_uncertainty_entropy(self, model, X: np.ndarray) -> np.ndarray:
        """Calcule incertitude Markov via entropie des transitions"""
        try:
            if not hasattr(model, 'is_trained') or not model.is_trained:
                return np.full(len(X), 1.0)

            uncertainties = []

            for sequence in X:
                sequence_flat = sequence.flatten() if sequence.ndim > 1 else sequence
                uncertainty = self._calculate_markov_sequence_uncertainty(model, sequence_flat)
                uncertainties.append(uncertainty)

            return np.array(uncertainties)

        except Exception as e:
            logger.error(f"Erreur incertitude Markov entropie: {e}")
            return np.full(len(X), 1.0)