# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\parameters.py
# Lignes: 226 à 246
# Type: Méthode de la classe GlobalConfig

    def _validate_config(self):
        """Valide la cohérence de la configuration"""
        try:
            # Validation AZR
            assert 0 < self.azr.learning_rate < 1, "Learning rate doit être entre 0 et 1"
            assert 0 < self.azr.confidence_threshold < 1, "Confidence threshold doit être entre 0 et 1"
            assert self.azr.total_rounds == 60, "Total rounds doit être 60"
            assert self.azr.prediction_start_round == 31, "Prédictions doivent commencer à la manche 31"

            # Validation système
            assert self.azr.max_ram_usage_gb > 0, "RAM usage doit être positif"

            # Validation prédiction
            assert self.prediction.player_outcome == 0, "Player outcome doit être 0"
            assert self.prediction.banker_outcome == 1, "Banker outcome doit être 1"

            return True

        except AssertionError as e:
            print(f"Erreur validation configuration: {e}")
            return False