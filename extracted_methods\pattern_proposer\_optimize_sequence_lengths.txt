# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 665 à 702
# Type: Méthode de la classe BaccaratPatternProposer

    def _optimize_sequence_lengths(self):
        """Optimise les longueurs de séquences basées sur performance"""
        try:
            # Optimisation séquences
            self._optimize_lengths_by_type('sequence', self.sequence_length_performance,
                                         self.optimal_sequence_lengths, self.sequence_length_range)

            # Optimisation cycles
            self._optimize_lengths_by_type('cycle', self.cycle_length_performance,
                                         self.optimal_cycle_lengths, self.cycle_length_range)

            # Optimisation séries
            self._optimize_lengths_by_type('streak', self.streak_length_performance,
                                         self.optimal_streak_lengths, self.streak_length_range)

            # Enregistrement historique
            optimization_entry = {
                'timestamp': time.time(),
                'optimal_sequences': self.optimal_sequence_lengths.copy(),
                'optimal_cycles': self.optimal_cycle_lengths.copy(),
                'optimal_streaks': self.optimal_streak_lengths.copy(),
                'performance_data': {
                    'sequences': dict(self.sequence_length_performance),
                    'cycles': dict(self.cycle_length_performance),
                    'streaks': dict(self.streak_length_performance)
                }
            }
            self.length_optimization_history.append(optimization_entry)

            # Limitation historique
            if len(self.length_optimization_history) > 50:
                self.length_optimization_history = self.length_optimization_history[-50:]

            logger.info(f"Optimisation longueurs: Séquences={self.optimal_sequence_lengths}, "
                       f"Cycles={self.optimal_cycle_lengths}, Séries={self.optimal_streak_lengths}")

        except Exception as e:
            logger.error(f"Erreur optimisation longueurs: {e}")