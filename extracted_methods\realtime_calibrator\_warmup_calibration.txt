# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 326 à 369
# Type: Méthode de la classe RealtimeCalibrator

    def _warmup_calibration(self) -> Dict[str, Any]:
        """
        Calibration phase échauffement (manches 1-30)
        Exploration intensive pour trouver meilleurs paramètres
        """
        try:
            old_parameters = self.calibrated_parameters.copy()

            # ═══════════════════════════════════════════════════════════════════
            # EXPLORATION INTENSIVE HAUTE PERFORMANCE (28GB RAM)
            # ═══════════════════════════════════════════════════════════════════

            # Génération combinaisons paramètres (parallélisé)
            parameter_combinations = self._generate_parameter_combinations_parallel(
                exploration_factor=global_config.azr.warmup_exploration_rate,
                num_combinations=500  # Haute capacité mémoire
            )

            # Évaluation parallèle toutes combinaisons
            evaluation_results = self._evaluate_parameter_combinations_parallel(
                parameter_combinations
            )

            # Sélection meilleure combinaison
            best_combination = max(evaluation_results, key=lambda x: x['performance_score'])

            # Mise à jour paramètres
            self.calibrated_parameters.update(best_combination['parameters'])

            improvement = best_combination['performance_score'] - self._calculate_current_performance()

            return {
                'success': True,
                'phase': 'warmup',
                'old_parameters': old_parameters,
                'new_parameters': self.calibrated_parameters.copy(),
                'improvement': improvement,
                'combinations_tested': len(parameter_combinations),
                'best_score': best_combination['performance_score']
            }

        except Exception as e:
            logger.error(f"Erreur calibration warmup: {e}")
            return {'success': False, 'error': str(e)}