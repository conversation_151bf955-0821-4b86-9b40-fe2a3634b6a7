# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 804 à 813
# Type: Méthode

def normalize_probabilities(probabilities: Dict[str, float]) -> Dict[str, float]:
    """Normalise probabilités pour qu'elles somment à 1"""
    try:
        total = sum(probabilities.values())
        if total == global_config.calculations.min_total_weight:
            return {k: global_config.calculations.default_probability/len(probabilities) for k in probabilities.keys()}
        return {k: v/total for k, v in probabilities.items()}
    except Exception as e:
        logger.error(f"Erreur normalisation: {e}")
        return probabilities