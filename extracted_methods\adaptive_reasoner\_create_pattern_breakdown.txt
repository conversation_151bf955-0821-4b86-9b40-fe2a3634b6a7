# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 429 à 475
# Type: Méthode de la classe AdaptiveReasoner

    def _create_pattern_breakdown(self, validated_patterns: List[Dict]) -> Dict[str, Any]:
        """Crée breakdown détaillé des patterns"""
        breakdown = {
            'total_patterns': len(validated_patterns),
            'by_type': defaultdict(int),
            'top_patterns': [],
            'average_confidence': 0.0,
            'consensus_level': 0.0
        }

        if not validated_patterns:
            return breakdown

        # Comptage par type
        for pattern in validated_patterns:
            breakdown['by_type'][pattern['type']] += 1

        # Top 3 patterns
        breakdown['top_patterns'] = [
            {
                'type': p['type'],
                'confidence': p['adjusted_confidence'],
                'reliability': p.get('reliability', 0.5)
            }
            for p in validated_patterns[:3]
        ]

        # Statistiques
        confidences = [p['adjusted_confidence'] for p in validated_patterns]
        breakdown['average_confidence'] = np.mean(confidences)

        # Niveau de consensus
        predictions = []
        for pattern in validated_patterns:
            validation = pattern.get('validation_result', {})
            prediction_details = validation.get('prediction_details', {})
            predicted_outcome = prediction_details.get('predicted_outcome')
            if predicted_outcome is not None:
                predictions.append(predicted_outcome)

        if predictions:
            player_votes = predictions.count(0)
            banker_votes = predictions.count(1)
            total_votes = len(predictions)
            breakdown['consensus_level'] = abs(player_votes - banker_votes) / total_votes

        return breakdown