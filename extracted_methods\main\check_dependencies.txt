# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main.py
# Lignes: 40 à 66
# Type: Méthode

def check_dependencies():
    """Vérifie les dépendances requises"""
    try:
        required_modules = [
            'numpy', 'tkinter', 'matplotlib', 'psutil'
        ]

        missing_modules = []

        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)

        if missing_modules:
            logger.error(f"Modules manquants: {missing_modules}")
            print(f"❌ Modules Python manquants: {', '.join(missing_modules)}")
            print("Installez-les avec: pip install " + " ".join(missing_modules))
            return False

        logger.info("Toutes les dépendances sont disponibles")
        return True

    except Exception as e:
        logger.error(f"Erreur vérification dépendances: {e}")
        return False