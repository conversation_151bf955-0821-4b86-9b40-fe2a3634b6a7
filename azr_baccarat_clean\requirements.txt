# DÉPENDANCES PYTHON - AZR BACCARAT PROGRAMME
# ==========================================

# Calcul scientifique et numérique
numpy>=1.21.0

# Monitoring système et ressources
psutil>=5.8.0

# Interface graphique (inclus avec Python standard)
# tkinter - Inclus avec Python

# Graphiques et visualisation
matplotlib>=3.5.0

# Traitement parallèle (inclus avec Python standard)
# concurrent.futures - Inclus avec Python
# multiprocessing - Inclus avec Python
# threading - Inclus avec Python

# Utilitaires système (inclus avec Python standard)
# pathlib - Inclus avec Python
# logging - Inclus avec Python
# time - Inclus avec Python
# sys - Inclus avec Python
# os - Inclus avec Python
# typing - Inclus avec Python
# collections - Inclus avec Python
# dataclasses - Inclus avec Python
# gc - Inclus avec Python

# NOTES D'INSTALLATION:
# =====================
# 
# Installation automatique:
# pip install -r requirements.txt
#
# Installation manuelle:
# pip install numpy psutil matplotlib
#
# Vérification versions:
# pip list
#
# COMPATIBILITÉ:
# ==============
# Python 3.8+ requis
# Windows 10/11 recommandé
# RAM: 8GB minimum, 16GB+ recommandé
# CPU: 4 cœurs minimum, 8+ recommandé
