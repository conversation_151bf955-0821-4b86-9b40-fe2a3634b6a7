# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 346 à 357
# Type: Méthode de la classe UnlimitedThresholdManager

    def _calculate_recent_accuracy(self, window_size: int) -> float:
        """Calcule accuracy récente"""
        try:
            if len(self.performance_history) < window_size:
                return 0.5
            
            recent_results = list(self.performance_history)[-window_size:]
            return sum(1 for r in recent_results if r['correct']) / len(recent_results)
            
        except Exception as e:
            logger.error(f"Erreur calcul accuracy récente: {e}")
            return 0.5