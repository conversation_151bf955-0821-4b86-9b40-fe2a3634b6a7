# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 217 à 229
# Type: Méthode de la classe AZRBaccaratPredictor

    def get_session_status(self) -> Dict[str, Any]:
        """Obtient statut de la session courante"""
        base_status = {
            'predictor_active': self.is_active,
            'current_session': self.current_session,
            'performance_metrics': self.performance_metrics.copy()
        }

        if self.is_active:
            session_status = self.self_play_engine.get_session_status()
            base_status.update(session_status)

        return base_status