"""
TEST D'INTÉGRATION - SYSTÈME WAIT STRATÉGIQUE AZR
=================================================

Script de test pour vérifier l'intégration complète du système WAIT stratégique
dans le modèle AZR existant.
"""

import sys
import logging
import numpy as np
from typing import List, Dict, Any

# Configuration logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_azr_integration():
    """Test d'intégration du système AZR avec WAIT stratégique"""
    
    logger.info("🚀 Début test d'intégration AZR + WAIT stratégique")
    
    try:
        # ═══════════════════════════════════════════════════════════════════
        # 1. IMPORT ET INITIALISATION
        # ═══════════════════════════════════════════════════════════════════
        
        logger.info("📦 Import des modules...")
        
        # Import du système AZR intégré
        from azr_core import create_azr_predictor, AZRCore
        
        # Import des conditions WAIT
        from conditions_wait_strategiques_azr import StrategicWAITDecisionEngine
        
        logger.info("✅ Imports réussis")
        
        # ═══════════════════════════════════════════════════════════════════
        # 2. CRÉATION INSTANCE AZR AVEC WAIT
        # ═══════════════════════════════════════════════════════════════════
        
        logger.info("🔧 Création instance AZRCore avec WAIT stratégique...")
        
        azr_predictor = create_azr_predictor(use_strategic_wait=True)
        
        logger.info("✅ AZRCore créé avec succès")
        
        # ═══════════════════════════════════════════════════════════════════
        # 3. VÉRIFICATION CONFIGURATION
        # ═══════════════════════════════════════════════════════════════════
        
        logger.info("🔍 Vérification configuration système...")
        
        # Statut système
        system_status = azr_predictor.get_system_status()
        
        logger.info(f"📊 Statut système AZR:")
        logger.info(f"   - Training iterations: {system_status.get('training_iterations', 0)}")
        logger.info(f"   - Ensemble disponible: {azr_predictor.azr_system.use_ensemble}")
        logger.info(f"   - WAIT stratégique: {azr_predictor.azr_system.use_strategic_wait}")
        
        # Statistiques WAIT
        wait_stats = azr_predictor.get_strategic_wait_statistics()
        logger.info(f"🎯 WAIT stratégique actif: {wait_stats.get('strategic_wait_active', False)}")
        
        # ═══════════════════════════════════════════════════════════════════
        # 4. TEST PRÉDICTIONS AVEC DIFFÉRENTS SCÉNARIOS
        # ═══════════════════════════════════════════════════════════════════
        
        logger.info("🎮 Test prédictions avec différents scénarios...")
        
        test_scenarios = {
            'Équilibré': [0, 1, 0, 1, 0, 1, 0, 1, 0, 1] * 3,
            'Streak Player': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] * 3,
            'Streak Banker': [1, 1, 1, 1, 1, 1, 1, 1, 1, 1] * 3,
            'Volatilité élevée': [0, 1, 1, 0, 0, 1, 0, 1, 1, 0] * 3,
            'Historique court': [0, 1, 0, 1, 0]
        }
        
        results_summary = {}
        
        for scenario_name, game_history in test_scenarios.items():
            logger.info(f"\n--- Test scénario: {scenario_name} ---")
            
            try:
                # Prédiction avec WAIT stratégique
                prediction = azr_predictor.predict_sequence(game_history, round_number=35)
                
                # Analyse résultat
                decision_type = prediction.get('decision_type', 'UNKNOWN')
                recommendation = prediction.get('recommendation', 'N/A')
                confidence = prediction.get('confidence', 0.0)
                
                logger.info(f"   Décision: {decision_type}")
                logger.info(f"   Recommandation: {recommendation}")
                logger.info(f"   Confiance: {confidence:.3f}")
                
                if decision_type == 'WAIT':
                    wait_reasoning = prediction.get('wait_reasoning', 'N/A')
                    triggered_conditions = prediction.get('triggered_conditions', [])
                    logger.info(f"   Raison WAIT: {wait_reasoning}")
                    logger.info(f"   Conditions déclenchées: {triggered_conditions}")
                
                # Sauvegarde résultats
                results_summary[scenario_name] = {
                    'decision_type': decision_type,
                    'recommendation': recommendation,
                    'confidence': confidence,
                    'success': True
                }
                
            except Exception as e:
                logger.error(f"   ❌ Erreur scénario {scenario_name}: {e}")
                results_summary[scenario_name] = {
                    'success': False,
                    'error': str(e)
                }
        
        # ═══════════════════════════════════════════════════════════════════
        # 5. TEST VALIDATION PRÉDICTIONS
        # ═══════════════════════════════════════════════════════════════════
        
        logger.info("\n🔬 Test validation prédictions...")
        
        # Simulation session courte
        game_history = [0, 1, 0, 0, 1, 1, 0, 1, 0, 1]
        predictions_made = 0
        wait_recommendations = 0
        
        for round_num in range(31, 41):  # 10 rounds de test
            # Prédiction
            prediction = azr_predictor.predict_sequence(game_history, round_number=round_num)
            
            if prediction.get('decision_type') == 'WAIT':
                wait_recommendations += 1
                logger.info(f"Round {round_num}: WAIT - {prediction.get('wait_reasoning', 'N/A')}")
            else:
                predictions_made += 1
                predicted = prediction.get('predicted_outcome')
                confidence = prediction.get('confidence', 0.0)
                logger.info(f"Round {round_num}: {'Player' if predicted == 0 else 'Banker'} (conf: {confidence:.3f})")
            
            # Simulation résultat réel
            actual_outcome = np.random.randint(0, 2)
            
            # Validation
            azr_predictor.validate_prediction(actual_outcome)
            
            # Mise à jour historique
            game_history.append(actual_outcome)
        
        # ═══════════════════════════════════════════════════════════════════
        # 6. STATISTIQUES FINALES
        # ═══════════════════════════════════════════════════════════════════
        
        logger.info("\n📈 STATISTIQUES FINALES:")
        
        final_wait_stats = azr_predictor.get_strategic_wait_statistics()
        
        if final_wait_stats.get('strategic_wait_active'):
            perf_metrics = final_wait_stats.get('performance_metrics', {})
            logger.info(f"   Total prédictions: {perf_metrics.get('total_predictions', 0)}")
            logger.info(f"   Taux WAIT: {perf_metrics.get('wait_rate', 0.0):.3f}")
            logger.info(f"   Taux couverture: {perf_metrics.get('coverage_rate', 0.0):.3f}")
            logger.info(f"   Précision sur prédictions: {perf_metrics.get('precision_on_predictions', 0.0):.3f}")
            logger.info(f"   Amélioration précision: {perf_metrics.get('accuracy_improvement', 0.0):.3f}")
        
        # ═══════════════════════════════════════════════════════════════════
        # 7. RÉSUMÉ TESTS
        # ═══════════════════════════════════════════════════════════════════
        
        logger.info("\n🎯 RÉSUMÉ TESTS:")
        
        successful_scenarios = sum(1 for r in results_summary.values() if r.get('success', False))
        total_scenarios = len(results_summary)
        
        logger.info(f"   Scénarios testés: {total_scenarios}")
        logger.info(f"   Scénarios réussis: {successful_scenarios}")
        logger.info(f"   Taux de réussite: {successful_scenarios/total_scenarios:.1%}")
        
        # Détails par scénario
        for scenario, result in results_summary.items():
            if result.get('success'):
                decision = result.get('decision_type', 'N/A')
                logger.info(f"   ✅ {scenario}: {decision}")
            else:
                logger.info(f"   ❌ {scenario}: {result.get('error', 'Erreur inconnue')}")
        
        # ═══════════════════════════════════════════════════════════════════
        # 8. CONCLUSION
        # ═══════════════════════════════════════════════════════════════════
        
        if successful_scenarios == total_scenarios:
            logger.info("\n🎉 INTÉGRATION RÉUSSIE!")
            logger.info("   Le système WAIT stratégique est parfaitement intégré au modèle AZR.")
            logger.info("   Toutes les fonctionnalités sont opérationnelles.")
            return True
        else:
            logger.warning("\n⚠️ INTÉGRATION PARTIELLE")
            logger.warning(f"   {total_scenarios - successful_scenarios} scénarios ont échoué.")
            logger.warning("   Vérifiez les erreurs ci-dessus.")
            return False
            
    except ImportError as e:
        logger.error(f"❌ Erreur d'import: {e}")
        logger.error("   Vérifiez que tous les fichiers sont présents:")
        logger.error("   - azr_core.py")
        logger.error("   - conditions_wait_strategiques_azr.py")
        logger.error("   - models.py (optionnel pour ensemble)")
        return False
        
    except Exception as e:
        logger.error(f"❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wait_conditions_standalone():
    """Test standalone des conditions WAIT"""
    
    logger.info("\n🔧 Test standalone conditions WAIT...")
    
    try:
        from conditions_wait_strategiques_azr import StrategicWAITDecisionEngine
        
        # Création moteur WAIT
        wait_engine = StrategicWAITDecisionEngine()
        
        # Test conditions
        mock_azr_pred = {
            'confidence': 0.3,  # Confiance faible
            'player_probability': 0.6,
            'uncertainty': 0.7,
            'pattern_breakdown': {},
            'reasoning_details': {'pattern_consensus': 0.2}
        }
        
        mock_ensemble_pred = {
            'confidence': 0.4,
            'player_probability': 0.4,  # Désaccord avec AZR
            'uncertainty': 0.6
        }
        
        mock_context = {
            'current_streak': 6,  # Streak long
            'volatility': 0.9,    # Volatilité élevée
            'alternation_rate': 0.1,  # Faible alternance
            'round_number': 25    # Hors fenêtre optimale
        }
        
        # Test décision WAIT
        should_wait, details = wait_engine.should_wait(
            mock_azr_pred, mock_ensemble_pred, mock_context
        )
        
        logger.info(f"   Décision WAIT: {should_wait}")
        logger.info(f"   Score WAIT: {details.get('wait_score', 0.0):.3f}")
        logger.info(f"   Conditions déclenchées: {details.get('triggered_conditions', [])}")
        
        logger.info("✅ Test conditions WAIT réussi")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur test conditions WAIT: {e}")
        return False

def main():
    """Fonction principale de test"""
    
    logger.info("🚀 DÉBUT TESTS D'INTÉGRATION SYSTÈME WAIT STRATÉGIQUE AZR")
    logger.info("=" * 70)
    
    # Test 1: Conditions WAIT standalone
    test1_success = test_wait_conditions_standalone()
    
    # Test 2: Intégration complète AZR
    test2_success = test_azr_integration()
    
    # Résultat final
    logger.info("\n" + "=" * 70)
    logger.info("🏁 RÉSULTATS FINAUX:")
    logger.info(f"   Test conditions WAIT: {'✅ RÉUSSI' if test1_success else '❌ ÉCHEC'}")
    logger.info(f"   Test intégration AZR: {'✅ RÉUSSI' if test2_success else '❌ ÉCHEC'}")
    
    if test1_success and test2_success:
        logger.info("\n🎉 TOUS LES TESTS RÉUSSIS!")
        logger.info("   Le système WAIT stratégique est parfaitement intégré.")
        logger.info("   Vous pouvez maintenant utiliser AZRCore avec confiance.")
        return 0
    else:
        logger.error("\n❌ CERTAINS TESTS ONT ÉCHOUÉ")
        logger.error("   Vérifiez les erreurs ci-dessus avant utilisation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
