# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\game_initialization_calibrator.py
# Lignes: 240 à 286
# Type: Méthode de la classe GameInitializationCalibrator

    def _calibrate_parameters_for_60_rounds(self) -> Dict[str, Any]:
        """Calibre paramètres spécifiquement pour partie 60 manches"""
        try:
            logger.info("🎯 Calibration paramètres pour 60 manches")
            
            # Paramètres optimaux pour partie 60 manches
            self.optimal_parameters = {
                # Paramètres généraux optimisés
                'learning_rate': 0.15,           # Équilib<PERSON> pour 60 manches
                'confidence_threshold': 0.35,    # Adaptatif selon phase
                'exploration_rate': 0.4,         # Modéré pour partie courte
                'adaptation_rate': 0.12,         # Réactif
                'pattern_decay_rate': 0.04,      # Adapté durée partie
                
                # Paramètres spécifiques phases
                'warmup_exploration': 0.8,       # Exploration intensive début
                'optimal_precision': 0.9,        # Précision maximale 31-60
                'memory_retention': 0.85,        # Rétention patterns récents
                
                # Calibration temps réel
                'realtime_calibration_frequency': 2,  # Tous les 2 rounds (31-60)
                'warmup_calibration_frequency': 3,    # Tous les 3 rounds (1-30)
                
                # Seuils adaptatifs
                'adaptive_threshold_learning_rate': 0.1,
                'threshold_optimization_frequency': 1,
                
                # Performance 60 manches
                'max_pattern_length': 8,         # Adapté pour 60 manches
                'min_pattern_confidence': 0.3,   # Seuil modéré
                'pattern_validation_window': 15   # Fenêtre validation
            }
            
            # Validation paramètres
            validation_score = self._validate_parameter_set(self.optimal_parameters)
            
            return {
                'success': True,
                'optimal_parameters': self.optimal_parameters.copy(),
                'validation_score': validation_score,
                'optimized_for_60_rounds': True,
                'phase_specific_config': self.phase_specific_parameters
            }
            
        except Exception as e:
            logger.error(f"Erreur calibration paramètres: {e}")
            return {'success': False, 'error': str(e)}