# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 508 à 517
# Type: Méthode de la classe BaccaratPatternValidator

    def get_validation_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques de validation"""
        return {
            'total_validations': self.total_validations,
            'successful_validations': self.successful_validations,
            'current_session_accuracy': self.current_session_accuracy,
            'pattern_performance': dict(self.pattern_performance),
            'recent_rewards': [v['reward'] for v in self.validation_history[-10:]],
            'validation_history_length': len(self.validation_history)
        }