# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 670 à 700
# Type: Méthode de la classe AdaptiveReasoner

    def _create_insufficient_history_result(self, game_history: List[int],
                                          current_round: int) -> Dict[str, Any]:
        """Crée résultat pour historique insuffisant"""
        required = global_config.azr.min_history_for_prediction
        current = len(game_history)

        return {
            'prediction': {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,
                'prediction_strength': 0.0
            },
            'confidence': 0.0,
            'uncertainty': 1.0,
            'recommendation': f"Attendre - Historique insuffisant ({current}/{required})",
            'reasoning_details': {
                'mode': 'insufficient_history',
                'current_round': current_round,
                'history_required': required,
                'history_available': current,
                'message': 'Historique insuffisant pour prédiction fiable'
            },
            'pattern_breakdown': {'total_patterns': 0, 'insufficient_history': True},
            'meta_info': {
                'round': current_round,
                'history_length': current,
                'in_optimal_window': False,
                'mode': 'insufficient_history'
            }
        }