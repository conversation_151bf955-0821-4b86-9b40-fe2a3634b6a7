# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 559 à 568
# Type: Méthode de la classe AdaptiveReasoner

    def _cleanup_old_patterns(self):
        """Nettoie patterns anciens"""
        max_successful = global_config.azr.max_successful_patterns
        max_failed = global_config.azr.max_failed_patterns

        if len(self.successful_patterns) > max_successful:
            self.successful_patterns = self.successful_patterns[-max_successful:]

        if len(self.failed_patterns) > max_failed:
            self.failed_patterns = self.failed_patterns[-max_failed:]