# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 324 à 329
# Type: Méthode de la classe LSTMModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, sequence_length=10, hidden_size=32):
        self.sequence_length = sequence_length
        self.hidden_size = hidden_size
        self.model = None
        self.is_trained = False
        self.prediction_history = []