# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 241 à 270
# Type: Méthode de la classe ConfidenceCalculator

    def _calculate_markov_sequence_confidence(self, model, sequence: np.ndarray) -> float:
        """Calcule confiance pour une séquence Markov unique"""
        try:
            with model.lock:
                max_confidence = 0.0

                # Essayer différents ordres pour trouver le plus confiant
                for order in range(min(model.max_order, len(sequence)), 0, -1):
                    if len(sequence) >= order:
                        state = tuple(sequence[-order:])

                        if state in model.models[order]:
                            total_transitions = sum(model.models[order][state].values())

                            # Confiance basée sur nombre d'observations
                            confidence = min(1.0, total_transitions / 10.0)  # 10 transitions = confiance max

                            # Bonus pour ordres plus élevés (plus spécifiques)
                            order_bonus = 1.0 + (order - 1) * 0.1
                            confidence *= order_bonus

                            max_confidence = max(max_confidence, confidence)

                return np.clip(max_confidence,
                              global_config.calculations.confidence_min,
                              global_config.calculations.confidence_max)

        except Exception as e:
            logger.error(f"Erreur confiance séquence Markov: {e}")
            return global_config.calculations.default_confidence