# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 704 à 731
# Type: Méthode de la classe BaccaratPatternProposer

    def _optimize_lengths_by_type(self, pattern_type: str, performance_data: Dict,
                                 optimal_lengths: List[int], length_range: Tuple[int, int]):
        """Optimise longueurs pour un type de pattern spécifique"""
        # Filtrage longueurs avec assez de données
        valid_lengths = []
        for length, perf in performance_data.items():
            if perf['attempts'] >= self.min_attempts_for_optimization:
                valid_lengths.append((length, perf['accuracy']))

        if len(valid_lengths) < 2:
            return  # Pas assez de données

        # Tri par accuracy décroissante
        valid_lengths.sort(key=lambda x: x[1], reverse=True)

        # Sélection top longueurs (max 5)
        top_lengths = [length for length, accuracy in valid_lengths[:5] if accuracy > 0.4]

        # Mise à jour longueurs optimales si amélioration significative
        if top_lengths:
            current_avg_accuracy = self._calculate_average_accuracy(optimal_lengths, performance_data)
            new_avg_accuracy = self._calculate_average_accuracy(top_lengths, performance_data)

            if new_avg_accuracy > current_avg_accuracy + 0.05:  # Amélioration 5%+
                optimal_lengths.clear()
                optimal_lengths.extend(top_lengths[:3])  # Max 3 longueurs optimales
                logger.info(f"Nouvelles longueurs optimales {pattern_type}: {optimal_lengths} "
                           f"(accuracy: {current_avg_accuracy:.3f} → {new_avg_accuracy:.3f})")