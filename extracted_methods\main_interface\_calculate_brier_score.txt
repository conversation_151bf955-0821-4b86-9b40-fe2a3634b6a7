# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 878 à 909
# Type: Méthode de la classe BaccaratPredictorApp

    def _calculate_brier_score(self):
        """Calcule Brier Score réel pour calibration confiance"""
        try:
            if len(self.predictions) < 3 or len(self.results) < 3:
                return 0.25  # Score neutre

            # Aligne prédictions et résultats
            min_len = min(len(self.predictions), len(self.results))
            recent_predictions = self.predictions[-min_len:]
            recent_results = self.results[-min_len:]

            brier_sum = 0.0
            for pred, result in zip(recent_predictions, recent_results):
                confidence = pred.get('confidence', 0.5)
                predicted_outcome = pred.get('predicted_outcome', 0)

                # Probabilité prédite pour résultat réel
                if result == predicted_outcome:
                    prob_predicted = confidence
                else:
                    prob_predicted = 1.0 - confidence

                # Brier Score: (probabilité - résultat)²
                brier_sum += (prob_predicted - 1.0) ** 2

            brier_score = brier_sum / min_len
            self._last_brier_score = brier_score
            return brier_score

        except Exception as e:
            logger.error(f"Erreur calcul Brier Score: {e}")
            return 0.25