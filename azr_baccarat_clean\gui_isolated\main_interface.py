#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PRÉDICTEUR BACCARAT AZR AUTHENTIQUE
===================================

Système AZR (Absolute Zero Reasoner) authentique basé sur recherches approfondies.
Implémente le vrai dual role architecture avec learnability/solution rewards.

Auteur: Assistant IA
Version: 2.0 - AZR Authentique
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import numpy as np
import logging
import json
import os
from datetime import datetime
import random
from azr_core import AZRSystem

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('baccarat_azr.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BaccaratPredictorApp:
    """Application principale prédicteur Baccarat avec système AZR authentique"""

    def __init__(self):
        """Initialise l'application avec système AZR authentique"""

        # Interface graphique
        self.root = tk.Tk()
        self.root.title("🎯 Prédicteur Baccarat AZR Authentique")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a2e')

        # Données de jeu
        self.results = []           # Résultats réels (0=Player, 1=Banker)
        self.predictions = []       # Prédictions générées
        self.current_round = 0      # Manche actuelle

        # 🚀 SYSTÈME AZR 100% OPÉRATIONNEL - BASÉ SUR RECHERCHES APPROFONDIES
        self.azr_system = AZRSystem()

        # MÉTRIQUES APPRENTISSAGE RÉEL
        self.learning_metrics = {
            'accuracy_history': [],           # Historique précision prédictions
            'confidence_calibration': [],    # Historique calibration confiance
            'task_difficulty_evolution': [], # Évolution difficulté tâches
            'capability_improvement': [],    # Amélioration capacités
            'learnability_scores': [],       # Scores learnability
            'solution_quality': []           # Qualité solutions
        }

        # PARAMÈTRES MODÈLE ÉVOLUTIFS
        self.model_parameters = {
            'confidence_adjustment_factor': 0.0,  # Facteur ajustement confiance
            'uncertainty_scaling': 1.0,          # Échelle incertitude
            'decision_threshold': 0.5,           # Seuil décision
            'prediction_bias': 0.0,              # Biais prédiction
            'calibration_offset': 0.0            # Offset calibration
        }

        # Interface utilisateur
        self.setup_ui()

        # Système AZR déjà initialisé dans AZRSystem()

        logger.info("🚀 Prédicteur Baccarat AZR authentique initialisé")

    def setup_ui(self):
        """Configure l'interface utilisateur"""

        # Style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#1a1a2e', foreground='white')
        style.configure('Info.TLabel', font=('Arial', 12), background='#1a1a2e', foreground='#00ff88')
        style.configure('Prediction.TLabel', font=('Arial', 14, 'bold'), background='#1a1a2e', foreground='#ffaa00')

        # Titre principal
        title_frame = tk.Frame(self.root, bg='#1a1a2e')
        title_frame.pack(pady=20)

        title_label = ttk.Label(title_frame, text="🎯 PRÉDICTEUR BACCARAT AZR AUTHENTIQUE", style='Title.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame, text="Système AZR avec Dual Role Architecture & TRR++", style='Info.TLabel')
        subtitle_label.pack(pady=5)

        # Frame principal
        main_frame = tk.Frame(self.root, bg='#1a1a2e')
        main_frame.pack(expand=True, fill='both', padx=20, pady=10)

        # Zone prédiction
        pred_frame = tk.LabelFrame(main_frame, text="🔮 PRÉDICTION AZR", bg='#2d2d44', fg='white', font=('Arial', 12, 'bold'))
        pred_frame.pack(fill='x', pady=10)

        self.prediction_label = ttk.Label(pred_frame, text="Cliquez sur 'COMMENCER' pour débuter", style='Prediction.TLabel')
        self.prediction_label.pack(pady=20)

        # Boutons de contrôle
        control_frame = tk.Frame(main_frame, bg='#1a1a2e')
        control_frame.pack(fill='x', pady=10)

        # Bouton prédire (seulement pour la première manche)
        self.predict_btn = tk.Button(control_frame, text="🎯 COMMENCER", command=self.predict_next,
                                   bg='#00aa44', fg='white', font=('Arial', 12, 'bold'), width=15)
        self.predict_btn.pack(side='left', padx=5)

        # Boutons résultats (initialement désactivés)
        self.player_btn = tk.Button(control_frame, text="👤 PLAYER", command=lambda: self.record_result(0),
                                  bg='#0066cc', fg='white', font=('Arial', 12, 'bold'), width=15, state='disabled')
        self.player_btn.pack(side='left', padx=5)

        self.banker_btn = tk.Button(control_frame, text="🏦 BANKER", command=lambda: self.record_result(1),
                                  bg='#cc6600', fg='white', font=('Arial', 12, 'bold'), width=15, state='disabled')
        self.banker_btn.pack(side='left', padx=5)

        # Bouton reset
        self.reset_btn = tk.Button(control_frame, text="🔄 RESET", command=self.reset_game,
                                 bg='#cc0044', fg='white', font=('Arial', 12, 'bold'), width=15)
        self.reset_btn.pack(side='right', padx=5)

        # Indicateur statut
        self.status_label = tk.Label(control_frame, text="Cliquez sur 'COMMENCER' pour débuter",
                                   bg='#1a1a2e', fg='#ffaa00', font=('Arial', 10))
        self.status_label.pack(side='right', padx=20)

        # Zone informations
        info_frame = tk.LabelFrame(main_frame, text="📊 INFORMATIONS AZR", bg='#2d2d44', fg='white', font=('Arial', 12, 'bold'))
        info_frame.pack(fill='both', expand=True, pady=10)

        self.info_text = tk.Text(info_frame, bg='#1a1a2e', fg='#00ff88', font=('Courier', 10), wrap='word')
        self.info_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Labels informatifs
        self.method_label = ttk.Label(info_frame, text="Méthode: AZR Authentique", style='Info.TLabel')
        self.method_label.pack(anchor='w', padx=10)

        self.azr_info_label = ttk.Label(info_frame, text="AZR: Système initialisé", style='Info.TLabel')
        self.azr_info_label.pack(anchor='w', padx=10)

    def run(self):
        """Lance l'application"""
        self.root.mainloop()

    # ==================== MÉTHODES PRINCIPALES ====================

    def predict_next(self):
        """Génère prédiction pour la prochaine manche via AZR authentique"""
        try:
            self.current_round += 1

            # Génération prédiction AZR authentique
            prediction = self._generate_azr_prediction()

            if prediction:
                # Affichage prédiction
                outcome_text = "👤 PLAYER" if prediction['predicted_outcome'] == 0 else "🏦 BANKER"
                confidence = prediction['confidence']
                method = prediction.get('method', 'azr_authentic')

                pred_text = f"Manche {self.current_round}: {outcome_text}\n"
                pred_text += f"Confiance: {confidence:.1%}\n"
                pred_text += f"Méthode: {method}"

                self.prediction_label.config(text=pred_text)

                # Stockage prédiction
                self.predictions.append(prediction)

                # Affichage détails
                self._show_prediction_details(prediction)

                # Affichage statut AZR
                self._show_azr_status()

                # Activation interface après première prédiction
                self._activate_game_interface()

                # Log
                logger.info(f"Prédiction manche {self.current_round}: {outcome_text} (confiance: {confidence:.1%})")

        except Exception as e:
            logger.error(f"Erreur génération prédiction: {e}")
            messagebox.showerror("Erreur", f"Erreur génération prédiction: {e}")

    def _activate_game_interface(self):
        """Active l'interface de jeu après la première prédiction"""
        try:
            # Désactiver bouton commencer
            self.predict_btn.config(state='disabled', text="🎯 EN COURS")

            # Activer boutons résultats
            self.player_btn.config(state='normal')
            self.banker_btn.config(state='normal')

            # Mise à jour statut
            self.status_label.config(text="Sélectionnez le résultat réel de la manche")

            # Message dans zone info
            self.info_text.insert(tk.END, "🎮 Interface activée - Sélectionnez le résultat de la manche\n")
            self.info_text.see(tk.END)

        except Exception as e:
            logger.error(f"Erreur activation interface: {e}")

    def record_result(self, outcome):
        """Enregistre le résultat réel d'une manche et génère automatiquement la prédiction suivante"""
        try:
            if not self.predictions:
                messagebox.showwarning("Attention", "Veuillez d'abord faire une prédiction")
                return

            if len(self.results) >= len(self.predictions):
                messagebox.showwarning("Attention", "Résultat déjà enregistré pour cette manche")
                return

            # Enregistrement résultat
            self.results.append(outcome)

            # Calcul précision
            if len(self.results) == len(self.predictions):
                last_prediction = self.predictions[-1]
                correct = (last_prediction['predicted_outcome'] == outcome)

                # Mise à jour affichage
                outcome_text = "👤 PLAYER" if outcome == 0 else "🏦 BANKER"
                result_text = "✅ CORRECT" if correct else "❌ INCORRECT"

                self.info_text.insert(tk.END, f"Manche {len(self.results)}: {outcome_text} - {result_text}\n")
                self.info_text.see(tk.END)

                # 🎯 APPRENTISSAGE AZR OPTIMISÉ AVEC RÉCOMPENSES ADAPTÉES
                self.azr_system.learn_from_result(
                    last_prediction['predicted_outcome'],
                    outcome,
                    last_prediction['confidence']
                )

                # 🎯 AFFICHAGE MÉTRIQUES RÉCOMPENSES OPTIMISÉES
                self._display_optimized_reward_metrics()

                # Log
                logger.info(f"Résultat manche {len(self.results)}: {outcome_text} - {result_text}")

                # 🚀 GÉNÉRATION AUTOMATIQUE PRÉDICTION SUIVANTE
                self._auto_generate_next_prediction()

        except Exception as e:
            logger.error(f"Erreur enregistrement résultat: {e}")
            messagebox.showerror("Erreur", f"Erreur enregistrement résultat: {e}")

    def _auto_generate_next_prediction(self):
        """Génère automatiquement la prédiction pour la manche suivante"""
        try:
            # Indication visuelle de génération en cours
            self.status_label.config(text="🤖 Génération prédiction automatique...")
            self.prediction_label.config(text="🔄 Analyse AZR en cours...\nVeuillez patienter")

            # Désactiver temporairement boutons résultats
            self.player_btn.config(state='disabled')
            self.banker_btn.config(state='disabled')

            # Attendre un court instant pour que l'utilisateur voie le résultat
            self.root.after(800, self._execute_auto_prediction)

        except Exception as e:
            logger.error(f"Erreur génération auto prédiction: {e}")

    def _execute_auto_prediction(self):
        """Exécute la génération automatique de prédiction"""
        try:
            self.current_round += 1

            # Génération prédiction AZR authentique
            prediction = self._generate_azr_prediction()

            if prediction:
                # Affichage prédiction
                outcome_text = "👤 PLAYER" if prediction['predicted_outcome'] == 0 else "🏦 BANKER"
                confidence = prediction['confidence']
                method = prediction.get('method', 'azr_authentic')

                pred_text = f"Manche {self.current_round}: {outcome_text}\n"
                pred_text += f"Confiance: {confidence:.1%}\n"
                pred_text += f"Méthode: {method}"

                self.prediction_label.config(text=pred_text)

                # Stockage prédiction
                self.predictions.append(prediction)

                # Affichage détails
                self._show_prediction_details(prediction)

                # Affichage statut AZR
                self._show_azr_status()

                # Log
                logger.info(f"Prédiction auto manche {self.current_round}: {outcome_text} (confiance: {confidence:.1%})")

                # Mise à jour interface pour indiquer prédiction automatique
                self.info_text.insert(tk.END, f"🤖 Prédiction automatique générée pour manche {self.current_round}\n")
                self.info_text.see(tk.END)

                # Réactiver boutons résultats
                self.player_btn.config(state='normal')
                self.banker_btn.config(state='normal')

                # Mise à jour statut
                self.status_label.config(text="Sélectionnez le résultat réel de la manche")

        except Exception as e:
            logger.error(f"Erreur exécution auto prédiction: {e}")
            # Réactiver boutons en cas d'erreur
            self.player_btn.config(state='normal')
            self.banker_btn.config(state='normal')
            self.status_label.config(text="Erreur - Sélectionnez le résultat")

    def reset_game(self):
        """Remet à zéro le jeu"""
        try:
            self.results = []
            self.predictions = []
            self.current_round = 0

            # Reset paramètres modèle
            self.model_parameters = {
                'confidence_adjustment_factor': 0.0,
                'uncertainty_scaling': 1.0,
                'decision_threshold': 0.5,
                'prediction_bias': 0.0,
                'calibration_offset': 0.0
            }

            # 🔄 RESET COMPLET DU SYSTÈME AZR
            try:
                self.azr_system.reset_system()
                logger.info("✅ Système AZR réinitialisé avec succès")
            except Exception as azr_error:
                logger.warning(f"Erreur reset AZR (non critique): {azr_error}")
                # Réinitialisation manuelle des composants critiques
                if hasattr(self.azr_system, 'training_iterations'):
                    self.azr_system.training_iterations = 0
                if hasattr(self.azr_system, 'shared_parameters'):
                    self.azr_system.shared_parameters = {
                        'confidence_adjustment': 0.0,
                        'prediction_bias': 0.0,
                        'decision_threshold': 0.5,
                        'pattern_weights': {
                            'frequency': 0.4,
                            'streaks': 0.3,
                            'alternation': 0.3
                        }
                    }
                if hasattr(self.azr_system, 'training_history'):
                    for key in self.azr_system.training_history:
                        self.azr_system.training_history[key].clear()

            # Reset interface
            self.prediction_label.config(text="Cliquez sur 'COMMENCER' pour débuter")
            self.info_text.delete(1.0, tk.END)
            self.method_label.config(text="Méthode: AZR Authentique")
            self.azr_info_label.config(text="AZR: Système réinitialisé")

            # Reset boutons
            self.predict_btn.config(state='normal', text="🎯 COMMENCER")
            self.player_btn.config(state='disabled')
            self.banker_btn.config(state='disabled')
            self.status_label.config(text="Cliquez sur 'COMMENCER' pour débuter")

            # Message reset
            self.info_text.insert(tk.END, "🔄 Système réinitialisé - Prêt pour une nouvelle session\n")
            self.info_text.insert(tk.END, "✅ AZR: Historique d'apprentissage effacé\n")
            self.info_text.insert(tk.END, "✅ AZR: Paramètres partagés réinitialisés\n")
            self.info_text.see(tk.END)

            logger.info("🔄 Jeu réinitialisé")

        except Exception as e:
            logger.error(f"Erreur reset: {e}")
            messagebox.showerror("Erreur", f"Erreur reset: {e}")

    # ==================== SYSTÈME AZR AUTHENTIQUE ====================

    def _generate_azr_prediction(self):
        """Génère prédiction via système AZR 100% opérationnel"""
        try:
            # Contexte pour AZR
            context = {
                'recent_results': self.results.copy(),
                'current_round': self.current_round,
                'total_rounds': len(self.results)
            }

            # Génération prédiction AZR 100% opérationnel
            prediction = self.azr_system.get_baccarat_prediction(context)

            # Ajout informations AZR dans prédiction
            if 'azr_info' in prediction:
                azr_info = prediction['azr_info']
                self.azr_info_label.config(
                    text=f"AZR: Itérations={azr_info['training_iterations']}, "
                         f"Biais={azr_info['learned_bias']:.3f}, "
                         f"Seuil={azr_info['learned_threshold']:.3f}"
                )

            return prediction

        except Exception as e:
            logger.error(f"Erreur génération prédiction AZR: {e}")
            return self._generate_basic_baccarat_prediction()

    def _show_azr_status(self):
        """Affiche statut détaillé du système AZR avec métriques optimisées"""
        try:
            status = self.azr_system.get_system_status()

            status_text = f"📊 Statut AZR Optimisé:\n"
            status_text += f"   • Itérations: {status['training_iterations']}\n"
            status_text += f"   • Biais appris: {status['shared_parameters']['prediction_bias']:.3f}\n"
            status_text += f"   • Seuil décision: {status['shared_parameters']['decision_threshold']:.3f}\n"
            status_text += f"   • Ajust. confiance: {status['shared_parameters']['confidence_adjustment']:.3f}\n"

            recent_perf = status['recent_performance']
            status_text += f"   • Précision récente: {recent_perf.get('recent_accuracy', 0.0):.1%}\n"
            status_text += f"   • Lambda adaptatif: {recent_perf.get('current_adaptive_lambda', 0.7):.3f}\n"
            status_text += f"   • Cible précision: {recent_perf.get('target_accuracy', 0.6):.1%}\n"
            status_text += f"   • Récompenses: L={recent_perf['avg_learnability_reward']:.3f}, "
            status_text += f"S={recent_perf['avg_solution_reward']:.3f}\n"

            # Affichage statut système de récompenses
            reward_status = status.get('reward_system_status', {})
            if reward_status:
                status_text += f"   • Système récompenses: ✅ Optimisé\n"

            self.info_text.insert(tk.END, status_text)
            self.info_text.see(tk.END)

        except Exception as e:
            logger.error(f"Erreur affichage statut AZR: {e}")

    def _display_optimized_reward_metrics(self):
        """Affiche métriques détaillées du système de récompenses optimisé"""
        try:
            status = self.azr_system.get_system_status()
            recent_perf = status.get('recent_performance', {})

            # Calcul métriques récentes
            recent_accuracy = recent_perf.get('recent_accuracy', 0.0)
            current_lambda = recent_perf.get('current_adaptive_lambda', 0.7)
            target_accuracy = recent_perf.get('target_accuracy', 0.6)

            # Affichage métriques optimisées
            metrics_text = f"🎯 Métriques Récompenses Optimisées:\n"
            metrics_text += f"   • Précision actuelle: {recent_accuracy:.1%}\n"
            metrics_text += f"   • Lambda adaptatif: {current_lambda:.3f}\n"
            metrics_text += f"   • Performance vs cible: {recent_accuracy - target_accuracy:+.1%}\n"

            # Indicateur performance
            if recent_accuracy >= target_accuracy:
                performance_indicator = "🟢 EXCELLENT"
            elif recent_accuracy >= target_accuracy - 0.1:
                performance_indicator = "🟡 BON"
            else:
                performance_indicator = "🔴 À AMÉLIORER"

            metrics_text += f"   • Statut: {performance_indicator}\n"

            # Affichage système de récompenses
            reward_status = status.get('reward_system_status', {})
            if reward_status:
                active_systems = []
                if reward_status.get('adaptive_lambda_enabled'):
                    active_systems.append("Lambda Adaptatif")
                if reward_status.get('progressive_rewards_enabled'):
                    active_systems.append("Récompenses Progressives")
                if reward_status.get('improvement_tracking_enabled'):
                    active_systems.append("Suivi Amélioration")
                if reward_status.get('confidence_calibration_enabled'):
                    active_systems.append("Calibration Confiance")

                metrics_text += f"   • Systèmes actifs: {', '.join(active_systems)}\n"

            self.info_text.insert(tk.END, metrics_text)
            self.info_text.see(tk.END)

        except Exception as e:
            logger.error(f"Erreur affichage métriques récompenses: {e}")

    def _generate_basic_baccarat_prediction(self):
        """Génère prédiction de base si AZR échoue"""
        # Génération aléatoire équilibrée
        import random
        predicted_outcome = random.choice([0, 1])  # 50/50 équilibré

        return {
            'predicted_outcome': predicted_outcome,
            'player_probability': 0.5,
            'banker_probability': 0.5,
            'confidence': 0.4,
            'method': 'basic_fallback_balanced'
        }

    # ==================== MÉTHODES ANALYSE INTELLIGENTE (CONSERVÉES) ====================

    def _analyze_baccarat_patterns(self):
        """Analyse patterns historiques Baccarat"""
        try:
            if len(self.results) < 3:
                return {'player_tendency': 0.0}

            # Analyse fréquence globale
            player_count = sum(1 for r in self.results if r == 0)
            total_count = len(self.results)
            player_frequency = player_count / total_count

            # Tendance par rapport à équilibre 50/50
            frequency_bias = (player_frequency - 0.5) * 0.1  # Ajustement modéré

            # Analyse patterns récents (dernières 5 manches)
            recent_results = self.results[-5:]
            recent_player_count = sum(1 for r in recent_results if r == 0)
            recent_frequency = recent_player_count / len(recent_results)
            recent_bias = (recent_frequency - 0.5) * 0.15  # Plus d'influence récente

            # Combinaison pondérée
            player_tendency = frequency_bias * 0.3 + recent_bias * 0.7

            return {
                'player_tendency': max(-0.2, min(0.2, player_tendency)),
                'global_frequency': player_frequency,
                'recent_frequency': recent_frequency
            }

        except Exception as e:
            logger.error(f"Erreur analyse patterns: {e}")
            return {'player_tendency': 0.0}

    def _analyze_current_streaks(self):
        """Analyse streaks actuelles pour anti-streak bias"""
        try:
            if len(self.results) < 2:
                return {'anti_streak_bias': 0.0}

            # Détection streak actuelle
            current_streak = 1
            last_result = self.results[-1]

            for i in range(len(self.results) - 2, -1, -1):
                if self.results[i] == last_result:
                    current_streak += 1
                else:
                    break

            # Anti-streak bias (plus la streak est longue, plus on prédit l'opposé)
            if current_streak >= 3:
                # Streak de 3+ → Forte tendance anti-streak
                anti_bias_strength = min(0.15, current_streak * 0.03)
                anti_streak_bias = anti_bias_strength if last_result == 1 else -anti_bias_strength
            elif current_streak == 2:
                # Streak de 2 → Tendance anti-streak modérée
                anti_bias_strength = 0.05
                anti_streak_bias = anti_bias_strength if last_result == 1 else -anti_bias_strength
            else:
                anti_streak_bias = 0.0

            return {
                'anti_streak_bias': anti_streak_bias,
                'current_streak': current_streak,
                'streak_type': 'Player' if last_result == 0 else 'Banker'
            }

        except Exception as e:
            logger.error(f"Erreur analyse streaks: {e}")
            return {'anti_streak_bias': 0.0}

    def _analyze_alternation_patterns(self):
        """Analyse patterns d'alternance"""
        try:
            if len(self.results) < 4:
                return {'alternation_tendency': 0.0}

            # Compte alternances dans les dernières manches
            alternations = 0
            for i in range(len(self.results) - 1):
                if self.results[i] != self.results[i + 1]:
                    alternations += 1

            # Taux d'alternance
            alternation_rate = alternations / (len(self.results) - 1)

            # Si forte alternance récente, continue la tendance
            if alternation_rate > 0.6:
                # Prédit l'opposé du dernier résultat
                last_result = self.results[-1]
                alternation_tendency = 0.08 if last_result == 1 else -0.08
            elif alternation_rate < 0.3:
                # Faible alternance → Prédit même résultat
                last_result = self.results[-1]
                alternation_tendency = -0.05 if last_result == 1 else 0.05
            else:
                alternation_tendency = 0.0

            return {
                'alternation_tendency': alternation_tendency,
                'alternation_rate': alternation_rate
            }

        except Exception as e:
            logger.error(f"Erreur analyse alternance: {e}")
            return {'alternation_tendency': 0.0}

    def _calculate_signal_consensus(self, pattern_adj, streak_adj, alternation_adj):
        """Calcule consensus entre différents signaux pour boost confiance"""
        try:
            signals = [pattern_adj, streak_adj, alternation_adj]

            # Filtre signaux significatifs (> 0.02 en valeur absolue)
            significant_signals = [s for s in signals if abs(s) > 0.02]

            if len(significant_signals) < 2:
                return 0.0  # Pas assez de signaux

            # Vérifie si signaux pointent dans même direction
            positive_signals = sum(1 for s in significant_signals if s > 0)
            negative_signals = sum(1 for s in significant_signals if s < 0)

            # Consensus fort si tous signaux dans même direction
            if positive_signals == len(significant_signals):
                consensus_strength = len(significant_signals) / 3.0  # Max 1.0
                return consensus_strength
            elif negative_signals == len(significant_signals):
                consensus_strength = len(significant_signals) / 3.0  # Max 1.0
                return consensus_strength
            else:
                # Signaux contradictoires → Faible consensus
                return 0.1

        except Exception as e:
            logger.error(f"Erreur calcul consensus: {e}")
            return 0.0

    # ==================== MÉTHODES UTILITAIRES ====================

    def _show_prediction_details(self, prediction):
        """Affiche détails prédiction intelligente dans interface"""
        try:
            if not prediction:
                return

            # Affichage méthode utilisée
            method = prediction.get('method', 'unknown')
            self.method_label.config(text=f"Méthode: {method}")

            # Affichage analyse intelligente si disponible
            if 'analysis_details' in prediction:
                analysis = prediction['analysis_details']
                pattern_adj = analysis.get('pattern_adjustment', 0.0)
                streak_adj = analysis.get('streak_adjustment', 0.0)
                alternation_adj = analysis.get('alternation_adjustment', 0.0)

                # Affichage détaillé de l'analyse
                analysis_text = f"Patterns: {pattern_adj:+.3f} | Streaks: {streak_adj:+.3f} | Alt: {alternation_adj:+.3f}"
                self.azr_info_label.config(text=f"Analyse: {analysis_text}")

                # Ajout détails dans zone info
                details_text = f"📊 Analyse intelligente:\n"
                details_text += f"   • Ajustement patterns: {pattern_adj:+.3f}\n"
                details_text += f"   • Ajustement streaks: {streak_adj:+.3f}\n"
                details_text += f"   • Ajustement alternance: {alternation_adj:+.3f}\n"

                if 'azr_improvements' in prediction:
                    azr_info = prediction['azr_improvements']
                    azr_bias = azr_info.get('prediction_bias', 0.0)
                    confidence_boost = azr_info.get('confidence_adjustment', 0.0)
                    details_text += f"   • Biais AZR appris: {azr_bias:+.3f}\n"
                    details_text += f"   • Boost confiance AZR: {confidence_boost:+.3f}\n"

                self.info_text.insert(tk.END, details_text)
                self.info_text.see(tk.END)

            elif 'azr_improvements' in prediction:
                # Fallback pour affichage AZR basique
                azr_info = prediction['azr_improvements']
                confidence_adj = azr_info.get('confidence_adjustment', 0.0)
                self.azr_info_label.config(
                    text=f"AZR: Ajust. confiance {confidence_adj:+.3f}"
                )

        except Exception as e:
            logger.error(f"Erreur affichage détails: {e}")

    def _update_accuracy_tracking(self):
        """Met à jour tracking précision pour apprentissage"""
        try:
            if len(self.predictions) > 0 and len(self.results) >= len(self.predictions):
                # Calcul précision récente
                recent_accuracy = self._calculate_recent_accuracy(10)
                self.learning_metrics['accuracy_history'].append(recent_accuracy)

                # Limite historique
                if len(self.learning_metrics['accuracy_history']) > 50:
                    self.learning_metrics['accuracy_history'] = \
                        self.learning_metrics['accuracy_history'][-25:]

        except Exception as e:
            logger.error(f"Erreur tracking précision: {e}")

    def _calculate_recent_accuracy(self, window_size):
        """Calcule précision récente"""
        try:
            if not self.predictions or not self.results:
                return 0.5

            # Données alignées
            min_len = min(len(self.predictions), len(self.results), window_size)
            if min_len == 0:
                return 0.5

            recent_predictions = self.predictions[-min_len:]
            recent_results = self.results[-min_len:]

            # Calcul précision
            correct = sum(1 for pred, result in zip(recent_predictions, recent_results)
                         if pred.get('predicted_outcome') == result)

            return correct / min_len if min_len > 0 else 0.5

        except Exception as e:
            logger.error(f"Erreur calcul précision récente: {e}")
            return 0.5

    # ==================== MÉTHODES STUBS (À IMPLÉMENTER) ====================

    def _generate_confidence_calibration_task(self):
        """Génère tâche RÉELLE de calibration confiance"""
        # Calcul Brier Score actuel pour calibration
        if len(self.predictions) >= 3 and len(self.results) >= 3:
            brier_score = self._calculate_brier_score()
            difficulty = min(0.9, brier_score * 2.0)  # Plus le score est mauvais, plus c'est difficile
        else:
            difficulty = 0.5

        return {
            'type': 'confidence_calibration',
            'description': 'Calibrer confiance prédictions via Brier Score',
            'parameters': {
                'target': 'brier_score_minimization',
                'current_brier_score': getattr(self, '_last_brier_score', 0.25),
                'target_improvement': 0.05
            },
            'difficulty': difficulty,
            'mathematical_complexity': 0.8
        }

    def _generate_uncertainty_quantification_task(self):
        """Génère tâche RÉELLE de quantification incertitude"""
        # Calcul entropie actuelle des prédictions
        if len(self.predictions) >= 3:
            entropy = self._calculate_prediction_entropy()
            difficulty = 0.3 + (entropy * 0.4)  # Plus d'entropie = plus difficile
        else:
            difficulty = 0.6

        return {
            'type': 'uncertainty_quantification',
            'description': 'Quantifier incertitude via entropie et variance',
            'parameters': {
                'metric': 'shannon_entropy',
                'current_entropy': getattr(self, '_last_entropy', 0.5),
                'variance_threshold': 0.1
            },
            'difficulty': difficulty,
            'mathematical_complexity': 0.9
        }

    def _generate_decision_optimization_task(self):
        """Génère tâche RÉELLE d'optimisation décision"""
        # Analyse performance seuil actuel
        current_threshold = self.model_parameters['decision_threshold']
        if len(self.results) >= 5:
            threshold_performance = self._evaluate_threshold_performance(current_threshold)
            difficulty = 1.0 - threshold_performance  # Mauvaise performance = plus difficile
        else:
            difficulty = 0.4

        return {
            'type': 'decision_optimization',
            'description': 'Optimiser seuil décision pour maximiser précision',
            'parameters': {
                'target': 'accuracy_maximization',
                'current_threshold': current_threshold,
                'performance_score': getattr(self, '_last_threshold_perf', 0.5),
                'optimization_range': [0.35, 0.65]
            },
            'difficulty': difficulty,
            'mathematical_complexity': 0.7
        }

    def _test_current_model_on_task(self, task, attempt):
        """Test modèle actuel sur tâche (stub)"""
        # Simulation test avec variabilité
        base_accuracy = 0.5
        task_difficulty = task.get('difficulty', 0.5)

        # Succès probabiliste basé sur difficulté
        success_prob = 1.0 - task_difficulty + (attempt * 0.05)  # Amélioration avec tentatives
        success = random.random() < success_prob

        return {'correct': success, 'accuracy': base_accuracy}

    def _azr_solve_baccarat_task(self, task):
        """Résout tâche Baccarat (stub)"""
        if not task:
            return None

        return {
            'type': f"{task['type']}_solution",
            'quality_score': random.uniform(0.3, 0.8),
            'improvement': random.uniform(0.0, 0.1)
        }

    def _azr_calculate_solution_reward(self, task, solution):
        """Calcule récompense solution (stub)"""
        if not solution:
            return 0.0

        quality_score = solution.get('quality_score', 0.5)
        return 1.0 if quality_score > 0.6 else 0.0

    def _azr_update_model_parameters(self, task, learnability_reward, solution, solution_reward):
        """Met à jour paramètres modèle (stub)"""
        # Mise à jour simple basée sur récompenses
        if solution_reward > 0.5 and solution:
            improvement = solution.get('improvement', 0.0)
            self.model_parameters['confidence_adjustment_factor'] += improvement * 0.1

    def _create_baccarat_task_validator(self):
        """Crée validateur tâches (stub)"""
        def validate(task):
            return task is not None and 'type' in task
        return validate

    def _create_learnability_calculator(self):
        """Crée calculateur learnability (stub)"""
        def calculate(task, model):
            return random.uniform(0.2, 0.8)
        return calculate

    def _create_solution_evaluator(self):
        """Crée évaluateur solutions (stub)"""
        def evaluate(task, solution):
            return random.uniform(0.0, 1.0)
        return evaluate

    def _create_feedback_provider(self):
        """Crée fournisseur feedback (stub)"""
        def provide(task, solution, rewards):
            return {'feedback': 'Task completed'}
        return provide

    # ==================== MÉTHODES MATHÉMATIQUES RÉELLES ====================

    def _calculate_brier_score(self):
        """Calcule Brier Score réel pour calibration confiance"""
        try:
            if len(self.predictions) < 3 or len(self.results) < 3:
                return 0.25  # Score neutre

            # Aligne prédictions et résultats
            min_len = min(len(self.predictions), len(self.results))
            recent_predictions = self.predictions[-min_len:]
            recent_results = self.results[-min_len:]

            brier_sum = 0.0
            for pred, result in zip(recent_predictions, recent_results):
                confidence = pred.get('confidence', 0.5)
                predicted_outcome = pred.get('predicted_outcome', 0)

                # Probabilité prédite pour résultat réel
                if result == predicted_outcome:
                    prob_predicted = confidence
                else:
                    prob_predicted = 1.0 - confidence

                # Brier Score: (probabilité - résultat)²
                brier_sum += (prob_predicted - 1.0) ** 2

            brier_score = brier_sum / min_len
            self._last_brier_score = brier_score
            return brier_score

        except Exception as e:
            logger.error(f"Erreur calcul Brier Score: {e}")
            return 0.25

    def _calculate_prediction_entropy(self):
        """Calcule entropie Shannon des prédictions"""
        try:
            if len(self.predictions) < 3:
                return 0.5

            # Collecte probabilités récentes
            recent_predictions = self.predictions[-10:]  # 10 dernières
            player_probs = [p.get('player_probability', 0.5) for p in recent_predictions]

            # Calcul entropie moyenne
            entropy_sum = 0.0
            for p_player in player_probs:
                p_banker = 1.0 - p_player

                # Évite log(0)
                p_player = max(0.001, min(0.999, p_player))
                p_banker = max(0.001, min(0.999, p_banker))

                # Entropie Shannon: -Σ(p * log2(p))
                entropy = -(p_player * np.log2(p_player) + p_banker * np.log2(p_banker))
                entropy_sum += entropy

            avg_entropy = entropy_sum / len(player_probs)
            self._last_entropy = avg_entropy
            return avg_entropy

        except Exception as e:
            logger.error(f"Erreur calcul entropie: {e}")
            return 0.5

    def _evaluate_threshold_performance(self, threshold):
        """Évalue performance d'un seuil de décision"""
        try:
            if len(self.predictions) < 5 or len(self.results) < 5:
                return 0.5

            # Simule prédictions avec ce seuil
            correct_count = 0
            min_len = min(len(self.predictions), len(self.results))

            for i in range(-min_len, 0):
                pred = self.predictions[i]
                result = self.results[i]

                player_prob = pred.get('player_probability', 0.5)
                simulated_outcome = 0 if player_prob > threshold else 1

                if simulated_outcome == result:
                    correct_count += 1

            performance = correct_count / min_len
            self._last_threshold_perf = performance
            return performance

        except Exception as e:
            logger.error(f"Erreur évaluation seuil: {e}")
            return 0.5


if __name__ == "__main__":
    app = BaccaratPredictorApp()
    app.run()
