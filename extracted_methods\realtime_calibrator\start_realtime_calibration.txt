# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 121 à 141
# Type: Méthode de la classe RealtimeCalibrator

    def start_realtime_calibration(self):
        """Démarre calibration temps réel"""
        try:
            if self.calibration_active:
                return {'success': False, 'message': 'Calibration déjà active'}

            self.calibration_active = True

            # Démarrage thread calibration temps réel
            self.calibration_thread = threading.Thread(
                target=self._realtime_calibration_loop,
                daemon=True
            )
            self.calibration_thread.start()

            logger.info("Calibration temps réel démarrée")
            return {'success': True, 'message': 'Calibration temps réel active'}

        except Exception as e:
            logger.error(f"Erreur démarrage calibration: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}