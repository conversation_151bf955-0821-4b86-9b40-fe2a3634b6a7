# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 268 à 320
# Type: Méthode de la classe AdaptiveReasoner

    def _calculate_adaptive_confidence(self, validated_patterns: List[Dict]) -> Dict[str, float]:
        """Calcule confiance adaptative basée sur patterns validés"""
        if not validated_patterns:
            return {'overall': 0.5, 'pattern_consensus': 0.0, 'reliability_factor': 0.0}

        # Confiances individuelles
        individual_confidences = [p['adjusted_confidence'] for p in validated_patterns]

        # Consensus entre patterns
        predictions = []
        for pattern in validated_patterns:
            validation = pattern.get('validation_result', {})
            prediction_details = validation.get('prediction_details', {})
            predicted_outcome = prediction_details.get('predicted_outcome')
            if predicted_outcome is not None:
                predictions.append(predicted_outcome)

        # Calcul consensus
        if predictions:
            player_votes = predictions.count(0)
            banker_votes = predictions.count(1)
            total_votes = len(predictions)

            consensus_strength = abs(player_votes - banker_votes) / total_votes
            pattern_consensus = consensus_strength
        else:
            pattern_consensus = 0.0

        # Facteur fiabilité basé sur historique
        reliability_scores = [p.get('reliability', 0.5) for p in validated_patterns]
        reliability_factor = np.mean(reliability_scores) if reliability_scores else 0.5

        # Confiance globale avec formule unifiée
        base_confidence = np.mean(individual_confidences)

        # Utilisation formule unifiée pour calcul final (avec fallback)
        if global_confidence_calculator:
            unified_confidence = global_confidence_calculator.calculate_ensemble_confidence(
                model_confidences={'azr_patterns': base_confidence},
                consensus_factor=pattern_consensus,
                performance_history=[reliability_factor]
            )
        else:
            # Fallback: calcul confiance simple
            unified_confidence = base_confidence * (0.5 + 0.5 * pattern_consensus) * reliability_factor

        return {
            'overall': unified_confidence,
            'pattern_consensus': pattern_consensus,
            'reliability_factor': reliability_factor,
            'base_confidence': base_confidence,
            'individual_confidences': individual_confidences
        }