"""
EXEMPLE D'UTILISATION - SYSTÈME WAIT STRATÉGIQUE AZR
===================================================

Exemple complet d'utilisation du système WAIT stratégique
pour améliorer la précision du modèle AZR.
"""

import numpy as np
from typing import List, Dict, Any
import logging
from integration_wait_azr import AZRWithStrategicWAIT
from conditions_wait_strategiques_azr import StrategicWAITDecisionEngine

# Configuration logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExempleWAITStrategique:
    """Exemple d'utilisation du système WAIT stratégique"""
    
    def __init__(self):
        """Initialisation de l'exemple"""
        # Simulation d'un système AZR (remplacer par le vrai système)
        self.azr_system_mock = self._create_azr_mock()
        
        # Système AZR avec WAIT stratégique
        self.azr_with_wait = AZRWithStrategicWAIT(self.azr_system_mock)
        
        # Historique de jeu simulé
        self.game_history = []
        self.round_number = 30
        
    def _create_azr_mock(self):
        """Crée un mock du système AZR pour l'exemple"""
        class AZRMock:
            def predict_sequence(self, history):
                # Simulation prédiction AZR
                player_prob = np.random.uniform(0.3, 0.7)
                banker_prob = 1.0 - player_prob
                confidence = np.random.uniform(0.2, 0.8)
                
                return {
                    'player_probability': player_prob,
                    'banker_probability': banker_prob,
                    'predicted_outcome': 0 if player_prob > banker_prob else 1,
                    'prediction_strength': abs(player_prob - 0.5) * 2,
                    'confidence': confidence,
                    'uncertainty': 1.0 - confidence,
                    'reasoning_details': {
                        'pattern_consensus': np.random.uniform(0.2, 0.8),
                        'patterns_used': np.random.randint(1, 5)
                    },
                    'pattern_breakdown': {'total_patterns': np.random.randint(1, 5)}
                }
        
        return AZRMock()
    
    def simuler_session_complete(self, nb_rounds: int = 30) -> Dict[str, Any]:
        """
        Simule une session complète avec le système WAIT stratégique
        
        Args:
            nb_rounds: Nombre de rounds à simuler
            
        Returns:
            Dict avec statistiques de la session
        """
        logger.info(f"🎮 Début simulation session {nb_rounds} rounds")
        
        # Statistiques session
        session_stats = {
            'total_rounds': nb_rounds,
            'predictions_made': 0,
            'wait_recommendations': 0,
            'correct_predictions': 0,
            'accuracy_with_wait': 0.0,
            'coverage_rate': 0.0,
            'wait_decisions': []
        }
        
        # Simulation des rounds
        for round_num in range(31, 31 + nb_rounds):  # Commence à round 31
            # Génération historique de jeu réaliste
            if len(self.game_history) == 0:
                # Initialisation avec historique aléatoire
                self.game_history = [np.random.randint(0, 2) for _ in range(30)]
            
            # Prédiction avec WAIT stratégique
            prediction_result = self.azr_with_wait.predict_with_strategic_wait(
                self.game_history, round_num
            )
            
            # Analyse de la décision
            is_wait = prediction_result.get('decision_type') == 'WAIT'
            
            if is_wait:
                session_stats['wait_recommendations'] += 1
                logger.info(f"Round {round_num}: WAIT - {prediction_result.get('wait_reasoning', 'N/A')}")
            else:
                session_stats['predictions_made'] += 1
                predicted = prediction_result.get('predicted_outcome')
                confidence = prediction_result.get('confidence', 0.0)
                logger.info(f"Round {round_num}: {'Player' if predicted == 0 else 'Banker'} "
                           f"(conf: {confidence:.3f})")
            
            # Simulation résultat réel
            actual_outcome = self._simulate_realistic_outcome(self.game_history)
            
            # Validation si prédiction faite
            if not is_wait:
                predicted_outcome = prediction_result.get('predicted_outcome')
                is_correct = (predicted_outcome == actual_outcome)
                if is_correct:
                    session_stats['correct_predictions'] += 1
                
                # Mise à jour système
                self.azr_with_wait.validate_prediction(actual_outcome)
            
            # Mise à jour historique
            self.game_history.append(actual_outcome)
            
            # Sauvegarde décision
            session_stats['wait_decisions'].append({
                'round': round_num,
                'is_wait': is_wait,
                'prediction_result': prediction_result,
                'actual_outcome': actual_outcome
            })
        
        # Calcul statistiques finales
        if session_stats['predictions_made'] > 0:
            session_stats['accuracy_with_wait'] = (
                session_stats['correct_predictions'] / session_stats['predictions_made']
            )
        
        session_stats['coverage_rate'] = (
            session_stats['predictions_made'] / nb_rounds
        )
        
        # Statistiques système WAIT
        wait_stats = self.azr_with_wait.get_strategic_wait_statistics()
        session_stats['wait_system_stats'] = wait_stats
        
        logger.info(f"📊 Session terminée - Accuracy: {session_stats['accuracy_with_wait']:.3f}, "
                   f"Coverage: {session_stats['coverage_rate']:.3f}")
        
        return session_stats
    
    def _simulate_realistic_outcome(self, history: List[int]) -> int:
        """Simule un résultat réaliste basé sur l'historique"""
        if len(history) < 5:
            return np.random.randint(0, 2)
        
        # Simulation avec légères tendances
        recent = history[-5:]
        player_freq = recent.count(0) / len(recent)
        
        # Légère tendance vers l'équilibre
        if player_freq > 0.7:
            prob_player = 0.4  # Tendance retour équilibre
        elif player_freq < 0.3:
            prob_player = 0.6  # Tendance retour équilibre
        else:
            prob_player = 0.5  # Équilibre
        
        return 0 if np.random.random() < prob_player else 1
    
    def analyser_conditions_wait(self, history: List[int]) -> Dict[str, Any]:
        """
        Analyse détaillée des conditions WAIT pour un historique donné
        
        Args:
            history: Historique de jeu
            
        Returns:
            Dict avec analyse détaillée
        """
        # Prédiction AZR
        azr_pred = self.azr_system_mock.predict_sequence(history)
        
        # Analyse contexte
        game_context = self.azr_with_wait._analyze_game_context(history, len(history) + 31)
        
        # Évaluation conditions WAIT
        should_wait, wait_details = self.azr_with_wait.wait_engine.should_wait(
            azr_pred, None, game_context
        )
        
        return {
            'should_wait': should_wait,
            'wait_details': wait_details,
            'azr_prediction': azr_pred,
            'game_context': game_context,
            'conditions_analysis': {
                name: {
                    'value': cond.current_value,
                    'threshold': cond.threshold,
                    'triggered': cond.is_triggered,
                    'weight': cond.weight
                }
                for name, cond in self.azr_with_wait.wait_engine.wait_conditions.items()
            }
        }
    
    def demonstrer_scenarios_wait(self):
        """Démontre différents scénarios déclenchant WAIT"""
        logger.info("🔍 Démonstration scénarios WAIT")
        
        scenarios = {
            'Confiance faible': [0, 1, 0, 1, 0, 1, 0, 1, 0, 1] * 3,  # Alternance parfaite
            'Streak long': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] * 3,       # Streak Player
            'Haute volatilité': [0, 1, 1, 0, 0, 1, 0, 1, 1, 0] * 3,  # Volatilité élevée
            'Équilibré': [0, 1, 0, 0, 1, 1, 0, 1, 0, 1] * 3          # Relativement équilibré
        }
        
        for scenario_name, history in scenarios.items():
            logger.info(f"\n--- Scénario: {scenario_name} ---")
            
            analysis = self.analyser_conditions_wait(history)
            
            logger.info(f"Décision: {'WAIT' if analysis['should_wait'] else 'PREDICT'}")
            logger.info(f"Score WAIT: {analysis['wait_details'].get('wait_score', 0):.3f}")
            
            # Conditions déclenchées
            triggered = analysis['wait_details'].get('triggered_conditions', [])
            if triggered:
                logger.info(f"Conditions déclenchées: {', '.join(triggered)}")
            
            # Contexte
            context = analysis['game_context']
            logger.info(f"Streak: {context.get('current_streak', 0)}, "
                       f"Volatilité: {context.get('volatility', 0):.3f}")

def main():
    """Fonction principale d'exemple"""
    logger.info("🚀 Démarrage exemple WAIT stratégique")
    
    # Création instance
    exemple = ExempleWAITStrategique()
    
    # Démonstration scénarios
    exemple.demonstrer_scenarios_wait()
    
    # Simulation session complète
    logger.info("\n" + "="*50)
    session_results = exemple.simuler_session_complete(20)
    
    # Affichage résultats
    logger.info("\n📈 RÉSULTATS SESSION:")
    logger.info(f"Accuracy avec WAIT: {session_results['accuracy_with_wait']:.3f}")
    logger.info(f"Taux de couverture: {session_results['coverage_rate']:.3f}")
    logger.info(f"Prédictions faites: {session_results['predictions_made']}")
    logger.info(f"Recommandations WAIT: {session_results['wait_recommendations']}")
    
    # Statistiques système WAIT
    wait_stats = session_results['wait_system_stats']
    logger.info(f"\n🎯 STATISTIQUES WAIT:")
    logger.info(f"Amélioration précision: {wait_stats['performance_metrics']['accuracy_improvement']:.3f}")
    logger.info(f"Taux WAIT: {wait_stats['performance_metrics']['wait_rate']:.3f}")

if __name__ == "__main__":
    main()
