# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 411 à 417
# Type: Méthode de la classe LGBMModel
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __init__(self, n_estimators=50, max_depth=3):
        self.n_estimators = n_estimators
        self.max_depth = max_depth
        self.model = None
        self.feature_importance = {}
        self.prediction_history = []
        self.is_trained = False