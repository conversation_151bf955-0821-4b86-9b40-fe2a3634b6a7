# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 286 à 324
# Type: Méthode de la classe RealtimeCalibrator

    def _trigger_immediate_calibration(self) -> Dict[str, Any]:
        """Déclenche calibration immédiate haute performance"""
        try:
            calibration_start = time.time()

            if self.current_phase == 'warmup':
                # Calibration échauffement - Exploration intensive
                result = self._warmup_calibration()
            elif self.current_phase == 'optimal':
                # Calibration fenêtre optimale - Précision maximale
                result = self._optimal_window_calibration()
            else:
                # Calibration post-optimale - Stabilisation
                result = self._post_optimal_calibration()

            calibration_time = time.time() - calibration_start
            self.last_calibration_round = self.current_round

            # Enregistrement historique
            calibration_entry = {
                'round': self.current_round,
                'phase': self.current_phase,
                'calibration_time': calibration_time,
                'old_parameters': result.get('old_parameters', {}),
                'new_parameters': result.get('new_parameters', {}),
                'improvement': result.get('improvement', 0.0),
                'timestamp': time.time()
            }
            self.calibration_history.append(calibration_entry)

            logger.info(f"Calibration {self.current_phase} - Manche {self.current_round} - "
                       f"Amélioration: {result.get('improvement', 0.0):.3f} - "
                       f"Temps: {calibration_time:.3f}s")

            return result

        except Exception as e:
            logger.error(f"Erreur calibration immédiate: {e}")
            return {'success': False, 'error': str(e)}