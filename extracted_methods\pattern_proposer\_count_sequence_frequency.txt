# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 364 à 373
# Type: Méthode de la classe BaccaratPatternProposer

    def _count_sequence_frequency(self, sequence: List[int], results: List[int]) -> int:
        """Compte fréquence d'apparition d'une séquence"""
        count = 0
        seq_len = len(sequence)

        for i in range(len(results) - seq_len + 1):
            if results[i:i+seq_len] == sequence:
                count += 1

        return count