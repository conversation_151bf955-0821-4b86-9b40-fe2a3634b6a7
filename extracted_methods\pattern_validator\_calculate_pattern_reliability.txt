# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 419 à 430
# Type: Méthode de la classe BaccaratPatternValidator

    def _calculate_pattern_reliability(self, pattern: Dict) -> float:
        """Calcule fiabilité du pattern basée sur historique"""
        pattern_type = pattern['type']
        pattern_performances = self.pattern_performance[pattern_type]

        if len(pattern_performances) < 3:
            return 0.5  # Fiabilité neutre

        recent_performances = pattern_performances[-10:]  # 10 dernières validations
        success_rate = sum(recent_performances) / len(recent_performances)

        return success_rate