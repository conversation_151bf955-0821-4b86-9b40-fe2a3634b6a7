# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 237 à 310
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def _execute_adaptation(self, reasoning_result: Dict[str, Any],
                          session_accuracy: float) -> Dict[str, Any]:
        """Exécute adaptation de la stratégie"""
        try:
            adaptation_start = time.time()

            # Types d'adaptation possibles
            adaptations_applied = []

            # 1. Ajustement seuils de confiance
            if reasoning_result['confidence'] < 0.4:
                self.adaptive_reasoner.confidence_threshold *= 0.9  # Réduction seuil
                adaptations_applied.append('confidence_threshold_reduced')

            # 2. Augmentation exploration si peu de patterns
            if reasoning_result['reasoning_details']['patterns_used'] < 2:
                self.exploration_rate = min(self.exploration_rate * 1.1, 0.8)
                adaptations_applied.append('exploration_increased')

            # 3. Ajustement taux d'apprentissage basé sur performance
            if session_accuracy < 0.4:
                self.learning_rate = min(self.learning_rate * 1.2, 0.3)
                adaptations_applied.append('learning_rate_increased')
            elif session_accuracy > 0.7:
                self.learning_rate = max(self.learning_rate * 0.9, 0.05)
                adaptations_applied.append('learning_rate_decreased')

            # 4. Reset patterns si performance très faible
            if session_accuracy < 0.3 and self.current_session['predictions_made'] > 10:
                self.adaptive_reasoner.successful_patterns = []
                adaptations_applied.append('patterns_reset')

            # 5. Ajustement decay patterns
            if reasoning_result['uncertainty'] > 0.7:
                self.adaptive_reasoner.pattern_decay_rate = min(
                    self.adaptive_reasoner.pattern_decay_rate * 1.1, 0.2
                )
                adaptations_applied.append('pattern_decay_increased')

            adaptation_time = time.time() - adaptation_start

            # Enregistrement événement d'adaptation
            adaptation_event = {
                'timestamp': time.time(),
                'session_id': self.current_session['session_id'],
                'round': self.current_session['rounds_played'],
                'session_accuracy': session_accuracy,
                'adaptations_applied': adaptations_applied,
                'adaptation_time': adaptation_time,
                'reasoning_context': {
                    'confidence': reasoning_result['confidence'],
                    'uncertainty': reasoning_result['uncertainty'],
                    'patterns_used': reasoning_result['reasoning_details']['patterns_used']
                }
            }

            self.adaptation_events.append(adaptation_event)

            logger.info(f"Adaptation exécutée - {len(adaptations_applied)} changements")

            return {
                'adaptations_applied': adaptations_applied,
                'adaptation_time': adaptation_time,
                'new_parameters': {
                    'confidence_threshold': self.adaptive_reasoner.confidence_threshold,
                    'exploration_rate': self.exploration_rate,
                    'learning_rate': self.learning_rate,
                    'pattern_decay_rate': self.adaptive_reasoner.pattern_decay_rate
                }
            }

        except Exception as e:
            logger.error(f"Erreur adaptation: {e}")
            return {'error': str(e)}