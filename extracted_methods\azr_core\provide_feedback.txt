# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 136 à 168
# Type: Méthode de la classe AZREnvironment

    def provide_feedback(self, task: AZRTask, solution: AZRSolution,
                        quality_score: float) -> Dict[str, Any]:
        """Fournit feedback détaillé pour apprentissage"""
        feedback = {
            'quality_score': quality_score,
            'success': quality_score > 0.6,
            'improvement_suggestions': [],
            'strengths': [],
            'weaknesses': []
        }

        # Analyse des forces et faiblesses
        if solution.confidence > 0.8:
            feedback['strengths'].append('Confiance élevée')
        elif solution.confidence < 0.4:
            feedback['weaknesses'].append('Confiance trop faible')

        if len(solution.reasoning_steps) > 3:
            feedback['strengths'].append('Raisonnement détaillé')
        elif len(solution.reasoning_steps) < 2:
            feedback['weaknesses'].append('Raisonnement insuffisant')
            feedback['improvement_suggestions'].append('Développer le raisonnement')

        # Suggestions d'amélioration
        if quality_score < 0.5:
            feedback['improvement_suggestions'].extend([
                'Analyser plus de patterns historiques',
                'Améliorer calibration confiance',
                'Développer raisonnement étape par étape'
            ])

        self.feedback_history.append(feedback)
        return feedback