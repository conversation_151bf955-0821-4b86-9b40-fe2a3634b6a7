# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main.py
# Lignes: 134 à 157
# Type: Méthode

def launch_gui():
    """Lance l'interface graphique"""
    try:
        logger.info("Lancement interface graphique")

        # Import interface (maintenant dans le même dossier)
        from main_interface import BaccaratPredictorApp

        # Création et lancement interface
        app = BaccaratPredictorApp()

        logger.info("Interface graphique démarrée")

        # Démarrage boucle principale
        app.run()

        return True

    except ImportError as e:
        logger.error(f"Erreur import interface: {e}")
        return False
    except Exception as e:
        logger.error(f"Erreur lancement interface: {e}")
        return False