# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 784 à 883
# Type: Méthode de la classe AZRSystem

    def _update_shared_parameters_trr(self, task: AZRTask, solution: AZRSolution,
                                     learnability_reward: float, solution_reward: float):
        """Mise à jour paramètres avec Task-Relative REINFORCE++"""
        try:
            # Calcul avantages normalisés par type de tâche
            task_type = task.task_type

            # Historique récompenses par type
            if not hasattr(self, '_reward_history_by_type'):
                self._reward_history_by_type = {}

            if task_type not in self._reward_history_by_type:
                self._reward_history_by_type[task_type] = {
                    'learnability': [],
                    'solution': []
                }

            # Ajoute récompenses actuelles
            self._reward_history_by_type[task_type]['learnability'].append(learnability_reward)
            self._reward_history_by_type[task_type]['solution'].append(solution_reward)

            # Limite historique
            max_history = 20
            for reward_type in ['learnability', 'solution']:
                history = self._reward_history_by_type[task_type][reward_type]
                if len(history) > max_history:
                    self._reward_history_by_type[task_type][reward_type] = history[-max_history:]

            # Calcul baselines par type de tâche
            learnability_history = self._reward_history_by_type[task_type]['learnability']
            solution_history = self._reward_history_by_type[task_type]['solution']

            learnability_baseline = np.mean(learnability_history) if learnability_history else 0.0
            solution_baseline = np.mean(solution_history) if solution_history else 0.0

            # Avantages normalisés
            learnability_advantage = learnability_reward - learnability_baseline
            solution_advantage = solution_reward - solution_baseline

            # MISE À JOUR PARAMÈTRES BASÉE SUR AVANTAGES

            # 1. Ajustement confiance
            if solution_advantage > 0:
                # Bonne solution → Augmente confiance
                confidence_update = self.learning_rate * solution_advantage * 0.1
                self.shared_parameters['confidence_adjustment'] += confidence_update
            else:
                # Mauvaise solution → Diminue confiance
                confidence_update = self.learning_rate * solution_advantage * 0.05
                self.shared_parameters['confidence_adjustment'] += confidence_update

            # Limites confiance
            self.shared_parameters['confidence_adjustment'] = max(-0.3, min(0.3,
                self.shared_parameters['confidence_adjustment']))

            # 2. Ajustement biais prédiction
            if solution_advantage > 0 and hasattr(solution, 'predicted_output'):
                # Renforce biais vers prédiction réussie
                if solution.predicted_output == 0:  # Player
                    bias_update = self.learning_rate * solution_advantage * 0.02
                else:  # Banker
                    bias_update = -self.learning_rate * solution_advantage * 0.02

                self.shared_parameters['prediction_bias'] += bias_update

            # Limites biais
            self.shared_parameters['prediction_bias'] = max(-0.1, min(0.1,
                self.shared_parameters['prediction_bias']))

            # 3. Ajustement seuil décision
            if solution_advantage != 0:
                # Ajuste seuil basé sur performance
                threshold_update = self.learning_rate * solution_advantage * 0.005
                self.shared_parameters['decision_threshold'] += threshold_update

                # Limites seuil
                self.shared_parameters['decision_threshold'] = max(0.4, min(0.6,
                    self.shared_parameters['decision_threshold']))

            # 4. Ajustement poids patterns
            if learnability_advantage > 0:
                # Tâche bien conçue → Renforce patterns utilisés
                if task.task_type == 'pattern_prediction':
                    self.shared_parameters['pattern_weights']['frequency'] += 0.01
                elif task.task_type == 'sequence_analysis':
                    self.shared_parameters['pattern_weights']['alternation'] += 0.01
                elif task.task_type == 'trend_detection':
                    self.shared_parameters['pattern_weights']['streaks'] += 0.01

                # Normalise poids
                total_weight = sum(self.shared_parameters['pattern_weights'].values())
                for key in self.shared_parameters['pattern_weights']:
                    self.shared_parameters['pattern_weights'][key] /= total_weight

            logger.debug(f"TRR++ Update - Task: {task_type}, "
                        f"L_adv: {learnability_advantage:.3f}, "
                        f"S_adv: {solution_advantage:.3f}")

        except Exception as e:
            logger.error(f"Erreur mise à jour TRR++: {e}")