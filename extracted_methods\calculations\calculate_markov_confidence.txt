# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 222 à 239
# Type: Méthode de la classe ConfidenceCalculator

    def calculate_markov_confidence(self, model, X: np.ndarray) -> np.ndarray:
        """Calcule confiance Markov basée sur fréquence des transitions"""
        try:
            if not hasattr(model, 'is_trained') or not model.is_trained:
                return np.full(len(X), global_config.calculations.default_confidence)

            confidences = []

            for sequence in X:
                sequence_flat = sequence.flatten() if sequence.ndim > 1 else sequence
                confidence = self._calculate_markov_sequence_confidence(model, sequence_flat)
                confidences.append(confidence)

            return np.array(confidences)

        except Exception as e:
            logger.error(f"Erreur confiance Markov: {e}")
            return np.full(len(X), global_config.calculations.default_confidence)