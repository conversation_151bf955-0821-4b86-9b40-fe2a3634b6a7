# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 39 à 65
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def __init__(self):
        """Initialisation du moteur self-play"""
        self.adaptive_reasoner = BaccaratAdaptiveReasoner()

        # État du moteur
        self.is_running = False
        self.is_paused = False
        self.self_play_thread = None

        # Historique et métriques
        self.game_sessions = []
        self.current_session = None
        self.total_rounds_played = 0
        self.learning_cycles = 0

        # Performance tracking
        self.performance_history = deque(maxlen=1000)
        self.adaptation_events = []
        self.pattern_evolution = defaultdict(list)

        # Paramètres self-play
        self.learning_rate = global_config.azr.learning_rate
        self.exploration_rate = global_config.azr.exploration_rate
        self.adaptation_threshold = global_config.azr.adaptation_threshold
        self.max_session_rounds = global_config.azr.max_session_rounds

        logger.info("BaccaratSelfPlayEngine initialisé avec paradigme AZR")