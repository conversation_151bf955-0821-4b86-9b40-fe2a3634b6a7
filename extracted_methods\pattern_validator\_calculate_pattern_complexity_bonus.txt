# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 380 à 393
# Type: Méthode de la classe BaccaratPatternValidator

    def _calculate_pattern_complexity_bonus(self, pattern: Dict) -> float:
        """Calcule bonus basé sur complexité du pattern"""
        pattern_type = pattern['type']

        complexity_scores = {
            'sequence': 0.1,
            'frequency': 0.05,
            'trend': 0.15,
            'alternation': 0.08,
            'streak': 0.12,
            'cycle': 0.2
        }

        return complexity_scores.get(pattern_type, 0.0)