# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\test_imports_flat.py
# Lignes: 103 à 129
# Type: Méthode

def test_basic_functionality():
    """Test fonctionnalité de base"""
    try:
        print("\n🔬 Test fonctionnalité de base...")

        # Test création objets principaux
        from parameters import global_config
        from pattern_proposer import BaccaratPatternProposer
        from adaptive_reasoner import BaccaratAdaptiveReasoner

        # Test création instances
        proposer = BaccaratPatternProposer()
        reasoner = BaccaratAdaptiveReasoner()

        print("✅ Création objets AZR OK")

        # Test configuration
        print(f"📊 Configuration: {global_config.azr.total_rounds} manches")
        print(f"🎯 Seuil confiance: {global_config.azr.confidence_threshold}")

        print("✅ Fonctionnalité de base OK")
        return True

    except Exception as e:
        print(f"❌ Erreur fonctionnalité: {e}")
        traceback.print_exc()
        return False