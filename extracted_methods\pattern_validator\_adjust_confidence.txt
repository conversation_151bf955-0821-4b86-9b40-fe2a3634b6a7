# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 400 à 417
# Type: Méthode de la classe BaccaratPatternValidator

    def _adjust_confidence(self, pattern: Dict, validation_result: Dict) -> float:
        """Ajuste confiance du pattern basée sur validation"""
        original_confidence = pattern.get('confidence', 0.5)

        if validation_result.get('awaiting_outcome'):
            return original_confidence

        success = validation_result.get('success', False)

        if success:
            # Augmentation confiance
            adjustment = 0.1
        else:
            # Diminution confiance
            adjustment = -0.15

        new_confidence = original_confidence + adjustment
        return np.clip(new_confidence, 0.1, 0.95)