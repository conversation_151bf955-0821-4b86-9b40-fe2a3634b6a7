# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 791 à 812
# Type: Méthode de la classe BaccaratPredictorApp

    def _generate_decision_optimization_task(self):
        """Génère tâche RÉELLE d'optimisation décision"""
        # Analyse performance seuil actuel
        current_threshold = self.model_parameters['decision_threshold']
        if len(self.results) >= 5:
            threshold_performance = self._evaluate_threshold_performance(current_threshold)
            difficulty = 1.0 - threshold_performance  # Mauvaise performance = plus difficile
        else:
            difficulty = 0.4

        return {
            'type': 'decision_optimization',
            'description': 'Optimiser seuil décision pour maximiser précision',
            'parameters': {
                'target': 'accuracy_maximization',
                'current_threshold': current_threshold,
                'performance_score': getattr(self, '_last_threshold_perf', 0.5),
                'optimization_range': [0.35, 0.65]
            },
            'difficulty': difficulty,
            'mathematical_complexity': 0.7
        }