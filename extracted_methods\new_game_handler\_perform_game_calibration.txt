# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 95 à 146
# Type: Méthode de la classe NewGameHandler

    def _perform_game_calibration(self):
        """Effectue calibration complète pour nouvelle partie"""
        try:
            self.calibration_in_progress = True
            calibration_start = time.time()

            # ═══════════════════════════════════════════════════════════════════
            # CALIBRATION COMPLÈTE NOUVELLE PARTIE
            # ═══════════════════════════════════════════════════════════════════

            # 1. Calibration initialisation
            self._update_interface_status("🔄 Calibration système...")
            init_result = self.game_calibrator.start_new_game_calibration()

            if not init_result['success']:
                self._handle_calibration_error("Erreur calibration système", init_result)
                return

            # 2. Démarrage calibration temps réel
            self._update_interface_status("⚡ Activation calibration temps réel...")
            realtime_result = self.realtime_calibrator.start_realtime_calibration()

            if not realtime_result['success']:
                self._handle_calibration_error("Erreur calibration temps réel", realtime_result)
                return

            # 3. Reset données partie
            self._update_interface_status("🔄 Reset données partie...")
            self._reset_game_data()

            # 4. Configuration interface
            self._update_interface_status("🎯 Configuration interface...")
            self._configure_interface_for_new_game()

            # 5. Finalisation
            calibration_duration = time.time() - calibration_start

            # Mise à jour état
            self.game_active = True
            self.game_round = 0
            self.game_phase = 'warmup'
            self.calibration_in_progress = False

            # Mise à jour interface finale
            self._update_interface_status(f"✅ Prêt - Calibration: {calibration_duration:.1f}s")
            self._update_game_phase_display()

            logger.info(f"✅ Nouvelle partie AZR prête - Calibration: {calibration_duration:.1f}s")

        except Exception as e:
            logger.error(f"Erreur calibration partie: {e}")
            self._handle_calibration_error("Erreur calibration", {'error': str(e)})