# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 808 à 831
# Type: Méthode de la classe AdaptiveReasoner

    def _explore_parameter_space(self):
        """Exploration légère de l'espace des paramètres"""
        # Petites variations aléatoires pour exploration
        learning_rate_noise = np.random.normal(0, 0.01)
        confidence_threshold_noise = np.random.normal(0, 0.02)
        exploration_rate_noise = np.random.normal(0, 0.02)

        # Application avec contraintes
        self.adaptive_learning_rate += learning_rate_noise
        self.adaptive_learning_rate = np.clip(self.adaptive_learning_rate,
                                            self.learning_rate_range[0],
                                            self.learning_rate_range[1])

        self.adaptive_confidence_threshold += confidence_threshold_noise
        self.adaptive_confidence_threshold = np.clip(self.adaptive_confidence_threshold,
                                                   self.confidence_threshold_range[0],
                                                   self.confidence_threshold_range[1])

        self.adaptive_exploration_rate += exploration_rate_noise
        self.adaptive_exploration_rate = np.clip(self.adaptive_exploration_rate,
                                                self.exploration_rate_range[0],
                                                self.exploration_rate_range[1])

        self._update_current_parameter_set()