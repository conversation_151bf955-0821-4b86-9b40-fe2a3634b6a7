# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 485 à 503
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def get_learning_history(self) -> Dict[str, Any]:
        """Obtient historique d'apprentissage"""
        return {
            'total_sessions': len(self.game_sessions),
            'total_rounds_played': self.total_rounds_played,
            'learning_cycles': self.learning_cycles,
            'adaptation_events': len(self.adaptation_events),
            'recent_adaptations': self.adaptation_events[-5:],
            'performance_trend': [p['correct'] for p in self.performance_history[-50:]],
            'session_summaries': [
                {
                    'session_id': s['session_id'],
                    'rounds': s['rounds_played'],
                    'accuracy': s.get('final_stats', {}).get('accuracy', 0.0),
                    'duration': s.get('duration', 0.0)
                }
                for s in self.game_sessions[-10:]  # 10 dernières sessions
            ]
        }