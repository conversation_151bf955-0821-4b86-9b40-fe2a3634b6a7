# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 45 à 95
# Type: Méthode de la classe BaccaratAdaptiveReasoner

    def __init__(self):
        """Initialisation du raisonneur adaptatif"""
        self.pattern_proposer = BaccaratPatternProposer()
        self.pattern_validator = BaccaratPatternValidator()

        # Historique et état
        self.game_history = deque(maxlen=global_config.azr.max_game_history)
        self.successful_patterns = []
        self.failed_patterns = []
        self.active_patterns = {}

        # Métriques performance
        self.reasoning_sessions = 0
        self.successful_predictions = 0
        self.total_predictions = 0
        self.confidence_history = deque(maxlen=100)

        # Paramètres adaptatifs
        self.adaptation_rate = global_config.azr.adaptation_rate
        self.confidence_threshold = global_config.azr.confidence_threshold
        self.pattern_decay_rate = global_config.azr.pattern_decay_rate

        # ═══════════════════════════════════════════════════════════════════
        # OPTIMISATION MÉTA-PARAMÈTRES EN TEMPS RÉEL
        # ═══════════════════════════════════════════════════════════════════

        # Paramètres adaptatifs (optimisés en temps réel)
        self.adaptive_learning_rate = global_config.azr.learning_rate
        self.adaptive_confidence_threshold = global_config.azr.confidence_threshold
        self.adaptive_exploration_rate = global_config.azr.exploration_rate

        # Historique performance pour optimisation
        self.parameter_performance_history = []
        self.parameter_optimization_window = 20  # Fenêtre optimisation
        self.last_optimization_round = 0
        self.optimization_frequency = 10  # Optimise tous les 10 rounds

        # Plages d'optimisation
        self.learning_rate_range = (0.05, 0.25)
        self.confidence_threshold_range = (0.2, 0.6)
        self.exploration_rate_range = (0.1, 0.5)

        # Métriques optimisation
        self.current_parameter_set = {
            'learning_rate': self.adaptive_learning_rate,
            'confidence_threshold': self.adaptive_confidence_threshold,
            'exploration_rate': self.adaptive_exploration_rate
        }
        self.parameter_performance_score = 0.5

        logger.info("BaccaratAdaptiveReasoner initialisé avec paradigme AZR + Optimisation méta-paramètres")