# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 520 à 532
# Type: Méthode de la classe BaccaratPatternProposer

    def _filter_and_score_patterns(self, patterns: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        """Filtre et score les patterns proposés"""
        filtered = {}

        for pattern_type, pattern_list in patterns.items():
            # Filtrage par confiance minimale
            filtered_list = [p for p in pattern_list if p.get('confidence', 0) > self.confidence_threshold]

            # Limitation du nombre de patterns par type
            max_patterns = global_config.azr.max_patterns_per_type
            filtered[pattern_type] = filtered_list[:max_patterns]

        return filtered