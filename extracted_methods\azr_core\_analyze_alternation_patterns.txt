# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 552 à 559
# Type: Méthode de la classe AZRSolver

    def _analyze_alternation_patterns(self, sequence: List[int]) -> Dict[str, Any]:
        """Analyse patterns d'alternance"""
        alternation_rate = self._calculate_alternation_rate(sequence)

        return {
            'alternation_rate': alternation_rate,
            'pattern_type': 'high_alternation' if alternation_rate > 0.6 else 'low_alternation'
        }