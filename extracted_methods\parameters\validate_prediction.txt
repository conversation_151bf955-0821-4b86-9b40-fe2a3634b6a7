# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\parameters.py
# Lignes: 301 à 326
# Type: Méthode

def validate_prediction(prediction: dict) -> bool:
    """Valide une prédiction"""
    required_keys = ['predicted_outcome', 'confidence', 'player_probability', 'banker_probability']

    if not all(key in prediction for key in required_keys):
        return False

    # Validation valeurs
    if prediction['predicted_outcome'] not in [0, 1]:
        return False

    if not (0 <= prediction['confidence'] <= 1):
        return False

    if not (0 <= prediction['player_probability'] <= 1):
        return False

    if not (0 <= prediction['banker_probability'] <= 1):
        return False

    # Validation somme probabilités
    prob_sum = prediction['player_probability'] + prediction['banker_probability']
    if not (0.95 <= prob_sum <= 1.05):  # Tolérance pour erreurs d'arrondi
        return False

    return True