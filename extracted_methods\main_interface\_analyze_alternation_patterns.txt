# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 591 à 625
# Type: Méthode de la classe BaccaratPredictorApp

    def _analyze_alternation_patterns(self):
        """Analyse patterns d'alternance"""
        try:
            if len(self.results) < 4:
                return {'alternation_tendency': 0.0}

            # Compte alternances dans les dernières manches
            alternations = 0
            for i in range(len(self.results) - 1):
                if self.results[i] != self.results[i + 1]:
                    alternations += 1

            # Taux d'alternance
            alternation_rate = alternations / (len(self.results) - 1)

            # Si forte alternance récente, continue la tendance
            if alternation_rate > 0.6:
                # Prédit l'opposé du dernier résultat
                last_result = self.results[-1]
                alternation_tendency = 0.08 if last_result == 1 else -0.08
            elif alternation_rate < 0.3:
                # Faible alternance → Prédit même résultat
                last_result = self.results[-1]
                alternation_tendency = -0.05 if last_result == 1 else 0.05
            else:
                alternation_tendency = 0.0

            return {
                'alternation_tendency': alternation_tendency,
                'alternation_rate': alternation_rate
            }

        except Exception as e:
            logger.error(f"Erreur analyse alternance: {e}")
            return {'alternation_tendency': 0.0}