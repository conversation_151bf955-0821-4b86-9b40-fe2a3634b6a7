# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 477 à 488
# Type: Méthode de la classe AdaptiveReasoner

    def _calculate_pattern_age_factor(self, pattern: Dict) -> float:
        """Calcule facteur d'âge pour un pattern"""
        # Simulation âge basée sur position dans historique
        # En réalité, utiliserait timestamp
        base_factor = 1.0
        decay_per_session = self.pattern_decay_rate

        # Approximation: patterns plus anciens ont facteur plus faible
        age_sessions = max(0, self.reasoning_sessions - pattern.get('session_created', self.reasoning_sessions))
        age_factor = base_factor * (1.0 - decay_per_session) ** age_sessions

        return max(0.1, age_factor)