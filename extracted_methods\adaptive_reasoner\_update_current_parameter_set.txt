# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 833 à 842
# Type: Méthode de la classe AdaptiveReasoner

    def _update_current_parameter_set(self):
        """Met à jour ensemble de paramètres actuels"""
        self.current_parameter_set = {
            'learning_rate': self.adaptive_learning_rate,
            'confidence_threshold': self.adaptive_confidence_threshold,
            'exploration_rate': self.adaptive_exploration_rate
        }

        # Mise à jour paramètres utilisés dans le système
        self.confidence_threshold = self.adaptive_confidence_threshold