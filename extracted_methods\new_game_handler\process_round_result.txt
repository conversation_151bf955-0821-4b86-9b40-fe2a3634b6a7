# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 256 à 315
# Type: Méthode de la classe NewGameHandler

    def process_round_result(self, outcome: int) -> Dict[str, Any]:
        """
        Traite résultat d'une manche

        Args:
            outcome: 0 (Player) ou 1 (<PERSON>er)

        Returns:
            Dict contenant résultats traitement
        """
        try:
            if not self.game_active:
                return {
                    'success': False,
                    'message': 'Aucune partie active'
                }

            self.game_round += 1

            # Mise à jour phase selon manche
            self._update_game_phase()

            # Traitement calibration temps réel
            if hasattr(self.main_interface, 'last_prediction'):
                prediction_result = getattr(self.main_interface, 'last_prediction', {})

                # Calibration temps réel
                calibration_result = self.realtime_calibrator.process_round_result(
                    self.game_round, prediction_result, outcome
                )

                # Mise à jour seuils adaptatifs
                threshold_result = self.threshold_manager.update_thresholds_realtime(
                    self.game_round, prediction_result, outcome
                )

                # Mise à jour interface
                self._update_round_display()

                return {
                    'success': True,
                    'round': self.game_round,
                    'phase': self.game_phase,
                    'calibration_result': calibration_result,
                    'threshold_result': threshold_result
                }

            return {
                'success': True,
                'round': self.game_round,
                'phase': self.game_phase,
                'message': 'Manche traitée'
            }

        except Exception as e:
            logger.error(f"Erreur traitement manche: {e}")
            return {
                'success': False,
                'error': str(e)
            }