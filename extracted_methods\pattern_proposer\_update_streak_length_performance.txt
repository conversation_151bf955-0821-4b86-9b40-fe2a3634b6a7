# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 648 à 663
# Type: Méthode de la classe BaccaratPatternProposer

    def _update_streak_length_performance(self, length: int, success: bool):
        """Met à jour performance d'une longueur de série"""
        if length not in self.streak_length_performance:
            self.streak_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }

        if success:
            self.streak_length_performance[length]['successes'] += 1

        # Recalcul accuracy
        perf = self.streak_length_performance[length]
        if perf['attempts'] > 0:
            perf['accuracy'] = perf['successes'] / perf['attempts']