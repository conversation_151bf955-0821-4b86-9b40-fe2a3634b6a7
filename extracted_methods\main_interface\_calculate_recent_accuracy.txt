# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 723 à 745
# Type: Méthode de la classe BaccaratPredictorApp

    def _calculate_recent_accuracy(self, window_size):
        """Calcule précision récente"""
        try:
            if not self.predictions or not self.results:
                return 0.5

            # Données alignées
            min_len = min(len(self.predictions), len(self.results), window_size)
            if min_len == 0:
                return 0.5

            recent_predictions = self.predictions[-min_len:]
            recent_results = self.results[-min_len:]

            # Calcul précision
            correct = sum(1 for pred, result in zip(recent_predictions, recent_results)
                         if pred.get('predicted_outcome') == result)

            return correct / min_len if min_len > 0 else 0.5

        except Exception as e:
            logger.error(f"Erreur calcul précision récente: {e}")
            return 0.5