# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 1098 à 1243
# Type: Méthode de la classe AZRSystem

    def _analyze_complete_sequence_for_next_prediction(self, sequence: List[int],
                                                     sequence_length: int,
                                                     target_round: int) -> Dict[str, Any]:
        """Analyse COMPLÈTE de la séquence [1...N-1] pour prédire manche N"""
        try:
            # 📊 ANALYSE MULTI-NIVEAUX DE LA SÉQUENCE COMPLÈTE

            # 1. 🔢 ANALYSE FRÉQUENTIELLE GLOBALE
            player_count = sum(1 for x in sequence if x == 0)
            global_player_freq = player_count / sequence_length
            frequency_prediction = global_player_freq

            # 2. 🔄 ANALYSE PATTERNS RÉCENTS (derniers 20% de la séquence)
            recent_window = max(3, sequence_length // 5)  # 20% minimum 3
            recent_sequence = sequence[-recent_window:]
            recent_player_count = sum(1 for x in recent_sequence if x == 0)
            recent_player_freq = recent_player_count / len(recent_sequence)
            recent_prediction = recent_player_freq

            # 3. 🌊 ANALYSE PROBABILISTE DES STREAKS
            current_streak_info = self.solver._analyze_current_streak(sequence)
            streak_length = current_streak_info['length']
            streak_type = current_streak_info['type']

            # ✅ ANALYSE PROBABILISTE : Calcul probabilité basée sur historique des streaks
            streak_prediction = self._calculate_streak_probability(sequence, streak_length, streak_type)

            # 4. 🔀 ANALYSE PROBABILISTE DE L'ALTERNANCE
            alternation_rate = self.solver._calculate_alternation_rate(sequence)

            # ✅ ANALYSE PROBABILISTE : Calcul probabilité basée sur patterns d'alternance
            alternation_prediction = self._calculate_alternation_probability(sequence, alternation_rate)

            # 5. 📈 ANALYSE TENDANCES PAR FENÊTRES GLISSANTES
            window_size = min(7, max(3, sequence_length // 4))
            trend_analysis = self._analyze_sequence_trends(sequence, window_size)
            trend_prediction = trend_analysis['trend_prediction']
            trend_strength = trend_analysis['trend_strength']

            # 6. 🎯 ANALYSE PATTERNS CYCLIQUES
            cycle_analysis = self._analyze_cyclical_patterns(sequence, sequence_length)
            cycle_prediction = cycle_analysis['cycle_prediction']
            cycle_confidence = cycle_analysis['cycle_confidence']

            # 📊 PONDÉRATION INTELLIGENTE DES PRÉDICTIONS
            # Poids adaptatifs basés sur la longueur de séquence et consistance
            if sequence_length <= 5:
                # Séquence courte : privilégie fréquence et récent
                weights = {
                    'frequency': 0.4,
                    'recent': 0.3,
                    'streak': 0.2,
                    'alternation': 0.1,
                    'trend': 0.0,
                    'cycle': 0.0
                }
            elif sequence_length <= 15:
                # Séquence moyenne : ajoute streaks et alternance
                weights = {
                    'frequency': 0.3,
                    'recent': 0.25,
                    'streak': 0.25,
                    'alternation': 0.2,
                    'trend': 0.0,
                    'cycle': 0.0
                }
            else:
                # Séquence longue : utilise toutes les analyses
                weights = {
                    'frequency': 0.2,
                    'recent': 0.2,
                    'streak': 0.2,
                    'alternation': 0.15,
                    'trend': 0.15,
                    'cycle': 0.1
                }

            # 🧮 CALCUL PRÉDICTION PONDÉRÉE
            weighted_prediction = (
                weights['frequency'] * frequency_prediction +
                weights['recent'] * recent_prediction +
                weights['streak'] * streak_prediction +
                weights['alternation'] * alternation_prediction +
                weights['trend'] * trend_prediction +
                weights['cycle'] * cycle_prediction
            )

            # 📊 CALCUL CONFIANCE BASÉE SUR CONSISTANCE
            predictions = [frequency_prediction, recent_prediction, streak_prediction,
                          alternation_prediction, trend_prediction, cycle_prediction]

            # Variance des prédictions (plus faible = plus consistant)
            prediction_variance = np.var(predictions)
            consistency_score = max(0.0, 1.0 - prediction_variance * 2.0)

            # Confiance basée sur longueur séquence
            length_confidence = min(0.9, sequence_length / 30.0)

            # Confiance composite
            sequence_confidence = (consistency_score * 0.6 + length_confidence * 0.4)

            # 🎯 FORCE DE LA SÉQUENCE (patterns détectables)
            pattern_signals = [
                abs(frequency_prediction - 0.5) * 2,  # Force fréquentielle
                abs(recent_prediction - 0.5) * 2,     # Force récente
                abs(streak_prediction - 0.5) * 2,     # Force anti-streak
                abs(alternation_prediction - 0.5) * 2, # Force alternance
                trend_strength,                        # Force tendance
                cycle_confidence                       # Force cyclique
            ]

            sequence_strength = np.mean(pattern_signals)

            return {
                'weighted_prediction': weighted_prediction,
                'sequence_confidence': sequence_confidence,
                'sequence_strength': sequence_strength,
                'pattern_consistency': consistency_score,
                'analysis_details': {
                    'global_frequency': global_player_freq,
                    'recent_frequency': recent_player_freq,
                    'streak_info': current_streak_info,
                    'alternation_rate': alternation_rate,
                    'trend_analysis': trend_analysis,
                    'cycle_analysis': cycle_analysis,
                    'weights_used': weights,
                    'individual_predictions': {
                        'frequency': frequency_prediction,
                        'recent': recent_prediction,
                        'streak': streak_prediction,
                        'alternation': alternation_prediction,
                        'trend': trend_prediction,
                        'cycle': cycle_prediction
                    }
                }
            }

        except Exception as e:
            logger.error(f"Erreur analyse séquence complète: {e}")
            return {
                'weighted_prediction': 0.5,
                'sequence_confidence': 0.4,
                'sequence_strength': 0.3,
                'pattern_consistency': 0.3,
                'analysis_details': {'error': str(e)}
            }