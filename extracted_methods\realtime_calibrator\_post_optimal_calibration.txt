# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 424 à 474
# Type: Méthode de la classe RealtimeCalibrator

    def _post_optimal_calibration(self) -> Dict[str, Any]:
        """
        Calibration post-optimale (manches 61+)
        Stabilisation et adaptation conservative
        """
        try:
            old_parameters = self.calibrated_parameters.copy()

            # ═══════════════════════════════════════════════════════════════════
            # STABILISATION CONSERVATIVE
            # ═══════════════════════════════════════════════════════════════════

            # Analyse stabilité performance
            stability_analysis = self._analyze_performance_stability()

            # Ajustements conservateurs seulement si dégradation
            if stability_analysis['performance_degrading']:
                # Ajustements minimaux pour stabilisation
                conservative_adjustments = self._calculate_conservative_adjustments(
                    stability_analysis
                )

                # Application ajustements conservateurs
                for param, adjustment in conservative_adjustments.items():
                    if param in self.calibrated_parameters:
                        old_value = self.calibrated_parameters[param]
                        new_value = np.clip(
                            old_value + adjustment * 0.5,  # Facteur conservateur
                            self.parameter_ranges[param][0],
                            self.parameter_ranges[param][1]
                        )
                        self.calibrated_parameters[param] = new_value

                improvement = self._calculate_improvement(old_parameters, self.calibrated_parameters)
            else:
                # Pas d'ajustement si performance stable
                improvement = 0.0

            return {
                'success': True,
                'phase': 'post_optimal',
                'old_parameters': old_parameters,
                'new_parameters': self.calibrated_parameters.copy(),
                'improvement': improvement,
                'stability_analysis': stability_analysis,
                'conservative_mode': True
            }

        except Exception as e:
            logger.error(f"Erreur calibration post-optimal: {e}")
            return {'success': False, 'error': str(e)}