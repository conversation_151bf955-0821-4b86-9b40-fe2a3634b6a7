# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 298 à 314
# Type: Méthode de la classe AZRBaccaratPredictor

    def _create_fallback_prediction(self) -> Dict[str, Any]:
        """Crée prédiction de fallback en cas d'erreur"""
        return {
            'player_probability': 0.5,
            'banker_probability': 0.5,
            'predicted_outcome': 0,
            'prediction_strength': 0.0,
            'confidence': 0.5,
            'uncertainty': 0.8,
            'recommendation': 'Attendre - Erreur système',
            'reasoning_details': {'error': True},
            'pattern_breakdown': {'total_patterns': 0},
            'meta_info': {'error': True},
            'prediction_time': 0.0,
            'model_type': 'AZR_FALLBACK',
            'session_active': False
        }