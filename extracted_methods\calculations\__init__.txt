# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 78 à 87
# Type: Méthode de la classe ConfidenceCalculator

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        # Utilisation configuration centralisée
        calc_config = global_config.calculations
        self.confidence_threshold = calc_config.confidence_threshold
        self.uncertainty_threshold = calc_config.uncertainty_threshold
        self.prediction_threshold = calc_config.prediction_threshold
        self.confidence_base_factor = calc_config.confidence_base_factor
        self.confidence_diversity_factor = calc_config.confidence_diversity_factor
        self.max_prob_center = calc_config.max_prob_center
        self.confidence_multiplier = calc_config.confidence_multiplier