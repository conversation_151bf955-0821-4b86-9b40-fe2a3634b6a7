"""
🧪 TEST AZR INTEGRATION - VÉRIFICATION SYSTÈME UNIFIÉ
====================================================

Tests de vérification de l'intégration complète du système AZR unifié.
Vérifie que tous les paramètres sont centralisés et cohérents.
"""

import logging
import sys
import traceback
from typing import Dict, Any

# Configuration logging pour tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_unified_parameters():
    """Test du système de paramètres unifiés"""
    try:
        from azr_unified_parameters import get_azr_orchestrator
        
        logger.info("🧪 Test 1: Orchestrateur AZR unifié")
        orchestrator = get_azr_orchestrator()
        
        # Test récupération paramètres
        all_params = orchestrator.get_all_parameters()
        
        # Vérifications
        assert 'wait' in all_params, "Paramètres WAIT manquants"
        assert 'confidence' in all_params, "Paramètres confiance manquants"
        assert 'streaks' in all_params, "Paramètres streaks manquants"
        assert 'patterns' in all_params, "Paramètres patterns manquants"
        assert 'shared_state' in all_params, "État partagé manquant"
        assert 'foundation' in all_params, "Paramètres fondamentaux manquants"
        
        logger.info("✅ Test 1 réussi: Orchestrateur unifié fonctionnel")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 1 échoué: {e}")
        traceback.print_exc()
        return False

def test_integration_manager():
    """Test du gestionnaire d'intégration"""
    try:
        from azr_integration_manager import get_integration_manager
        
        logger.info("🧪 Test 2: Gestionnaire d'intégration")
        manager = get_integration_manager()
        
        # Test intégration composants
        azr_config = manager.integrate_azr_core()
        wait_config = manager.integrate_wait_system()
        models_config = manager.integrate_models()
        patterns_config = manager.integrate_patterns()
        
        # Vérifications
        assert 'learning_rate' in azr_config, "Configuration AZR incomplète"
        assert 'base_wait_threshold' in wait_config, "Configuration WAIT incomplète"
        assert 'lstm' in models_config, "Configuration modèles incomplète"
        assert 'streaks' in patterns_config, "Configuration patterns incomplète"
        
        logger.info("✅ Test 2 réussi: Gestionnaire d'intégration fonctionnel")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 2 échoué: {e}")
        traceback.print_exc()
        return False

def test_azr_core_integration():
    """Test intégration AZR Core"""
    try:
        logger.info("🧪 Test 3: Intégration AZR Core")
        
        # Import avec intégration
        from azr_core import AZRSystem
        
        # Création instance
        azr = AZRSystem(use_strategic_wait=True)
        
        # Vérifications
        assert hasattr(azr, 'integration_manager'), "Gestionnaire d'intégration manquant"
        assert hasattr(azr, 'orchestrator'), "Orchestrateur manquant"
        assert azr.learning_rate > 0, "Learning rate invalide"
        assert azr.target_accuracy > 0, "Target accuracy invalide"
        
        logger.info("✅ Test 3 réussi: AZR Core intégré")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 3 échoué: {e}")
        traceback.print_exc()
        return False

def test_wait_system_integration():
    """Test intégration système WAIT"""
    try:
        logger.info("🧪 Test 4: Intégration système WAIT")
        
        # Import avec intégration
        from conditions_wait_strategiques_azr import StrategicWAITEngine
        
        # Création instance
        wait_engine = StrategicWAITEngine()
        
        # Vérifications
        assert hasattr(wait_engine, 'integration_manager'), "Gestionnaire d'intégration manquant"
        assert hasattr(wait_engine, 'orchestrator'), "Orchestrateur manquant"
        assert hasattr(wait_engine, 'wait_config'), "Configuration WAIT manquante"
        
        # Test paramètres adaptatifs
        assert wait_engine.adaptive_params['base_wait_threshold'] > 0, "Seuil WAIT invalide"
        assert wait_engine.adaptive_params['performance_target'] > 0, "Cible performance invalide"
        
        logger.info("✅ Test 4 réussi: Système WAIT intégré")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 4 échoué: {e}")
        traceback.print_exc()
        return False

def test_parameter_coherence():
    """Test cohérence des paramètres"""
    try:
        logger.info("🧪 Test 5: Cohérence des paramètres")
        
        from azr_unified_parameters import get_azr_orchestrator
        
        orchestrator = get_azr_orchestrator()
        all_params = orchestrator.get_all_parameters()
        
        # Vérifications cohérence mathématique
        foundation = all_params['foundation']
        wait_params = all_params['wait']
        confidence_params = all_params['confidence']
        
        # Golden ratio utilisé
        assert foundation['golden_ratio'] == 0.618033988749, "Golden ratio incorrect"
        
        # Relations cohérentes
        assert wait_params['min_wait_threshold'] < wait_params['max_wait_threshold'], "Seuils WAIT incohérents"
        assert confidence_params['min_confidence'] < confidence_params['max_confidence'], "Seuils confiance incohérents"
        
        # Cibles réalistes
        assert 0.5 <= foundation['performance_target'] <= 0.7, "Cible performance irréaliste"
        assert 0.5 <= foundation['confidence_target'] <= 0.8, "Cible confiance irréaliste"
        
        logger.info("✅ Test 5 réussi: Paramètres cohérents")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 5 échoué: {e}")
        traceback.print_exc()
        return False

def test_dynamic_adaptation():
    """Test adaptation dynamique"""
    try:
        logger.info("🧪 Test 6: Adaptation dynamique")
        
        from azr_unified_parameters import get_azr_orchestrator
        from azr_integration_manager import sync_azr_system
        
        orchestrator = get_azr_orchestrator()
        
        # Simulation prédiction
        prediction_result = {
            'predicted_outcome': 1,
            'confidence': 0.65,
            'method': 'test'
        }
        
        # Test synchronisation
        sync_azr_system(prediction_result, actual_outcome=1)
        
        # Vérification mise à jour état
        diagnostic = orchestrator.get_diagnostic_info()
        assert diagnostic['total_iterations'] > 0, "Compteur itérations non mis à jour"
        assert diagnostic['recent_performance'] >= 0, "Performance non mise à jour"
        
        logger.info("✅ Test 6 réussi: Adaptation dynamique fonctionnelle")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test 6 échoué: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Exécute tous les tests d'intégration"""
    logger.info("🚀 DÉBUT DES TESTS D'INTÉGRATION AZR UNIFIÉ")
    logger.info("=" * 60)
    
    tests = [
        test_unified_parameters,
        test_integration_manager,
        test_azr_core_integration,
        test_wait_system_integration,
        test_parameter_coherence,
        test_dynamic_adaptation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            logger.error(f"Erreur test {test.__name__}: {e}")
            results.append(False)
    
    # Résumé
    passed = sum(results)
    total = len(results)
    
    logger.info("=" * 60)
    logger.info(f"📊 RÉSULTATS: {passed}/{total} tests réussis")
    
    if passed == total:
        logger.info("🎉 INTÉGRATION COMPLÈTE RÉUSSIE!")
        logger.info("✅ Tous les composants sont unifiés et cohérents")
        logger.info("✅ Paramètres centralisés fonctionnels")
        logger.info("✅ Adaptation dynamique active")
        logger.info("✅ Système prêt pour utilisation")
    else:
        logger.warning(f"⚠️  {total - passed} tests ont échoué")
        logger.warning("🔧 Vérifiez les erreurs ci-dessus")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
