# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 621 à 653
# Type: Méthode de la classe UncertaintyCalculator

    def calculate_lstm_uncertainty_dropout(self, model, X: np.ndarray, n_samples: int = 10) -> np.ndarray:
        """Calcule incertitude LSTM via Dropout Monte Carlo"""
        try:
            if not hasattr(model, 'is_trained') or not model.is_trained:
                return np.full(len(X), global_config.calculations.default_confidence)

            # Activer dropout pour Monte Carlo
            model.train()
            predictions = []

            import torch
            for _ in range(n_samples):
                with torch.no_grad():
                    X_tensor = torch.FloatTensor(X)
                    outputs = model(X_tensor)
                    probas = torch.softmax(outputs, dim=1)
                    predictions.append(probas[:, 1].numpy())  # Probabilité Banker

            model.eval()  # Remettre en mode évaluation

            # Calculer variance des prédictions
            predictions = np.array(predictions)
            uncertainty = np.var(predictions, axis=0)

            # ✅ Normalisation stabilisée pour LSTM
            # Utilise fonction logarithmique pour éviter amplification excessive
            uncertainty = np.clip(np.log1p(uncertainty * 10.0) / np.log(3.0), 0.0, 1.0)

            return uncertainty

        except Exception as e:
            logger.error(f"Erreur incertitude LSTM dropout: {e}")
            return np.full(len(X), global_config.calculations.default_confidence)