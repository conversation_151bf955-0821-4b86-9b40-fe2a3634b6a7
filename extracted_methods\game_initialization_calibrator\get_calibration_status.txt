# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\game_initialization_calibrator.py
# Lignes: 400 à 413
# Type: Méthode de la classe GameInitializationCalibrator

    def get_calibration_status(self) -> Dict[str, Any]:
        """Obtient statut calibration"""
        return {
            'calibration_complete': self.calibration_complete,
            'calibration_duration': self.calibration_duration,
            'optimal_parameters': self.optimal_parameters.copy(),
            'system_resources': {
                'ram_available': self.available_ram,
                'cpu_cores': self.cpu_cores,
                'ram_allocated': self.max_ram_gb
            },
            'ready_for_60_rounds': self.calibration_complete,
            'calibration_history_count': len(self.calibration_history)
        }