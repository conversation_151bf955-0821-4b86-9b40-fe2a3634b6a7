# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\main_interface.py
# Lignes: 749 à 768
# Type: Méthode de la classe BaccaratPredictorApp

    def _generate_confidence_calibration_task(self):
        """Génère tâche RÉELLE de calibration confiance"""
        # Calcul Brier Score actuel pour calibration
        if len(self.predictions) >= 3 and len(self.results) >= 3:
            brier_score = self._calculate_brier_score()
            difficulty = min(0.9, brier_score * 2.0)  # Plus le score est mauvais, plus c'est difficile
        else:
            difficulty = 0.5

        return {
            'type': 'confidence_calibration',
            'description': 'Calibrer confiance prédictions via Brier Score',
            'parameters': {
                'target': 'brier_score_minimization',
                'current_brier_score': getattr(self, '_last_brier_score', 0.25),
                'target_improvement': 0.05
            },
            'difficulty': difficulty,
            'mathematical_complexity': 0.8
        }