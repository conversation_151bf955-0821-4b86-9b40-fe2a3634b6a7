# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\self_play_engine.py
# Lignes: 368 à 375
# Type: Méthode de la classe BaccaratSelfPlayEngine

    def pause_session(self) -> Dict[str, Any]:
        """Met en pause la session"""
        if not self.is_running:
            return {'success': False, 'message': 'Aucune session active'}

        self.is_paused = True
        logger.info("Session self-play mise en pause")
        return {'success': True, 'message': 'Session mise en pause'}