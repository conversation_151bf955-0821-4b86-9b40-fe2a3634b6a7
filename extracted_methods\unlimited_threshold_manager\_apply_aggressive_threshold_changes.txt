# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 298 à 322
# Type: Méthode de la classe UnlimitedThresholdManager

    def _apply_aggressive_threshold_changes(self):
        """Applique changements agressifs pour sortir du plateau"""
        try:
            logger.info("Application changements agressifs - Sortie plateau performance")
            
            for threshold_name in self.current_thresholds.keys():
                min_val, max_val = self.adaptation_ranges[threshold_name]
                
                # Changements agressifs aléatoires
                if np.random.random() < 0.5:
                    # Exploration vers les extrêmes
                    if np.random.random() < 0.5:
                        new_value = min_val + (max_val - min_val) * 0.1  # Vers minimum
                    else:
                        new_value = min_val + (max_val - min_val) * 0.9  # Vers maximum
                else:
                    # Variation aléatoire importante
                    current_value = self.current_thresholds[threshold_name]
                    variation = (max_val - min_val) * 0.2 * (np.random.random() - 0.5)
                    new_value = np.clip(current_value + variation, min_val, max_val)
                
                self.current_thresholds[threshold_name] = new_value
            
        except Exception as e:
            logger.error(f"Erreur changements agressifs: {e}")