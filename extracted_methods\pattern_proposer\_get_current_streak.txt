# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 427 à 441
# Type: Méthode de la classe BaccaratPatternProposer

    def _get_current_streak(self, results: List[int]) -> Dict:
        """Obtient la série actuelle"""
        if not results:
            return {'length': 0, 'outcome': None}

        current_outcome = results[-1]
        length = 1

        for i in range(len(results) - 2, -1, -1):
            if results[i] == current_outcome:
                length += 1
            else:
                break

        return {'length': length, 'outcome': current_outcome}