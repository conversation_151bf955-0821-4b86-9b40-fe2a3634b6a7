# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 419 à 465
# Type: Méthode de la classe LGBMModel

    def _extract_features(self, sequence):
        """Extraction features pour LGBM"""
        if len(sequence) == 0:
            return np.array([0.5, 0.5, 0.5, 0.5, 0.5]).reshape(1, -1)

        features = []

        # Feature 1: Fréquence Player/Banker
        player_freq = sum(1 for x in sequence if x == 0) / len(sequence)
        features.append(player_freq)

        # Feature 2: Longueur streak actuelle
        if len(sequence) >= 2:
            current_streak = 1
            for i in range(len(sequence)-2, -1, -1):
                if sequence[i] == sequence[-1]:
                    current_streak += 1
                else:
                    break
            features.append(min(current_streak / 10, 1.0))  # Normalisation
        else:
            features.append(0.1)

        # Feature 3: Taux d'alternance récent
        if len(sequence) >= 3:
            alternations = sum(1 for i in range(len(sequence)-2)
                             if sequence[i] != sequence[i+1])
            alt_rate = alternations / (len(sequence) - 1)
            features.append(alt_rate)
        else:
            features.append(0.5)

        # Feature 4: Variance récente
        if len(sequence) >= 4:
            recent_var = np.var(sequence[-4:])
            features.append(recent_var)
        else:
            features.append(0.25)

        # Feature 5: Momentum
        if len(sequence) >= 3:
            momentum = (sequence[-1] - sequence[-3]) / 2
            features.append(momentum + 0.5)  # Centrage sur 0.5
        else:
            features.append(0.5)

        return np.array(features).reshape(1, -1)