# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\new_game_handler.py
# Lignes: 148 à 172
# Type: Méthode de la classe NewGameHandler

    def _reset_game_data(self):
        """Reset toutes les données de partie"""
        try:
            # Reset données interface
            if hasattr(self.main_interface, 'results'):
                self.main_interface.results.clear()

            if hasattr(self.main_interface, 'predictions'):
                self.main_interface.predictions.clear()

            # Reset compteurs
            self.game_round = 0

            # Reset graphique
            if hasattr(self.main_interface, 'draw_trend_chart'):
                self.main_interface.draw_trend_chart()

            # Reset statistiques
            if hasattr(self.main_interface, 'update_stats'):
                self.main_interface.update_stats()

            logger.info("Données partie réinitialisées")

        except Exception as e:
            logger.error(f"Erreur reset données: {e}")