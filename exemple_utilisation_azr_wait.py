"""
EXEMPLE D'UTILISATION - AZR AVEC WAIT STRATÉGIQUE
================================================

Exemple simple d'utilisation du système AZR intégré avec WAIT stratégique
pour prédictions Baccarat optimisées.
"""

import logging
import numpy as np
from typing import List

# Configuration logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def exemple_session_baccarat():
    """Exemple d'une session complète de Baccarat avec AZR + WAIT stratégique"""
    
    logger.info("🎰 Début session Baccarat avec AZR + WAIT stratégique")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # 1. INITIALISATION SYSTÈME AZR
    # ═══════════════════════════════════════════════════════════════════════════════
    
    try:
        from azr_core import create_azr_predictor
        
        # Création prédicteur avec WAIT stratégique activé
        azr_predictor = create_azr_predictor(use_strategic_wait=True)
        
        logger.info("✅ Prédicteur AZR initialisé avec WAIT stratégique")
        
    except ImportError as e:
        logger.error(f"❌ Erreur import: {e}")
        logger.error("Vérifiez que azr_core.py et conditions_wait_strategiques_azr.py sont présents")
        return
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # 2. SIMULATION HISTORIQUE INITIAL
    # ═══════════════════════════════════════════════════════════════════════════════
    
    # Historique initial (30 premiers rounds simulés)
    # 0 = Player, 1 = Banker
    game_history = [
        0, 1, 0, 0, 1, 1, 0, 1, 0, 1,  # Rounds 1-10
        1, 0, 1, 1, 0, 0, 1, 0, 1, 0,  # Rounds 11-20
        0, 0, 1, 0, 1, 1, 1, 0, 0, 1   # Rounds 21-30
    ]
    
    logger.info(f"📊 Historique initial: {len(game_history)} rounds")
    logger.info(f"   Player: {game_history.count(0)} | Banker: {game_history.count(1)}")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # 3. PRÉDICTIONS AVEC WAIT STRATÉGIQUE
    # ═══════════════════════════════════════════════════════════════════════════════
    
    logger.info("\n🎯 Début prédictions avec WAIT stratégique:")
    logger.info("-" * 50)
    
    # Statistiques session
    session_stats = {
        'total_rounds': 0,
        'predictions_made': 0,
        'wait_recommendations': 0,
        'correct_predictions': 0,
        'incorrect_predictions': 0
    }
    
    # Simulation rounds 31-60
    for round_number in range(31, 61):
        
        # ═══════════════════════════════════════════════════════════════════
        # PRÉDICTION AZR AVEC WAIT STRATÉGIQUE
        # ═══════════════════════════════════════════════════════════════════
        
        prediction = azr_predictor.predict_sequence(game_history, round_number=round_number)
        
        # Analyse de la décision
        decision_type = prediction.get('decision_type', 'UNKNOWN')
        recommendation = prediction.get('recommendation', 'N/A')
        confidence = prediction.get('confidence', 0.0)
        
        session_stats['total_rounds'] += 1
        
        if decision_type == 'WAIT':
            # Recommandation WAIT
            session_stats['wait_recommendations'] += 1
            wait_reasoning = prediction.get('wait_reasoning', 'Conditions défavorables')
            triggered_conditions = prediction.get('triggered_conditions', [])
            
            logger.info(f"Round {round_number:2d}: 🔶 WAIT")
            logger.info(f"            Raison: {wait_reasoning}")
            if triggered_conditions:
                logger.info(f"            Conditions: {', '.join(triggered_conditions)}")
            
        else:
            # Prédiction Player/Banker
            session_stats['predictions_made'] += 1
            predicted_outcome = prediction.get('predicted_outcome')
            
            logger.info(f"Round {round_number:2d}: 🎯 {recommendation}")
            logger.info(f"            Confiance: {confidence:.3f}")
            
            # Bonus confiance pour prédictions sélectionnées
            confidence_boost = prediction.get('confidence_boost', 0.0)
            if confidence_boost > 0:
                logger.info(f"            Bonus WAIT: +{confidence_boost:.3f}")
        
        # ═══════════════════════════════════════════════════════════════════
        # SIMULATION RÉSULTAT RÉEL
        # ═══════════════════════════════════════════════════════════════════
        
        # Simulation résultat réaliste (légèrement biaisé vers équilibre)
        recent_player_freq = game_history[-10:].count(0) / 10
        if recent_player_freq > 0.7:
            prob_player = 0.4  # Tendance retour équilibre
        elif recent_player_freq < 0.3:
            prob_player = 0.6
        else:
            prob_player = 0.5
        
        actual_outcome = 0 if np.random.random() < prob_player else 1
        
        # ═══════════════════════════════════════════════════════════════════
        # VALIDATION ET MISE À JOUR
        # ═══════════════════════════════════════════════════════════════════
        
        # Validation de la prédiction
        azr_predictor.validate_prediction(actual_outcome)
        
        # Vérification si prédiction était correcte
        if decision_type == 'PREDICT':
            predicted_outcome = prediction.get('predicted_outcome')
            if predicted_outcome == actual_outcome:
                session_stats['correct_predictions'] += 1
                logger.info(f"            ✅ Correct! (Résultat: {'Player' if actual_outcome == 0 else 'Banker'})")
            else:
                session_stats['incorrect_predictions'] += 1
                logger.info(f"            ❌ Incorrect (Résultat: {'Player' if actual_outcome == 0 else 'Banker'})")
        else:
            logger.info(f"            Résultat: {'Player' if actual_outcome == 0 else 'Banker'} (WAIT - pas évalué)")
        
        # Mise à jour historique
        game_history.append(actual_outcome)
        
        logger.info("")  # Ligne vide pour lisibilité
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # 4. STATISTIQUES FINALES
    # ═══════════════════════════════════════════════════════════════════════════════
    
    logger.info("📈 STATISTIQUES SESSION:")
    logger.info("=" * 50)
    
    # Statistiques générales
    logger.info(f"Total rounds: {session_stats['total_rounds']}")
    logger.info(f"Prédictions faites: {session_stats['predictions_made']}")
    logger.info(f"Recommandations WAIT: {session_stats['wait_recommendations']}")
    
    # Taux de couverture
    coverage_rate = session_stats['predictions_made'] / session_stats['total_rounds']
    wait_rate = session_stats['wait_recommendations'] / session_stats['total_rounds']
    
    logger.info(f"Taux de couverture: {coverage_rate:.1%}")
    logger.info(f"Taux WAIT: {wait_rate:.1%}")
    
    # Précision sur prédictions
    if session_stats['predictions_made'] > 0:
        accuracy = session_stats['correct_predictions'] / session_stats['predictions_made']
        logger.info(f"Précision sur prédictions: {accuracy:.1%}")
        logger.info(f"Prédictions correctes: {session_stats['correct_predictions']}")
        logger.info(f"Prédictions incorrectes: {session_stats['incorrect_predictions']}")
        
        # Amélioration vs baseline
        baseline_accuracy = 0.50  # 50% baseline
        improvement = accuracy - baseline_accuracy
        logger.info(f"Amélioration vs baseline: {improvement:+.1%}")
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # 5. STATISTIQUES SYSTÈME WAIT
    # ═══════════════════════════════════════════════════════════════════════════════
    
    logger.info("\n🎯 STATISTIQUES SYSTÈME WAIT:")
    logger.info("-" * 50)
    
    wait_stats = azr_predictor.get_strategic_wait_statistics()
    
    if wait_stats.get('strategic_wait_active'):
        perf_metrics = wait_stats.get('performance_metrics', {})
        wait_engine_stats = wait_stats.get('strategic_wait_stats', {})
        
        logger.info(f"Système WAIT actif: ✅")
        logger.info(f"Total décisions: {perf_metrics.get('total_predictions', 0)}")
        logger.info(f"Taux WAIT global: {perf_metrics.get('wait_rate', 0.0):.1%}")
        logger.info(f"Précision améliorée: {perf_metrics.get('accuracy_improvement', 0.0):+.1%}")
        
        # Conditions les plus fréquentes
        if 'strategic_wait_stats' in wait_stats:
            logger.info("Conditions WAIT les plus fréquentes:")
            # (Cette information serait disponible avec plus de détails dans le système complet)
    
    # ═══════════════════════════════════════════════════════════════════════════════
    # 6. RECOMMANDATIONS
    # ═══════════════════════════════════════════════════════════════════════════════
    
    logger.info("\n💡 RECOMMANDATIONS:")
    logger.info("-" * 50)
    
    if coverage_rate >= 0.7:
        logger.info("✅ Bon taux de couverture - Système équilibré")
    elif coverage_rate < 0.5:
        logger.info("⚠️ Taux de couverture faible - Système très conservateur")
    
    if session_stats['predictions_made'] > 0:
        if accuracy >= 0.60:
            logger.info("✅ Excellente précision - Système WAIT très efficace")
        elif accuracy >= 0.55:
            logger.info("✅ Bonne précision - Système WAIT efficace")
        elif accuracy >= 0.50:
            logger.info("⚠️ Précision modérée - Ajustements possibles")
        else:
            logger.info("❌ Précision faible - Révision nécessaire")
    
    logger.info("\n🎉 Session terminée avec succès!")

def exemple_analyse_conditions():
    """Exemple d'analyse détaillée des conditions WAIT"""
    
    logger.info("\n🔍 ANALYSE DÉTAILLÉE CONDITIONS WAIT")
    logger.info("=" * 50)
    
    try:
        from azr_core import create_azr_predictor
        
        azr_predictor = create_azr_predictor(use_strategic_wait=True)
        
        # Scénarios de test
        scenarios = {
            'Confiance élevée': [0, 1, 0, 1, 0, 1] * 5,  # Pattern régulier
            'Streak extrême': [0] * 15,                   # Streak Player long
            'Volatilité élevée': [0, 1, 1, 0, 0, 1, 0, 1, 1, 0] * 3,
            'Historique court': [0, 1, 0],               # Peu de données
            'Équilibré stable': [0, 1, 0, 0, 1, 1, 0, 1, 0, 1] * 3
        }
        
        for scenario_name, history in scenarios.items():
            logger.info(f"\n--- Scénario: {scenario_name} ---")
            
            prediction = azr_predictor.predict_sequence(history, round_number=35)
            
            decision_type = prediction.get('decision_type', 'UNKNOWN')
            logger.info(f"Décision: {decision_type}")
            
            if decision_type == 'WAIT':
                logger.info(f"Raison: {prediction.get('wait_reasoning', 'N/A')}")
                logger.info(f"Score WAIT: {prediction.get('wait_score', 0.0):.3f}")
                logger.info(f"Seuil adaptatif: {prediction.get('adaptive_threshold', 0.5):.3f}")
                
                conditions = prediction.get('triggered_conditions', [])
                if conditions:
                    logger.info(f"Conditions déclenchées: {', '.join(conditions)}")
            else:
                logger.info(f"Recommandation: {prediction.get('recommendation', 'N/A')}")
                logger.info(f"Confiance: {prediction.get('confidence', 0.0):.3f}")
                logger.info(f"Bonus WAIT: +{prediction.get('confidence_boost', 0.0):.3f}")
        
    except Exception as e:
        logger.error(f"Erreur analyse conditions: {e}")

def main():
    """Fonction principale"""
    
    logger.info("🚀 EXEMPLE D'UTILISATION AZR + WAIT STRATÉGIQUE")
    logger.info("=" * 70)
    
    # Exemple session complète
    exemple_session_baccarat()
    
    # Analyse conditions
    exemple_analyse_conditions()
    
    logger.info("\n" + "=" * 70)
    logger.info("✅ Exemple terminé avec succès!")
    logger.info("\nPour utiliser dans votre code:")
    logger.info("```python")
    logger.info("from azr_core import create_azr_predictor")
    logger.info("")
    logger.info("# Création prédicteur")
    logger.info("azr = create_azr_predictor(use_strategic_wait=True)")
    logger.info("")
    logger.info("# Prédiction")
    logger.info("result = azr.predict_sequence(game_history, round_number=35)")
    logger.info("")
    logger.info("# Validation")
    logger.info("azr.validate_prediction(actual_outcome)")
    logger.info("```")

if __name__ == "__main__":
    main()
