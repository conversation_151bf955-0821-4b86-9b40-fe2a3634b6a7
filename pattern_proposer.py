"""
PATTERN PROPOSER - AZR CORE
============================

Proposeur de patterns pour Baccarat basé sur le paradigme AZR.
Implémente le rôle "Proposer" du système Absolute Zero Reasoner.
"""

import numpy as np
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import time
from collections import deque, defaultdict

# Import configuration centralisée (maintenant dans le même dossier)
from parameters import global_config

# Configuration logging
logger = logging.getLogger(__name__)

class BaccaratPatternProposer:
    """
    Proposeur de patterns pour Baccarat - Paradigme AZR Authentique

    Implémente le rôle "Proposer" du paradigme Absolute Zero Reasoner.
    Génère des tâches d'apprentissage optimales dans la zone de learnability
    [0.4, 0.6] pour maximiser l'amélioration du solver.

    Exploite l'espace d'états fini du Baccarat (2^60 séquences) et la
    non-indépendance des coups pour proposer des séquences d'apprentissage.
    """

    def __init__(self):
        """Initialisation du proposeur de patterns"""
        self.recent_history = deque(maxlen=global_config.azr.max_history_length)
        self.pattern_hypotheses = []
        self.successful_patterns = {}
        self.failed_patterns = {}

        # Paramètres AZR
        self.min_pattern_length = global_config.azr.min_pattern_length
        self.max_pattern_length = global_config.azr.max_pattern_length
        self.confidence_threshold = global_config.azr.confidence_threshold

        # ═══════════════════════════════════════════════════════════════════
        # AUTO-OPTIMISATION LONGUEURS SÉQUENCES
        # ═══════════════════════════════════════════════════════════════════

        # Tracking performance par longueur de séquence
        self.sequence_length_performance = {}  # {length: {'successes': int, 'attempts': int, 'accuracy': float}}
        self.cycle_length_performance = {}     # {length: {'successes': int, 'attempts': int, 'accuracy': float}}
        self.streak_length_performance = {}    # {length: {'successes': int, 'attempts': int, 'accuracy': float}}

        # Plages de test pour auto-découverte
        self.sequence_length_range = (2, 12)   # Test longueurs 2-12
        self.cycle_length_range = (3, 10)      # Test cycles 3-10
        self.streak_length_range = (2, 8)      # Test séries 2-8

        # Longueurs optimales découvertes (mise à jour automatique)
        self.optimal_sequence_lengths = [2, 3, 4]  # Valeurs initiales
        self.optimal_cycle_lengths = [3, 4, 5]     # Valeurs initiales
        self.optimal_streak_lengths = [2, 3, 4]    # Valeurs initiales

        # Paramètres optimisation
        self.min_attempts_for_optimization = 10    # Minimum tentatives avant optimisation
        self.optimization_frequency = 20           # Optimise tous les 20 patterns
        self.pattern_attempts_count = 0            # Compteur pour optimisation

        # Historique découvertes
        self.length_optimization_history = []

        # Nouveaux paramètres AZR optimaux
        self.buffer_size = global_config.azr.buffer_size
        self.learnability_min = global_config.azr.learnability_zone_min
        self.learnability_max = global_config.azr.learnability_zone_max
        self.proposer_solver_balance = global_config.azr.proposer_solver_balance

        # Buffer des tâches passées pour conditionnement
        self.task_buffer = deque(maxlen=self.buffer_size)
        self.sequence_proposals = []
        self.learnability_scores = []

        # Exploitation non-indépendance
        self.finite_state_exploitation = global_config.azr.finite_state_exploitation
        self.sequence_dependency_modeling = global_config.azr.sequence_dependency_modeling
        self.conditional_probability_chains = global_config.azr.conditional_probability_chains

        # Métriques proposer AZR
        self.total_proposals = 0
        self.successful_proposals = 0  # Proposals dans zone learnability
        self.proposal_performance_history = deque(maxlen=100)

        logger.info("BaccaratPatternProposer initialisé avec paradigme AZR authentique + Exploitation non-indépendance")

    def propose_patterns(self, recent_results: List[int]) -> Dict[str, List[Dict]]:
        """
        Génère hypothèses de patterns basées sur historique récent

        Args:
            recent_results: Liste des résultats récents (0=Player, 1=Banker)

        Returns:
            Dict contenant différents types de patterns proposés
        """
        try:
            if len(recent_results) < self.min_pattern_length:
                return self._generate_default_patterns()

            # Mise à jour historique
            self.recent_history.extend(recent_results)

            # Génération patterns par type
            patterns = {
                'sequences': self._propose_sequence_patterns(recent_results),
                'frequencies': self._propose_frequency_patterns(recent_results),
                'trends': self._propose_trend_patterns(recent_results),
                'alternations': self._propose_alternation_patterns(recent_results),
                'streaks': self._propose_streak_patterns(recent_results),
                'cycles': self._propose_cycle_patterns(recent_results)
            }

            # Filtrage et scoring
            filtered_patterns = self._filter_and_score_patterns(patterns)

            logger.info(f"Proposé {sum(len(p) for p in filtered_patterns.values())} patterns")
            return filtered_patterns

        except Exception as e:
            logger.error(f"Erreur proposition patterns: {e}")
            return self._generate_default_patterns()

    def propose_learning_sequences(self, current_solver_performance: float) -> Dict[str, Any]:
        """
        Propose des séquences d'apprentissage selon paradigme AZR authentique

        Génère des tâches dans la zone de learnability optimale [0.4, 0.6]
        pour maximiser l'amélioration du solver.

        Args:
            current_solver_performance: Performance actuelle du solver

        Returns:
            Dict contenant séquences proposées et récompense learnability
        """
        try:
            self.total_proposals += 1

            # Génération séquence conditionnée sur buffer passé
            proposed_sequence = self._generate_conditioned_sequence()

            # Calcul récompense learnability
            learnability_reward = self._calculate_learnability_reward(
                proposed_sequence, current_solver_performance
            )

            # Validation zone optimale
            is_in_optimal_zone = (
                self.learnability_min <= learnability_reward <= self.learnability_max
            )

            if is_in_optimal_zone:
                self.successful_proposals += 1
                self.task_buffer.append({
                    'sequence': proposed_sequence,
                    'learnability': learnability_reward,
                    'timestamp': time.time()
                })

            # Mise à jour métriques
            self.learnability_scores.append(learnability_reward)
            self.proposal_performance_history.append(learnability_reward)

            return {
                'proposed_sequence': proposed_sequence,
                'learnability_reward': learnability_reward,
                'is_optimal': is_in_optimal_zone,
                'proposer_accuracy': self.successful_proposals / max(1, self.total_proposals),
                'task_type': 'sequence_learning'
            }

        except Exception as e:
            logger.error(f"Erreur proposition séquences apprentissage: {e}")
            return self._generate_default_learning_task()

    def _propose_sequence_patterns(self, results: List[int]) -> List[Dict]:
        """
        Propose patterns de séquences courtes - LONGUEURS AUTO-OPTIMISÉES
        Utilise les longueurs découvertes comme optimales par le système
        """
        patterns = []

        # ═══════════════════════════════════════════════════════════════════
        # UTILISATION LONGUEURS OPTIMALES AUTO-DÉCOUVERTES
        # ═══════════════════════════════════════════════════════════════════

        # Test longueurs optimales découvertes + exploration nouvelles longueurs
        lengths_to_test = self.optimal_sequence_lengths.copy()

        # Ajout exploration nouvelles longueurs (10% du temps)
        if np.random.random() < 0.1:
            exploration_length = np.random.randint(*self.sequence_length_range)
            if exploration_length not in lengths_to_test:
                lengths_to_test.append(exploration_length)

        for length in lengths_to_test:
            if length <= len(results):
                for i in range(len(results) - length + 1):
                    sequence = results[i:i+length]

                    # Calcul confiance basée sur logique Baccarat
                    confidence = self._calculate_sequence_confidence(sequence, results)

                    if confidence > self.confidence_threshold:
                        pattern = {
                            'type': 'sequence',
                            'pattern': sequence,
                            'length': length,
                            'confidence': confidence,
                            'last_seen': i + length - 1,
                            'frequency': self._count_sequence_frequency(sequence, results),
                            'optimal_length': length in self.optimal_sequence_lengths
                        }
                        patterns.append(pattern)

                        # Enregistrement tentative pour optimisation
                        self._record_sequence_attempt(length)

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:10]

    def _propose_frequency_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns basés sur fréquences"""
        patterns = []

        if len(results) < 3:
            return patterns

        # Analyse fréquence globale
        player_count = results.count(0)
        banker_count = results.count(1)
        total = len(results)

        player_freq = player_count / total
        banker_freq = banker_count / total

        # Pattern fréquence si déséquilibre significatif
        if abs(player_freq - 0.5) > 0.1:
            dominant_outcome = 0 if player_freq > banker_freq else 1
            confidence = abs(player_freq - 0.5) * 2  # Normalisation 0-1

            pattern = {
                'type': 'frequency',
                'dominant_outcome': dominant_outcome,
                'frequency': player_freq if dominant_outcome == 0 else banker_freq,
                'confidence': confidence,
                'sample_size': total,
                'deviation_from_random': abs(player_freq - 0.5)
            }
            patterns.append(pattern)

        return patterns

    def _propose_trend_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns de tendances"""
        patterns = []

        if len(results) < 5:
            return patterns

        # Analyse tendance récente (derniers 5-10 résultats)
        for window_size in [5, 7, 10]:
            if window_size <= len(results):
                recent_window = results[-window_size:]
                player_count = recent_window.count(0)
                trend_strength = abs(player_count / window_size - 0.5) * 2

                if trend_strength > 0.3:  # Seuil tendance significative
                    dominant = 0 if player_count > window_size / 2 else 1
                    
                    pattern = {
                        'type': 'trend',
                        'window_size': window_size,
                        'dominant_outcome': dominant,
                        'trend_strength': trend_strength,
                        'confidence': min(trend_strength * 1.5, 0.9),
                        'recent_dominance': player_count / window_size
                    }
                    patterns.append(pattern)

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:3]
