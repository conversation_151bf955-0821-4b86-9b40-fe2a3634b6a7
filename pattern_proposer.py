"""
PATTERN PROPOSER - AZR CORE
============================

Génère des hypothèses de patterns pour Baccarat sans entraînement préalable.
Basé sur le paradigme Absolute Zero Reasoner.
"""

import numpy as np
import sys
import os
import time
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from collections import Counter, deque
import logging

# Import configuration centralisée (maintenant dans le même dossier)
from parameters import global_config

# Configuration logging
logger = logging.getLogger(__name__)

class BaccaratPatternProposer:
    """
    Proposeur de patterns pour Baccarat - Paradigme AZR Authentique

    Implémente le rôle "Proposer" du paradigme Absolute Zero Reasoner.
    Génère des tâches d'apprentissage optimales dans la zone de learnability
    [0.4, 0.6] pour maximiser l'amélioration du solver.

    Exploite l'espace d'états fini du Baccarat (2^60 séquences) et la
    non-indépendance des coups pour proposer des séquences d'apprentissage.
    """

    def __init__(self):
        """Initialisation du proposeur de patterns"""
        self.recent_history = deque(maxlen=global_config.azr.max_history_length)
        self.pattern_hypotheses = []
        self.successful_patterns = {}
        self.failed_patterns = {}

        # Paramètres AZR
        self.min_pattern_length = global_config.azr.min_pattern_length
        self.max_pattern_length = global_config.azr.max_pattern_length
        self.confidence_threshold = global_config.azr.confidence_threshold

        # ═══════════════════════════════════════════════════════════════════
        # AUTO-OPTIMISATION LONGUEURS SÉQUENCES
        # ═══════════════════════════════════════════════════════════════════

        # Tracking performance par longueur de séquence
        self.sequence_length_performance = {}  # {length: {'successes': int, 'attempts': int, 'accuracy': float}}
        self.cycle_length_performance = {}     # {length: {'successes': int, 'attempts': int, 'accuracy': float}}
        self.streak_length_performance = {}    # {length: {'successes': int, 'attempts': int, 'accuracy': float}}

        # Plages de test pour auto-découverte
        self.sequence_length_range = (2, 12)   # Test longueurs 2-12
        self.cycle_length_range = (3, 10)      # Test cycles 3-10
        self.streak_length_range = (2, 8)      # Test séries 2-8

        # Longueurs optimales découvertes (mise à jour automatique)
        self.optimal_sequence_lengths = [2, 3, 4]  # Valeurs initiales
        self.optimal_cycle_lengths = [3, 4, 5]     # Valeurs initiales
        self.optimal_streak_lengths = [2, 3, 4]    # Valeurs initiales

        # Paramètres optimisation
        self.min_attempts_for_optimization = 10    # Minimum tentatives avant optimisation
        self.optimization_frequency = 20           # Optimise tous les 20 patterns
        self.pattern_attempts_count = 0            # Compteur pour optimisation

        # Historique découvertes
        self.length_optimization_history = []

        # Nouveaux paramètres AZR optimaux
        self.buffer_size = global_config.azr.buffer_size
        self.learnability_min = global_config.azr.learnability_zone_min
        self.learnability_max = global_config.azr.learnability_zone_max
        self.proposer_solver_balance = global_config.azr.proposer_solver_balance

        # Buffer des tâches passées pour conditionnement
        self.task_buffer = deque(maxlen=self.buffer_size)
        self.sequence_proposals = []
        self.learnability_scores = []

        # Exploitation non-indépendance
        self.finite_state_exploitation = global_config.azr.finite_state_exploitation
        self.sequence_dependency_modeling = global_config.azr.sequence_dependency_modeling
        self.conditional_probability_chains = global_config.azr.conditional_probability_chains

        # Métriques proposer AZR
        self.total_proposals = 0
        self.successful_proposals = 0  # Proposals dans zone learnability
        self.proposal_performance_history = deque(maxlen=100)

        logger.info("BaccaratPatternProposer initialisé avec paradigme AZR authentique + Exploitation non-indépendance")

    def propose_patterns(self, recent_results: List[int]) -> Dict[str, List[Dict]]:
        """
        Génère hypothèses de patterns basées sur historique récent

        Args:
            recent_results: Liste des résultats récents (0=Player, 1=Banker)

        Returns:
            Dict contenant différents types de patterns proposés
        """
        try:
            if len(recent_results) < self.min_pattern_length:
                return self._generate_default_patterns()

            # Mise à jour historique
            self.recent_history.extend(recent_results)

            # Génération patterns par type
            patterns = {
                'sequences': self._propose_sequence_patterns(recent_results),
                'frequencies': self._propose_frequency_patterns(recent_results),
                'trends': self._propose_trend_patterns(recent_results),
                'alternations': self._propose_alternation_patterns(recent_results),
                'streaks': self._propose_streak_patterns(recent_results),
                'cycles': self._propose_cycle_patterns(recent_results)
            }

            # Filtrage et scoring
            filtered_patterns = self._filter_and_score_patterns(patterns)

            logger.info(f"Proposé {sum(len(p) for p in filtered_patterns.values())} patterns")
            return filtered_patterns

        except Exception as e:
            logger.error(f"Erreur proposition patterns: {e}")
            return self._generate_default_patterns()

    def propose_learning_sequences(self, current_solver_performance: float) -> Dict[str, Any]:
        """
        Propose des séquences d'apprentissage selon paradigme AZR authentique

        Génère des tâches dans la zone de learnability optimale [0.4, 0.6]
        pour maximiser l'amélioration du solver.

        Args:
            current_solver_performance: Performance actuelle du solver

        Returns:
            Dict contenant séquences proposées et récompense learnability
        """
        try:
            self.total_proposals += 1

            # Génération séquence conditionnée sur buffer passé
            proposed_sequence = self._generate_conditioned_sequence()

            # Calcul récompense learnability
            learnability_reward = self._calculate_learnability_reward(
                proposed_sequence, current_solver_performance
            )

            # Validation zone optimale
            is_in_optimal_zone = (
                self.learnability_min <= learnability_reward <= self.learnability_max
            )

            if is_in_optimal_zone:
                self.successful_proposals += 1
                self.task_buffer.append({
                    'sequence': proposed_sequence,
                    'learnability': learnability_reward,
                    'timestamp': time.time()
                })

            # Mise à jour métriques
            self.learnability_scores.append(learnability_reward)
            self.proposal_performance_history.append(learnability_reward)

            return {
                'proposed_sequence': proposed_sequence,
                'learnability_reward': learnability_reward,
                'is_optimal': is_in_optimal_zone,
                'proposer_accuracy': self.successful_proposals / max(1, self.total_proposals),
                'task_type': 'sequence_learning'
            }

        except Exception as e:
            logger.error(f"Erreur proposition séquences apprentissage: {e}")
            return self._generate_default_learning_task()

    def _propose_sequence_patterns(self, results: List[int]) -> List[Dict]:
        """
        Propose patterns de séquences courtes - LONGUEURS AUTO-OPTIMISÉES
        Utilise les longueurs découvertes comme optimales par le système
        """
        patterns = []

        # ═══════════════════════════════════════════════════════════════════
        # UTILISATION LONGUEURS OPTIMALES AUTO-DÉCOUVERTES
        # ═══════════════════════════════════════════════════════════════════

        # Test longueurs optimales découvertes + exploration nouvelles longueurs
        lengths_to_test = self.optimal_sequence_lengths.copy()

        # Ajout exploration nouvelles longueurs (10% du temps)
        if self.pattern_attempts_count % 10 == 0:
            exploration_length = self._get_exploration_sequence_length()
            if exploration_length not in lengths_to_test:
                lengths_to_test.append(exploration_length)

        for length in lengths_to_test:
            if length <= len(results):
                for i in range(len(results) - length + 1):
                    sequence = results[i:i+length]

                    # Calcul confiance basée sur logique Baccarat
                    confidence = self._calculate_sequence_confidence(sequence, results)

                    if confidence > self.confidence_threshold:
                        pattern = {
                            'type': 'sequence',
                            'pattern': sequence,
                            'length': length,
                            'confidence': confidence,
                            'last_seen': i + length - 1,
                            'frequency': self._count_sequence_frequency(sequence, results),
                            'optimal_length': length in self.optimal_sequence_lengths
                        }
                        patterns.append(pattern)

                        # Enregistrement tentative pour optimisation
                        self._record_sequence_attempt(length)

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:10]

    def _propose_frequency_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns basés sur fréquences"""
        patterns = []

        if len(results) < 10:
            return patterns

        # Analyse fréquences par fenêtres glissantes
        window_sizes = [10, 15, 20, 30]

        for window_size in window_sizes:
            if len(results) >= window_size:
                window = results[-window_size:]
                player_freq = window.count(0) / len(window)
                banker_freq = window.count(1) / len(window)

                # Détection déséquilibres significatifs
                if abs(player_freq - 0.5) > 0.15:  # Seuil de déséquilibre
                    dominant = 0 if player_freq > banker_freq else 1
                    confidence = abs(player_freq - 0.5) * 2  # Normalisation

                    patterns.append({
                        'type': 'frequency',
                        'pattern': f'dominant_{dominant}',
                        'window_size': window_size,
                        'dominant_outcome': dominant,
                        'frequency_ratio': max(player_freq, banker_freq),
                        'confidence': confidence,
                        'deviation': abs(player_freq - 0.5)
                    })

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:5]

    def _propose_trend_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns de tendances"""
        patterns = []

        if len(results) < 6:
            return patterns

        # Analyse tendances par segments
        segment_sizes = [6, 9, 12, 15]

        for segment_size in segment_sizes:
            if len(results) >= segment_size:
                segment = results[-segment_size:]

                # Calcul tendance (pente)
                x = np.arange(len(segment))
                trend_slope = np.polyfit(x, segment, 1)[0]

                # Confiance basée sur consistance de la tendance
                confidence = self._calculate_trend_confidence(segment, trend_slope)

                if confidence > self.confidence_threshold:
                    patterns.append({
                        'type': 'trend',
                        'pattern': 'increasing' if trend_slope > 0 else 'decreasing',
                        'slope': trend_slope,
                        'segment_size': segment_size,
                        'confidence': confidence,
                        'direction': 1 if trend_slope > 0 else 0
                    })

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:3]

    def _propose_alternation_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns d'alternance"""
        patterns = []

        if len(results) < 4:
            return patterns

        # Détection alternances parfaites et quasi-parfaites
        for start_idx in range(len(results) - 3):
            for length in range(4, min(len(results) - start_idx + 1, 13)):
                segment = results[start_idx:start_idx + length]

                alternation_score = self._calculate_alternation_score(segment)

                if alternation_score > 0.7:  # Seuil alternance
                    patterns.append({
                        'type': 'alternation',
                        'pattern': 'alternating',
                        'segment': segment,
                        'length': length,
                        'alternation_score': alternation_score,
                        'confidence': alternation_score,
                        'start_with': segment[0]
                    })

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:3]

    def _propose_streak_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns de séries"""
        patterns = []

        if len(results) < 3:
            return patterns

        # Détection séries actuelles et récentes
        current_streak = self._get_current_streak(results)
        recent_streaks = self._analyze_recent_streaks(results)

        if current_streak['length'] >= 4:  # STREAK SIGNIFICATIVE À PARTIR DE 4
            # Prédiction continuation ou rupture
            continuation_prob = self._calculate_streak_continuation_probability(
                current_streak, recent_streaks
            )

            patterns.append({
                'type': 'streak',
                'pattern': 'continuation' if continuation_prob > 0.5 else 'break',
                'current_streak': current_streak,
                'continuation_probability': continuation_prob,
                'confidence': abs(continuation_prob - 0.5) * 2,
                'predicted_outcome': current_streak['outcome'] if continuation_prob > 0.5 else 1 - current_streak['outcome']
            })

        return patterns

    def _propose_cycle_patterns(self, results: List[int]) -> List[Dict]:
        """
        Propose patterns cycliques - LONGUEURS AUTO-OPTIMISÉES
        Utilise les longueurs de cycles découvertes comme optimales
        """
        patterns = []

        if len(results) < 8:
            return patterns

        # ═══════════════════════════════════════════════════════════════════
        # UTILISATION LONGUEURS CYCLES OPTIMALES AUTO-DÉCOUVERTES
        # ═══════════════════════════════════════════════════════════════════

        # Test longueurs cycles optimales découvertes + exploration
        cycle_lengths_to_test = self.optimal_cycle_lengths.copy()

        # Ajout exploration nouvelles longueurs cycles (15% du temps)
        if self.pattern_attempts_count % 7 == 0:
            exploration_cycle_length = self._get_exploration_cycle_length()
            if exploration_cycle_length not in cycle_lengths_to_test:
                cycle_lengths_to_test.append(exploration_cycle_length)

        # Recherche cycles avec longueurs optimisées
        for cycle_length in cycle_lengths_to_test:
            if cycle_length <= len(results) // 2:
                cycle_confidence = self._detect_cycle_pattern(results, cycle_length)

                if cycle_confidence > self.confidence_threshold:
                    # Prédiction basée sur position dans le cycle
                    position_in_cycle = len(results) % cycle_length
                    cycle_pattern = results[-cycle_length:]
                    predicted_outcome = cycle_pattern[position_in_cycle] if position_in_cycle < len(cycle_pattern) else None

                    pattern = {
                        'type': 'cycle',
                        'pattern': f'cycle_{cycle_length}',
                        'cycle_length': cycle_length,
                        'cycle_pattern': cycle_pattern,
                        'position_in_cycle': position_in_cycle,
                        'predicted_outcome': predicted_outcome,
                        'confidence': cycle_confidence,
                        'optimal_length': cycle_length in self.optimal_cycle_lengths
                    }
                    patterns.append(pattern)

                    # Enregistrement tentative pour optimisation
                    self._record_cycle_attempt(cycle_length)

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:2]

    def _calculate_sequence_confidence(self, sequence: List[int], results: List[int]) -> float:
        """
        Calcule confiance d'une séquence - LOGIQUE BACCARAT PURE
        SUPPRESSION fréquences relatives statistiques inadaptées au Baccarat
        """
        try:
            # ═══════════════════════════════════════════════════════════════════
            # LOGIQUE BACCARAT SPÉCIFIQUE (pas de fréquences relatives)
            # ═══════════════════════════════════════════════════════════════════

            if len(sequence) > len(results):
                return 0.0

            # Vérification séquence récente (plus important que fréquence globale)
            recent_window = min(20, len(results))  # Fenêtre récente Baccarat
            recent_results = results[-recent_window:]

            # Bonus majeur si séquence apparaît en fin
            if len(results) >= len(sequence):
                if results[-len(sequence):] == sequence:
                    return 0.8  # Confiance élevée pour pattern immédiat

            # Recherche dans fenêtre récente seulement
            recent_count = self._count_sequence_frequency(sequence, recent_results)

            # Logique Baccarat: patterns récents plus significatifs
            if recent_count >= 2:
                return 0.6  # Pattern récurrent récent
            elif recent_count == 1:
                return 0.4  # Pattern présent récemment
            else:
                return 0.1  # Pattern absent récemment

        except Exception:
            return 0.0

    def _count_sequence_frequency(self, sequence: List[int], results: List[int]) -> int:
        """Compte fréquence d'apparition d'une séquence"""
        count = 0
        seq_len = len(sequence)

        for i in range(len(results) - seq_len + 1):
            if results[i:i+seq_len] == sequence:
                count += 1

        return count

    def _calculate_trend_confidence(self, segment: List[int], slope: float) -> float:
        """
        Calcule confiance d'une tendance - LOGIQUE BACCARAT PURE
        SUPPRESSION corrélations statistiques inadaptées au Baccarat
        """
        try:
            # ═══════════════════════════════════════════════════════════════════
            # LOGIQUE BACCARAT SPÉCIFIQUE (pas de corrélations mathématiques)
            # ═══════════════════════════════════════════════════════════════════

            # Analyse directe de la consistance du pattern
            if len(segment) < 3:
                return 0.0

            # Vérification tendance simple (pas de régression)
            recent_changes = []
            for i in range(1, len(segment)):
                if segment[i] != segment[i-1]:
                    recent_changes.append(1)
                else:
                    recent_changes.append(0)

            # Confiance basée sur consistance directe
            if len(recent_changes) == 0:
                return 0.0

            change_rate = sum(recent_changes) / len(recent_changes)

            # Logique Baccarat: tendances modérées plus fiables
            if 0.3 <= change_rate <= 0.7:
                return 0.6  # Tendance équilibrée
            elif change_rate < 0.3:
                return 0.4  # Trop stable (suspect)
            else:
                return 0.3  # Trop chaotique

        except Exception:
            return 0.0

    def _calculate_alternation_score(self, segment: List[int]) -> float:
        """Calcule score d'alternance d'un segment"""
        if len(segment) < 2:
            return 0.0

        alternations = 0
        for i in range(1, len(segment)):
            if segment[i] != segment[i-1]:
                alternations += 1

        max_alternations = len(segment) - 1
        return alternations / max_alternations if max_alternations > 0 else 0.0

    def _get_current_streak(self, results: List[int]) -> Dict:
        """Obtient la série actuelle"""
        if not results:
            return {'length': 0, 'outcome': None}

        current_outcome = results[-1]
        length = 1

        for i in range(len(results) - 2, -1, -1):
            if results[i] == current_outcome:
                length += 1
            else:
                break

        return {'length': length, 'outcome': current_outcome}

    def _analyze_recent_streaks(self, results: List[int]) -> List[Dict]:
        """Analyse les séries récentes"""
        streaks = []
        if len(results) < 2:
            return streaks

        current_outcome = results[0]
        current_length = 1

        for i in range(1, len(results)):
            if results[i] == current_outcome:
                current_length += 1
            else:
                if current_length >= 4:  # STREAK SIGNIFICATIVE À PARTIR DE 4
                    streaks.append({
                        'outcome': current_outcome,
                        'length': current_length,
                        'end_position': i - 1
                    })
                current_outcome = results[i]
                current_length = 1

        # Ajouter la série finale si elle existe
        if current_length >= 4:  # STREAK SIGNIFICATIVE À PARTIR DE 4
            streaks.append({
                'outcome': current_outcome,
                'length': current_length,
                'end_position': len(results) - 1
            })

        return streaks[-5:]  # Garder les 5 séries les plus récentes

    def _calculate_streak_continuation_probability(self, current_streak: Dict, recent_streaks: List[Dict]) -> float:
        """
        Calcule probabilité de continuation d'une série - LOGIQUE BACCARAT PURE
        SUPPRESSION des moyennes statistiques inadaptées au Baccarat
        """
        if not recent_streaks:
            return 0.5  # Probabilité neutre

        # ═══════════════════════════════════════════════════════════════════
        # LOGIQUE BACCARAT SPÉCIFIQUE (pas de moyennes statistiques)
        # ═══════════════════════════════════════════════════════════════════

        current_length = current_streak['length']

        # Règles empiriques Baccarat - STREAKS SIGNIFICATIVES À PARTIR DE 4
        if current_length == 4:
            return 0.65  # Séries de 4 tendent à continuer (première streak significative)
        elif current_length == 5:
            return 0.60  # Séries de 5 encore probables
        elif current_length == 6:
            return 0.50  # Séries de 6 équilibrées
        elif current_length >= 7:
            return 0.40  # Séries très longues tendent à se casser
        else:
            return 0.5   # Cas par défaut (< 4 répétitions)

    def _detect_cycle_pattern(self, results: List[int], cycle_length: int) -> float:
        """Détecte patterns cycliques"""
        if len(results) < cycle_length * 2:
            return 0.0

        # Vérification répétition du pattern
        pattern = results[-cycle_length:]
        matches = 0
        total_checks = 0

        for i in range(len(results) - cycle_length, -1, -cycle_length):
            if i >= cycle_length:
                segment = results[i-cycle_length:i]
                total_checks += 1
                if segment == pattern:
                    matches += 1

        return matches / total_checks if total_checks > 0 else 0.0

    def _filter_and_score_patterns(self, patterns: Dict[str, List[Dict]]) -> Dict[str, List[Dict]]:
        """Filtre et score les patterns proposés"""
        filtered = {}

        for pattern_type, pattern_list in patterns.items():
            # Filtrage par confiance minimale
            filtered_list = [p for p in pattern_list if p.get('confidence', 0) > self.confidence_threshold]

            # Limitation du nombre de patterns par type
            max_patterns = global_config.azr.max_patterns_per_type
            filtered[pattern_type] = filtered_list[:max_patterns]

        return filtered

    def _generate_default_patterns(self) -> Dict[str, List[Dict]]:
        """Génère patterns par défaut si historique insuffisant"""
        return {
            'sequences': [],
            'frequencies': [],
            'trends': [],
            'alternations': [],
            'streaks': [],
            'cycles': []
        }

    def update_pattern_performance(self, pattern: Dict, success: bool):
        """
        Met à jour performance d'un pattern + OPTIMISATION LONGUEURS
        Enregistre performance par longueur pour auto-découverte optimums
        """
        pattern_key = self._get_pattern_key(pattern)

        if success:
            if pattern_key not in self.successful_patterns:
                self.successful_patterns[pattern_key] = 0
            self.successful_patterns[pattern_key] += 1
        else:
            if pattern_key not in self.failed_patterns:
                self.failed_patterns[pattern_key] = 0
            self.failed_patterns[pattern_key] += 1

        # ═══════════════════════════════════════════════════════════════════
        # MISE À JOUR PERFORMANCE PAR LONGUEUR POUR AUTO-OPTIMISATION
        # ═══════════════════════════════════════════════════════════════════

        pattern_type = pattern.get('type')

        # Mise à jour performance selon type de pattern
        if pattern_type == 'sequence':
            length = pattern.get('length')
            if length:
                self._update_sequence_length_performance(length, success)
        elif pattern_type == 'cycle':
            cycle_length = pattern.get('cycle_length')
            if cycle_length:
                self._update_cycle_length_performance(cycle_length, success)
        elif pattern_type == 'streak':
            streak_length = pattern.get('current_streak', {}).get('length')
            if streak_length:
                self._update_streak_length_performance(streak_length, success)

        # Optimisation périodique des longueurs
        self.pattern_attempts_count += 1
        if self.pattern_attempts_count % self.optimization_frequency == 0:
            self._optimize_sequence_lengths()

    def _get_pattern_key(self, pattern: Dict) -> str:
        """Génère clé unique pour un pattern"""
        return f"{pattern['type']}_{str(pattern.get('pattern', ''))}"

    # ═══════════════════════════════════════════════════════════════════
    # MÉTHODES AUTO-OPTIMISATION LONGUEURS SÉQUENCES
    # ═══════════════════════════════════════════════════════════════════

    def _record_sequence_attempt(self, length: int):
        """Enregistre tentative pour une longueur de séquence"""
        if length not in self.sequence_length_performance:
            self.sequence_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }
        self.sequence_length_performance[length]['attempts'] += 1

    def _record_cycle_attempt(self, length: int):
        """Enregistre tentative pour une longueur de cycle"""
        if length not in self.cycle_length_performance:
            self.cycle_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }
        self.cycle_length_performance[length]['attempts'] += 1

    def _update_sequence_length_performance(self, length: int, success: bool):
        """Met à jour performance d'une longueur de séquence"""
        if length not in self.sequence_length_performance:
            self.sequence_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }

        if success:
            self.sequence_length_performance[length]['successes'] += 1

        # Recalcul accuracy
        perf = self.sequence_length_performance[length]
        if perf['attempts'] > 0:
            perf['accuracy'] = perf['successes'] / perf['attempts']

    def _update_cycle_length_performance(self, length: int, success: bool):
        """Met à jour performance d'une longueur de cycle"""
        if length not in self.cycle_length_performance:
            self.cycle_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }

        if success:
            self.cycle_length_performance[length]['successes'] += 1

        # Recalcul accuracy
        perf = self.cycle_length_performance[length]
        if perf['attempts'] > 0:
            perf['accuracy'] = perf['successes'] / perf['attempts']

    def _update_streak_length_performance(self, length: int, success: bool):
        """Met à jour performance d'une longueur de série"""
        if length not in self.streak_length_performance:
            self.streak_length_performance[length] = {
                'successes': 0,
                'attempts': 0,
                'accuracy': 0.0
            }

        if success:
            self.streak_length_performance[length]['successes'] += 1

        # Recalcul accuracy
        perf = self.streak_length_performance[length]
        if perf['attempts'] > 0:
            perf['accuracy'] = perf['successes'] / perf['attempts']

    def _optimize_sequence_lengths(self):
        """Optimise les longueurs de séquences basées sur performance"""
        try:
            # Optimisation séquences
            self._optimize_lengths_by_type('sequence', self.sequence_length_performance,
                                         self.optimal_sequence_lengths, self.sequence_length_range)

            # Optimisation cycles
            self._optimize_lengths_by_type('cycle', self.cycle_length_performance,
                                         self.optimal_cycle_lengths, self.cycle_length_range)

            # Optimisation séries
            self._optimize_lengths_by_type('streak', self.streak_length_performance,
                                         self.optimal_streak_lengths, self.streak_length_range)

            # Enregistrement historique
            optimization_entry = {
                'timestamp': time.time(),
                'optimal_sequences': self.optimal_sequence_lengths.copy(),
                'optimal_cycles': self.optimal_cycle_lengths.copy(),
                'optimal_streaks': self.optimal_streak_lengths.copy(),
                'performance_data': {
                    'sequences': dict(self.sequence_length_performance),
                    'cycles': dict(self.cycle_length_performance),
                    'streaks': dict(self.streak_length_performance)
                }
            }
            self.length_optimization_history.append(optimization_entry)

            # Limitation historique
            if len(self.length_optimization_history) > 50:
                self.length_optimization_history = self.length_optimization_history[-50:]

            logger.info(f"Optimisation longueurs: Séquences={self.optimal_sequence_lengths}, "
                       f"Cycles={self.optimal_cycle_lengths}, Séries={self.optimal_streak_lengths}")

        except Exception as e:
            logger.error(f"Erreur optimisation longueurs: {e}")

    def _optimize_lengths_by_type(self, pattern_type: str, performance_data: Dict,
                                 optimal_lengths: List[int], length_range: Tuple[int, int]):
        """Optimise longueurs pour un type de pattern spécifique"""
        # Filtrage longueurs avec assez de données
        valid_lengths = []
        for length, perf in performance_data.items():
            if perf['attempts'] >= self.min_attempts_for_optimization:
                valid_lengths.append((length, perf['accuracy']))

        if len(valid_lengths) < 2:
            return  # Pas assez de données

        # Tri par accuracy décroissante
        valid_lengths.sort(key=lambda x: x[1], reverse=True)

        # Sélection top longueurs (max 5)
        top_lengths = [length for length, accuracy in valid_lengths[:5] if accuracy > 0.4]

        # Mise à jour longueurs optimales si amélioration significative
        if top_lengths:
            current_avg_accuracy = self._calculate_average_accuracy(optimal_lengths, performance_data)
            new_avg_accuracy = self._calculate_average_accuracy(top_lengths, performance_data)

            if new_avg_accuracy > current_avg_accuracy + 0.05:  # Amélioration 5%+
                optimal_lengths.clear()
                optimal_lengths.extend(top_lengths[:3])  # Max 3 longueurs optimales
                logger.info(f"Nouvelles longueurs optimales {pattern_type}: {optimal_lengths} "
                           f"(accuracy: {current_avg_accuracy:.3f} → {new_avg_accuracy:.3f})")

    def _calculate_average_accuracy(self, lengths: List[int], performance_data: Dict) -> float:
        """Calcule accuracy moyenne pour une liste de longueurs"""
        if not lengths:
            return 0.0

        total_accuracy = 0.0
        valid_count = 0

        for length in lengths:
            if length in performance_data and performance_data[length]['attempts'] > 0:
                total_accuracy += performance_data[length]['accuracy']
                valid_count += 1

        return total_accuracy / valid_count if valid_count > 0 else 0.0

    def _get_exploration_sequence_length(self) -> int:
        """Obtient longueur de séquence pour exploration"""
        min_len, max_len = self.sequence_length_range
        return np.random.randint(min_len, max_len + 1)

    def _get_exploration_cycle_length(self) -> int:
        """Obtient longueur de cycle pour exploration"""
        min_len, max_len = self.cycle_length_range
        return np.random.randint(min_len, max_len + 1)

    def get_length_optimization_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques d'optimisation des longueurs"""
        return {
            'optimal_lengths': {
                'sequences': self.optimal_sequence_lengths.copy(),
                'cycles': self.optimal_cycle_lengths.copy(),
                'streaks': self.optimal_streak_lengths.copy()
            },
            'performance_data': {
                'sequences': dict(self.sequence_length_performance),
                'cycles': dict(self.cycle_length_performance),
                'streaks': dict(self.streak_length_performance)
            },
            'optimization_history_count': len(self.length_optimization_history),
            'pattern_attempts_count': self.pattern_attempts_count,
            'next_optimization_in': self.optimization_frequency - (self.pattern_attempts_count % self.optimization_frequency)
        }

    def get_pattern_success_rate(self, pattern: Dict) -> float:
        """Obtient taux de succès d'un pattern"""
        pattern_key = self._get_pattern_key(pattern)

        successes = self.successful_patterns.get(pattern_key, 0)
        failures = self.failed_patterns.get(pattern_key, 0)
        total = successes + failures

        return successes / total if total > 0 else 0.5

    # ═══════════════════════════════════════════════════════════════════
    # MÉTHODES AZR AUTHENTIQUE - PROPOSER OPTIMISÉ
    # ═══════════════════════════════════════════════════════════════════

    def _generate_conditioned_sequence(self) -> List[int]:
        """Génère séquence conditionnée sur K exemples passés du buffer"""
        try:
            # Si buffer vide, génération aléatoire
            if not self.task_buffer:
                return self._generate_random_sequence()

            # Analyse patterns dans buffer
            buffer_patterns = self._analyze_buffer_patterns()

            # Génération séquence différente des exemples passés
            new_sequence = self._generate_diverse_sequence(buffer_patterns)

            # Exploitation non-indépendance si activée
            if self.sequence_dependency_modeling:
                new_sequence = self._apply_dependency_modeling(new_sequence)

            return new_sequence

        except Exception as e:
            logger.error(f"Erreur génération séquence conditionnée: {e}")
            return self._generate_random_sequence()

    def _calculate_learnability_reward(self, sequence: List[int],
                                     solver_performance: float) -> float:
        """
        Calcule récompense learnability selon paradigme AZR

        Récompense basée sur difficulté prédictive optimale:
        - Trop facile (>0.6): faible récompense
        - Trop difficile (<0.4): faible récompense
        - Zone optimale [0.4, 0.6]: récompense élevée
        """
        try:
            # Estimation difficulté séquence
            sequence_complexity = self._estimate_sequence_complexity(sequence)

            # Estimation performance solver sur cette séquence
            estimated_solver_accuracy = self._estimate_solver_accuracy(
                sequence, solver_performance
            )

            # Calcul learnability selon zone optimale
            if self.learnability_min <= estimated_solver_accuracy <= self.learnability_max:
                # Zone optimale: récompense élevée
                learnability = 0.8 + 0.2 * sequence_complexity
            elif estimated_solver_accuracy > self.learnability_max:
                # Trop facile: récompense faible
                learnability = 0.3 * (1.0 - estimated_solver_accuracy)
            else:
                # Trop difficile: récompense faible
                learnability = 0.3 * estimated_solver_accuracy

            return max(0.0, min(1.0, learnability))

        except Exception as e:
            logger.error(f"Erreur calcul learnability: {e}")
            return 0.5  # Valeur par défaut

    def _generate_random_sequence(self) -> List[int]:
        """Génère séquence aléatoire pour initialisation"""
        length = np.random.randint(5, 15)  # Séquences 5-15 manches
        return [np.random.randint(0, 2) for _ in range(length)]

    def _analyze_buffer_patterns(self) -> Dict[str, Any]:
        """Analyse patterns dans buffer des tâches passées"""
        if not self.task_buffer:
            return {}

        patterns = {
            'common_lengths': [],
            'frequent_subsequences': [],
            'alternation_rates': [],
            'streak_patterns': []
        }

        for task in self.task_buffer:
            sequence = task['sequence']
            patterns['common_lengths'].append(len(sequence))
            patterns['alternation_rates'].append(self._calculate_alternation_score(sequence))

        return patterns

    def _generate_diverse_sequence(self, buffer_patterns: Dict) -> List[int]:
        """Génère séquence diverse par rapport aux patterns du buffer"""
        # Éviter longueurs communes
        common_lengths = buffer_patterns.get('common_lengths', [])
        if common_lengths:
            avg_length = int(np.mean(common_lengths))
            # Générer longueur différente ±3
            new_length = max(5, min(20, avg_length + np.random.randint(-3, 4)))
        else:
            new_length = np.random.randint(8, 15)

        # Génération avec diversité
        sequence = []
        for i in range(new_length):
            # Éviter patterns trop prévisibles
            if i == 0:
                sequence.append(np.random.randint(0, 2))
            else:
                # Logique anti-pattern pour diversité
                if len(sequence) >= 2 and sequence[-1] == sequence[-2]:
                    # Éviter séries trop longues
                    sequence.append(1 - sequence[-1])
                else:
                    sequence.append(np.random.randint(0, 2))

        return sequence

    def _apply_dependency_modeling(self, sequence: List[int]) -> List[int]:
        """Applique modélisation dépendances pour exploitation non-indépendance"""
        if not self.finite_state_exploitation:
            return sequence

        # Modélisation simple des dépendances
        modified_sequence = sequence.copy()

        for i in range(1, len(modified_sequence)):
            # Probabilité conditionnelle basée sur état précédent
            prev_state = modified_sequence[i-1]

            # Simulation dépendance simple (non-indépendance)
            if prev_state == 0:  # Player précédent
                # Légère tendance alternance
                if np.random.random() < 0.6:
                    modified_sequence[i] = 1  # Banker
            else:  # Banker précédent
                # Légère tendance alternance
                if np.random.random() < 0.6:
                    modified_sequence[i] = 0  # Player

        return modified_sequence

    def _estimate_sequence_complexity(self, sequence: List[int]) -> float:
        """Estime complexité d'une séquence"""
        if len(sequence) < 2:
            return 0.0

        # Facteurs de complexité
        alternation_score = self._calculate_alternation_score(sequence)
        length_factor = min(1.0, len(sequence) / 15.0)

        # Complexité basée sur imprévisibilité
        complexity = (alternation_score * 0.7 + length_factor * 0.3)
        return max(0.1, min(1.0, complexity))

    def _estimate_solver_accuracy(self, sequence: List[int],
                                 current_performance: float) -> float:
        """Estime accuracy du solver sur cette séquence"""
        # Estimation basée sur complexité et performance actuelle
        complexity = self._estimate_sequence_complexity(sequence)

        # Ajustement selon complexité
        if complexity < 0.3:
            # Séquence simple: performance élevée
            estimated_accuracy = min(0.9, current_performance + 0.2)
        elif complexity > 0.7:
            # Séquence complexe: performance réduite
            estimated_accuracy = max(0.1, current_performance - 0.3)
        else:
            # Séquence modérée: performance similaire
            estimated_accuracy = current_performance + np.random.uniform(-0.1, 0.1)

        return max(0.0, min(1.0, estimated_accuracy))

    def _generate_default_learning_task(self) -> Dict[str, Any]:
        """Génère tâche d'apprentissage par défaut en cas d'erreur"""
        return {
            'proposed_sequence': self._generate_random_sequence(),
            'learnability_reward': 0.5,
            'is_optimal': False,
            'proposer_accuracy': 0.0,
            'task_type': 'default_fallback'
        }

    def get_azr_proposer_metrics(self) -> Dict[str, Any]:
        """Obtient métriques du proposer AZR"""
        return {
            'total_proposals': self.total_proposals,
            'successful_proposals': self.successful_proposals,
            'proposer_accuracy': self.successful_proposals / max(1, self.total_proposals),
            'buffer_size': len(self.task_buffer),
            'avg_learnability': np.mean(self.learnability_scores) if self.learnability_scores else 0.0,
            'learnability_zone': f"[{self.learnability_min}, {self.learnability_max}]",
            'finite_state_exploitation': self.finite_state_exploitation,
            'sequence_dependency_modeling': self.sequence_dependency_modeling
        }
