# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 55 à 58
# Type: Méthode

    def f1_score(y_true, y_pred, average='binary', zero_division=0):
        prec = precision_score(y_true, y_pred, average, zero_division)
        rec = recall_score(y_true, y_pred, average, zero_division)
        return 2 * (prec * rec) / (prec + rec) if (prec + rec) > 0 else zero_division