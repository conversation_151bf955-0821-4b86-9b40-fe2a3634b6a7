# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\calculations.py
# Lignes: 577 à 595
# Type: Méthode de la classe UncertaintyCalculator

    def _calculate_aleatoric_uncertainty(self, individual_uncertainties: Dict[str, float],
                                       weights: Dict[str, float] = None) -> float:
        """Calcule incertitude aléatoire (moyenne pondérée des incertitudes individuelles)"""
        try:
            uncertainty_values = list(individual_uncertainties.values())

            if weights and len(weights) == len(uncertainty_values):
                # Moyenne pondérée
                weight_values = list(weights.values())
                aleatoric = np.average(uncertainty_values, weights=weight_values)
            else:
                # Moyenne simple
                aleatoric = np.mean(uncertainty_values)

            return np.clip(aleatoric, 0.0, 1.0)

        except Exception as e:
            logger.error(f"Erreur incertitude aléatoire: {e}")
            return 0.5