# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\parameters.py
# Lignes: 248 à 270
# Type: Méthode de la classe GlobalConfig

    def get_phase_parameters(self, round_number: int) -> dict:
        """Obtient paramètres spécifiques à la phase"""
        if round_number <= self.azr.warmup_phase_rounds:
            return {
                'exploration_rate': 0.8,
                'learning_rate': 0.2,
                'confidence_threshold': 0.2,
                'calibration_frequency': self.calibration.warmup_calibration_frequency
            }
        elif round_number <= self.azr.prediction_end_round:
            return {
                'exploration_rate': 0.3,
                'learning_rate': 0.15,
                'confidence_threshold': 0.4,
                'calibration_frequency': self.calibration.optimal_calibration_frequency
            }
        else:
            return {
                'exploration_rate': 0.1,
                'learning_rate': 0.1,
                'confidence_threshold': 0.6,
                'calibration_frequency': self.calibration.post_optimal_calibration_frequency
            }