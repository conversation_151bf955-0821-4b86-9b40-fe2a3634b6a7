"""
CONDITIONS STRATÉGIQUES POUR RECOMMANDATIONS WAIT - MODÈLE AZR
==============================================================

Conditions optimales pour que le modèle AZR fasse des recommandations WAIT
afin d'améliorer significativement la précision des prédictions NON-WAIT.

Basé sur recherches scientifiques et analyse du système existant.
"""

import numpy as np
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class WAITCondition:
    """Représente une condition pour recommandation WAIT"""
    name: str
    description: str
    weight: float
    threshold: float
    current_value: float = 0.0
    is_triggered: bool = False

class StrategicWAITDecisionEngine:
    """
    Moteur de décision WAIT stratégique pour modèle AZR

    OBJECTIF: Maximiser la précision des prédictions NON-WAIT
    en utilisant des recommandations WAIT conditionnelles et adaptatives
    """

    def __init__(self):
        # ═══════════════════════════════════════════════════════════════════
        # CONDITIONS STRATÉGIQUES WAIT (Basées sur recherches scientifiques)
        # ═══════════════════════════════════════════════════════════════════

        self.wait_conditions = {
            # 1. CONFIANCE INSUFFISANTE (Condition de base)
            'low_confidence': WAITCondition(
                name='low_confidence',
                description='Confiance globale insuffisante',
                weight=0.25,
                threshold=0.50  # Seuil ajusté pour confiances observées (40-50%)
            ),

            # 2. DÉSACCORD ENTRE MODÈLES (Variance épistémique élevée)
            'model_disagreement': WAITCondition(
                name='model_disagreement',
                description='Désaccord significatif entre AZR et ensemble',
                weight=0.20,
                threshold=0.15  # Variance épistémique > 15%
            ),

            # 3. INCERTITUDE DONNÉES ÉLEVÉE (Variance aléatoire)
            'high_data_uncertainty': WAITCondition(
                name='high_data_uncertainty',
                description='Incertitude des données trop élevée',
                weight=0.15,
                threshold=0.70  # Incertitude > 70%
            ),

            # 4. CONTEXTE DÉFAVORABLE (Streaks, volatilité)
            'unfavorable_context': WAITCondition(
                name='unfavorable_context',
                description='Contexte de jeu défavorable aux prédictions',
                weight=0.15,
                threshold=0.60  # Score contexte < 60%
            ),

            # 5. PERFORMANCE RÉCENTE DÉGRADÉE (Condition clé adaptative)
            'poor_recent_performance': WAITCondition(
                name='poor_recent_performance',
                description='Performance AZR récente en dessous des attentes',
                weight=0.30,  # Poids plus élevé pour réactivité
                threshold=0.50  # Performance < 50% (ajusté aux observations)
            ),

            # 6. PATTERNS CONTRADICTOIRES
            'conflicting_patterns': WAITCondition(
                name='conflicting_patterns',
                description='Patterns détectés se contredisent',
                weight=0.10,
                threshold=0.30  # Consensus patterns < 30%
            )
        }

        # ═══════════════════════════════════════════════════════════════════
        # PARAMÈTRES ADAPTATIFS
        # ═══════════════════════════════════════════════════════════════════

        self.adaptive_params = {
            'base_wait_threshold': 0.30,      # Seuil de base adaptatif
            'adaptation_rate': 0.10,          # Vitesse adaptation plus rapide
            'performance_window': 5,          # Fenêtre plus courte pour réactivité
            'min_wait_threshold': 0.05,       # Seuil minimum très bas
            'max_wait_threshold': 0.80,       # Seuil maximum plus élevé
            'volatility_factor': 1.5,         # Facteur ajustement volatilité
            'streak_penalty_factor': 2.0,     # Facteur pénalité streaks
            'performance_target': 0.55,       # Cible de performance AZR
            'adaptation_aggressiveness': 2.0  # Agressivité adaptation aux mauvaises performances
        }

        # Historique pour adaptation
        self.recent_predictions = []
        self.recent_accuracies = []
        self.wait_decisions_history = []
        self.context_history = []

    def should_wait(self, azr_prediction: Dict[str, Any],
                   ensemble_prediction: Dict[str, Any],
                   game_context: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        Détermine si le modèle AZR doit faire une recommandation WAIT

        Returns:
            Tuple[bool, Dict]: (should_wait, decision_details)
        """
        try:
            # ═══════════════════════════════════════════════════════════════════
            # ÉVALUATION CONDITIONS WAIT
            # ═══════════════════════════════════════════════════════════════════

            # 1. Confiance insuffisante
            self._evaluate_confidence_condition(azr_prediction, ensemble_prediction)

            # 2. Désaccord entre modèles
            self._evaluate_model_disagreement(azr_prediction, ensemble_prediction)

            # 3. Incertitude données
            self._evaluate_data_uncertainty(azr_prediction, ensemble_prediction)

            # 4. Contexte défavorable
            self._evaluate_game_context(game_context)

            # 5. Performance récente
            self._evaluate_recent_performance()

            # 6. Patterns contradictoires
            self._evaluate_pattern_conflicts(azr_prediction)

            # ═══════════════════════════════════════════════════════════════════
            # CALCUL SCORE WAIT PONDÉRÉ
            # ═══════════════════════════════════════════════════════════════════

            wait_score = self._calculate_weighted_wait_score()

            # ═══════════════════════════════════════════════════════════════════
            # DÉCISION FINALE ADAPTATIVE
            # ═══════════════════════════════════════════════════════════════════

            # Seuil adaptatif basé sur performance récente
            adaptive_threshold = self._calculate_adaptive_threshold()

            should_wait = wait_score > adaptive_threshold

            # Détails de la décision
            triggered_conditions = [cond.name for cond in self.wait_conditions.values() if cond.is_triggered]
            decision_details = {
                'wait_score': wait_score,
                'adaptive_threshold': adaptive_threshold,
                'triggered_conditions': triggered_conditions,
                'condition_values': {
                    name: cond.current_value
                    for name, cond in self.wait_conditions.items()
                },
                'reasoning': self._generate_wait_reasoning(should_wait, wait_score)
            }

            # LOGGING DÉTAILLÉ POUR DIAGNOSTIC
            logger.info(f"🔍 WAIT Decision: score={wait_score:.3f}, threshold={adaptive_threshold:.3f}, "
                       f"should_wait={should_wait}, triggered={triggered_conditions}")

            if len(self.recent_accuracies) > 0:
                recent_perf = np.mean(self.recent_accuracies[-5:]) if len(self.recent_accuracies) >= 5 else np.mean(self.recent_accuracies)
                logger.info(f"📊 Performance récente: {recent_perf:.3f}, historique: {len(self.recent_accuracies)} prédictions")

            # Mise à jour historique
            self._update_decision_history(should_wait, wait_score, decision_details)

            return should_wait, decision_details

        except Exception as e:
            logger.error(f"Erreur décision WAIT: {e}")
            return False, {'error': str(e)}

    def _evaluate_confidence_condition(self, azr_pred: Dict, ensemble_pred: Dict):
        """Évalue condition confiance insuffisante"""
        azr_conf = azr_pred.get('confidence', 0.5)
        ensemble_conf = ensemble_pred.get('confidence', 0.5) if ensemble_pred else 0.5

        # Confiance globale pondérée
        global_confidence = (azr_conf * 0.4 + ensemble_conf * 0.6)

        condition = self.wait_conditions['low_confidence']
        condition.current_value = global_confidence
        condition.is_triggered = global_confidence < condition.threshold

    def _evaluate_model_disagreement(self, azr_pred: Dict, ensemble_pred: Dict):
        """Évalue désaccord entre modèles (variance épistémique)"""
        if not ensemble_pred:
            # Pas d'ensemble disponible
            condition = self.wait_conditions['model_disagreement']
            condition.current_value = 0.0
            condition.is_triggered = False
            return

        azr_prob = azr_pred.get('player_probability', 0.5)
        ensemble_prob = ensemble_pred.get('player_probability', 0.5)

        # Variance épistémique (désaccord entre modèles)
        epistemic_variance = (azr_prob - ensemble_prob) ** 2

        condition = self.wait_conditions['model_disagreement']
        condition.current_value = epistemic_variance
        condition.is_triggered = epistemic_variance > condition.threshold

    def _evaluate_data_uncertainty(self, azr_pred: Dict, ensemble_pred: Dict):
        """Évalue incertitude des données (variance aléatoire)"""
        azr_uncertainty = azr_pred.get('uncertainty', 0.5)
        ensemble_uncertainty = ensemble_pred.get('uncertainty', 0.5) if ensemble_pred else 0.5

        # Incertitude moyenne pondérée
        avg_uncertainty = (azr_uncertainty * 0.4 + ensemble_uncertainty * 0.6)

        condition = self.wait_conditions['high_data_uncertainty']
        condition.current_value = avg_uncertainty
        condition.is_triggered = avg_uncertainty > condition.threshold

    def _evaluate_game_context(self, game_context: Dict):
        """Évalue contexte de jeu défavorable"""
        context_score = 1.0  # Score de base favorable

        # Facteurs contextuels défavorables
        current_streak = game_context.get('current_streak', 0)
        volatility = game_context.get('volatility', 0.0)
        alternation_rate = game_context.get('alternation_rate', 0.5)
        round_number = game_context.get('round_number', 30)

        # Pénalités contextuelles - STREAK SIGNIFICATIVE À PARTIR DE 4
        if abs(current_streak) >= 4:  # Streak significative (4+ répétitions)
            context_score *= 0.4  # Pénalité forte pour streaks longues

        if volatility > 0.8:  # Haute volatilité
            context_score *= 0.6

        if alternation_rate > 0.8 or alternation_rate < 0.2:  # Alternance extrême
            context_score *= 0.8

        if round_number < 35 or round_number > 55:  # Hors fenêtre optimale
            context_score *= 0.9

        condition = self.wait_conditions['unfavorable_context']
        condition.current_value = context_score
        condition.is_triggered = context_score < condition.threshold

    def _evaluate_recent_performance(self):
        """Évalue performance récente du modèle AZR avec déclenchement adaptatif"""
        condition = self.wait_conditions['poor_recent_performance']

        if len(self.recent_accuracies) < 3:
            # Pas assez d'historique : déclenchement prudent
            condition.current_value = 0.4  # Assume performance faible
            condition.is_triggered = True  # Déclenche WAIT par prudence
            return

        # Performance récente (dernières prédictions)
        window = self.adaptive_params['performance_window']
        recent_acc = self.recent_accuracies[-window:] if len(self.recent_accuracies) >= window else self.recent_accuracies
        avg_recent_accuracy = np.mean(recent_acc)

        # DÉCLENCHEMENT ADAPTATIF BASÉ SUR PERFORMANCE RÉELLE
        condition.current_value = avg_recent_accuracy

        # Seuil dynamique basé sur historique
        if len(self.recent_accuracies) >= 10:
            # Calcul seuil adaptatif basé sur performance globale
            global_accuracy = np.mean(self.recent_accuracies)
            adaptive_threshold = max(0.40, min(0.60, global_accuracy - 0.05))
        else:
            adaptive_threshold = condition.threshold

        # Déclenchement si performance récente < seuil adaptatif
        condition.is_triggered = avg_recent_accuracy < adaptive_threshold

        # DÉCLENCHEMENT FORCÉ si performance très mauvaise
        if avg_recent_accuracy < 0.35:  # Performance catastrophique
            condition.is_triggered = True
            condition.weight = 0.50  # Poids maximal

    def _evaluate_pattern_conflicts(self, azr_pred: Dict):
        """Évalue conflits entre patterns détectés"""
        pattern_breakdown = azr_pred.get('pattern_breakdown', {})
        reasoning_details = azr_pred.get('reasoning_details', {})

        # Consensus entre patterns
        pattern_consensus = reasoning_details.get('pattern_consensus', 0.5)

        condition = self.wait_conditions['conflicting_patterns']
        condition.current_value = pattern_consensus
        condition.is_triggered = pattern_consensus < condition.threshold

    def _calculate_weighted_wait_score(self) -> float:
        """Calcule score WAIT pondéré"""
        total_score = 0.0
        total_weight = 0.0

        for condition in self.wait_conditions.values():
            if condition.is_triggered:
                # Contribution proportionnelle à l'écart au seuil
                if condition.name == 'low_confidence':
                    contribution = (condition.threshold - condition.current_value) / condition.threshold
                elif condition.name == 'unfavorable_context' or condition.name == 'poor_recent_performance':
                    contribution = (condition.threshold - condition.current_value) / condition.threshold
                else:
                    contribution = (condition.current_value - condition.threshold) / (1.0 - condition.threshold)

                total_score += condition.weight * max(0.0, contribution)
                total_weight += condition.weight

        return total_score / max(0.1, total_weight) if total_weight > 0 else 0.0

    def _calculate_adaptive_threshold(self) -> float:
        """Calcule seuil adaptatif basé sur performance AZR réelle"""
        base_threshold = self.adaptive_params['base_wait_threshold']

        if len(self.recent_accuracies) < 3:
            # Pas assez d'historique : seuil bas pour être prudent
            return max(0.15, base_threshold * 0.7)

        # Performance récente AZR
        window = self.adaptive_params['performance_window']
        recent_performance = np.mean(self.recent_accuracies[-window:])
        target_performance = self.adaptive_params['performance_target']

        # Écart par rapport à la cible
        performance_gap = target_performance - recent_performance

        # ADAPTATION AGRESSIVE AUX MAUVAISES PERFORMANCES
        if performance_gap > 0.10:  # Performance très mauvaise (< 45%)
            # Seuil très bas = beaucoup de WAIT
            adjustment = -performance_gap * self.adaptive_params['adaptation_aggressiveness']
        elif performance_gap > 0.05:  # Performance mauvaise (45-50%)
            # Seuil bas = plus de WAIT
            adjustment = -performance_gap * 1.5
        elif performance_gap < -0.05:  # Performance excellente (> 60%)
            # Seuil plus haut = moins de WAIT
            adjustment = min(0.20, abs(performance_gap) * 1.0)
        else:
            # Performance acceptable : ajustement minimal
            adjustment = -performance_gap * 0.5

        # Facteur de volatilité : plus de WAIT si résultats instables
        if len(self.recent_accuracies) >= 5:
            volatility = np.std(self.recent_accuracies[-5:])
            volatility_adjustment = volatility * 0.3  # Plus de WAIT si instable
        else:
            volatility_adjustment = 0.0

        # Seuil final adaptatif
        adaptive_threshold = base_threshold + adjustment - volatility_adjustment

        # Application des limites
        final_threshold = np.clip(
            adaptive_threshold,
            self.adaptive_params['min_wait_threshold'],
            self.adaptive_params['max_wait_threshold']
        )

        return final_threshold

    def _generate_wait_reasoning(self, should_wait: bool, wait_score: float) -> str:
        """Génère explication de la décision WAIT"""
        if not should_wait:
            return "Conditions favorables - Prédiction recommandée"

        triggered = [cond.name for cond in self.wait_conditions.values() if cond.is_triggered]

        if 'low_confidence' in triggered:
            return f"WAIT - Confiance insuffisante (score: {wait_score:.3f})"
        elif 'model_disagreement' in triggered:
            return f"WAIT - Désaccord entre modèles (score: {wait_score:.3f})"
        elif 'unfavorable_context' in triggered:
            return f"WAIT - Contexte défavorable (score: {wait_score:.3f})"
        else:
            return f"WAIT - Conditions multiples défavorables (score: {wait_score:.3f})"

    def _update_decision_history(self, should_wait: bool, wait_score: float, details: Dict):
        """Met à jour historique des décisions"""
        self.wait_decisions_history.append({
            'should_wait': should_wait,
            'wait_score': wait_score,
            'details': details
        })

        # Limite taille historique
        if len(self.wait_decisions_history) > 100:
            self.wait_decisions_history = self.wait_decisions_history[-50:]

    def update_performance(self, prediction_correct: bool, actual_accuracy: float):
        """Met à jour performance pour adaptation"""
        self.recent_accuracies.append(1.0 if prediction_correct else 0.0)

        # Limite taille historique
        if len(self.recent_accuracies) > 50:
            self.recent_accuracies = self.recent_accuracies[-25:]

    def get_wait_statistics(self) -> Dict[str, Any]:
        """Retourne statistiques des décisions WAIT"""
        if not self.wait_decisions_history:
            return {'total_decisions': 0}

        total_decisions = len(self.wait_decisions_history)
        wait_decisions = sum(1 for d in self.wait_decisions_history if d['should_wait'])
        wait_rate = wait_decisions / total_decisions

        return {
            'total_decisions': total_decisions,
            'wait_decisions': wait_decisions,
            'wait_rate': wait_rate,
            'avg_wait_score': np.mean([d['wait_score'] for d in self.wait_decisions_history]),
            'current_thresholds': {name: cond.threshold for name, cond in self.wait_conditions.items()}
        }
