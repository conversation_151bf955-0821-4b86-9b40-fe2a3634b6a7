"""
AZR BACCARAT PREDICTOR - VERSION FLAT
=====================================

Module principal du paradigme Absolute Zero Reasoner pour Baccarat.
Tous les fichiers Python sont maintenant dans le même dossier.
"""

# Import des modules AZR principaux
try:
    from pattern_proposer import BaccaratPatternProposer
    from pattern_validator import BaccaratPatternValidator
    from adaptive_reasoner import BaccaratAdaptiveReasoner
    from self_play_engine import BaccaratSelfPlayEngine

    # Import des autres modules
    from models import AZRBaccaratPredictor
    from parameters import global_config
    from calculations import global_confidence_calculator
    from main_interface import BaccaratPredictorApp
    from new_game_handler import NewGameHandler

    __all__ = [
        'BaccaratPatternProposer',
        'BaccaratPatternValidator',
        'BaccaratAdaptiveReasoner',
        'BaccaratSelfPlayEngine',
        'AZRBaccaratPredictor',
        'BaccaratPredictorApp',
        'NewGameHandler',
        'global_config',
        'global_confidence_calculator'
    ]

except ImportError as e:
    print(f"Erreur import modules AZR: {e}")
    __all__ = []

__version__ = "1.0.0"
__author__ = "AZR Baccarat Team"
__description__ = "Absolute Zero Reasoner pour prédiction Baccarat - Version Flat"
