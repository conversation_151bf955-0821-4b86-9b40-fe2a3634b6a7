# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\parameters.py
# Lignes: 204 à 208
# Type: Méthode de la classe CalculationsConfig
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def __post_init__(self):
        if self.metric_names is None:
            self.metric_names = ['accuracy', 'precision', 'recall', 'f1_score']
        if self.probabilistic_metric_names is None:
            self.probabilistic_metric_names = ['log_loss', 'brier_score']