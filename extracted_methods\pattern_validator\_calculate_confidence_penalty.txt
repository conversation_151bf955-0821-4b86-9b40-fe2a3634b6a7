# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 371 à 378
# Type: Méthode de la classe BaccaratPatternValidator

    def _calculate_confidence_penalty(self, predicted_confidence: float, success: bool) -> float:
        """Calcule pénalité basée sur confiance et résultat"""
        if success:
            # Pas de pénalité si succès
            return 0.0
        else:
            # Pénalité proportionnelle à la confiance erronée
            return predicted_confidence * 0.5