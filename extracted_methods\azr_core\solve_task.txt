# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 343 à 357
# Type: Méthode de la classe AZRSolver

    def solve_task(self, task: AZRTask) -> Optional[AZRSolution]:
        """Résout une tâche AZR"""
        try:
            if task.task_type == 'pattern_prediction':
                return self._solve_pattern_task(task)
            elif task.task_type == 'sequence_analysis':
                return self._solve_sequence_task(task)
            elif task.task_type == 'trend_detection':
                return self._solve_trend_task(task)
            else:
                return None

        except Exception as e:
            logger.error(f"Erreur résolution tâche: {e}")
            return None