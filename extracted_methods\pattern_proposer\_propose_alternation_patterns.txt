# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 222 à 247
# Type: Méthode de la classe BaccaratPatternProposer

    def _propose_alternation_patterns(self, results: List[int]) -> List[Dict]:
        """Propose patterns d'alternance"""
        patterns = []

        if len(results) < 4:
            return patterns

        # Détection alternances parfaites et quasi-parfaites
        for start_idx in range(len(results) - 3):
            for length in range(4, min(len(results) - start_idx + 1, 13)):
                segment = results[start_idx:start_idx + length]

                alternation_score = self._calculate_alternation_score(segment)

                if alternation_score > 0.7:  # Seuil alternance
                    patterns.append({
                        'type': 'alternation',
                        'pattern': 'alternating',
                        'segment': segment,
                        'length': length,
                        'alternation_score': alternation_score,
                        'confidence': alternation_score,
                        'start_with': segment[0]
                    })

        return sorted(patterns, key=lambda x: x['confidence'], reverse=True)[:3]