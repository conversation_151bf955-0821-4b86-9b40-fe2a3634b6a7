"""
ADAPTIVE REASONER - AZR CORE
=============================

Raisonnement adaptatif pour Baccarat sans entraînement préalable.
Basé sur le paradigme Absolute Zero Reasoner.
"""

import numpy as np
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import time
from collections import defaultdict, deque

# Import modules AZR (maintenant dans le même dossier)
from pattern_proposer import BaccaratPatternProposer
from pattern_validator import BaccaratPatternValidator

# Import configuration centralisée (maintenant dans le même dossier)
from parameters import global_config

# Import calculs centralisés (avec gestion d'erreur)
try:
    from calculations import global_confidence_calculator, global_uncertainty_calculator, global_ensemble_calculator
except ImportError:
    # Fallback si modules calculs non disponibles
    global_confidence_calculator = None
    global_uncertainty_calculator = None
    global_ensemble_calculator = None

# Configuration logging
logger = logging.getLogger(__name__)

class BaccaratAdaptiveReasoner:
    """
    Raisonneur adaptatif pour Baccarat - Paradigme AZR

    Combine proposition et validation de patterns pour raisonnement
    adaptatif en temps réel sans entraînement préalable.
    """

    def __init__(self):
        """Initialisation du raisonneur adaptatif"""
        self.pattern_proposer = BaccaratPatternProposer()
        self.pattern_validator = BaccaratPatternValidator()

        # Historique et état
        self.game_history = deque(maxlen=global_config.azr.max_game_history)
        self.successful_patterns = []
        self.failed_patterns = []
        self.active_patterns = {}

        # Métriques performance
        self.reasoning_sessions = 0
        self.successful_predictions = 0
        self.total_predictions = 0
        self.confidence_history = deque(maxlen=100)

        # Paramètres adaptatifs
        self.adaptation_rate = global_config.azr.adaptation_rate
        self.confidence_threshold = global_config.azr.confidence_threshold
        self.pattern_decay_rate = global_config.azr.pattern_decay_rate

        # ═══════════════════════════════════════════════════════════════════
        # OPTIMISATION MÉTA-PARAMÈTRES EN TEMPS RÉEL
        # ═══════════════════════════════════════════════════════════════════

        # Paramètres adaptatifs (optimisés en temps réel)
        self.adaptive_learning_rate = global_config.azr.learning_rate
        self.adaptive_confidence_threshold = global_config.azr.confidence_threshold
        self.adaptive_exploration_rate = global_config.azr.exploration_rate

        # Historique performance pour optimisation
        self.parameter_performance_history = []
        self.parameter_optimization_window = 20  # Fenêtre optimisation
        self.last_optimization_round = 0
        self.optimization_frequency = 10  # Optimise tous les 10 rounds

        # Plages d'optimisation
        self.learning_rate_range = (0.05, 0.25)
        self.confidence_threshold_range = (0.2, 0.6)
        self.exploration_rate_range = (0.1, 0.5)

        # Métriques optimisation
        self.current_parameter_set = {
            'learning_rate': self.adaptive_learning_rate,
            'confidence_threshold': self.adaptive_confidence_threshold,
            'exploration_rate': self.adaptive_exploration_rate
        }
        self.parameter_performance_score = 0.5

        # Nouveaux paramètres AZR optimaux selon recherches
        self.proposer_solver_balance = global_config.azr.proposer_solver_balance
        self.self_play_iterations = global_config.azr.self_play_iterations
        self.finite_state_exploitation = global_config.azr.finite_state_exploitation

        # Métriques self-play AZR
        self.self_play_sessions = 0
        self.proposer_rewards = deque(maxlen=100)
        self.solver_rewards = deque(maxlen=100)
        self.learnability_history = deque(maxlen=100)

        # Performance tracking pour proposer
        self.current_solver_performance = 0.5  # Performance initiale
        self.solver_performance_history = deque(maxlen=50)

        logger.info("BaccaratAdaptiveReasoner initialisé avec paradigme AZR authentique + Self-play optimisé")


# Alias pour compatibilité
class AdaptiveReasoner(BaccaratAdaptiveReasoner):
    """Alias pour BaccaratAdaptiveReasoner pour compatibilité imports"""

    def predict_next_outcome(self, history: List[int]) -> Dict[str, Any]:
        """Interface simplifiée pour prédiction"""
        return self.reason_next_outcome(history, len(history) + 1)

    def reason_next_outcome(self, game_history: List[int],
                          current_round: int = None) -> Dict[str, Any]:
        """
        Raisonne sur prochaine issue sans modèle pré-entraîné
        CALIBRATION OPTIMALE: Fenêtre prédiction manches 31-60

        Args:
            game_history: Historique complet du jeu
            current_round: Numéro de manche actuel

        Returns:
            Dict contenant prédiction, confiance et raisonnement
        """
        try:
            reasoning_start = time.time()
            self.reasoning_sessions += 1

            # ═══════════════════════════════════════════════════════════════════
            # VÉRIFICATION FENÊTRE PRÉDICTION OPTIMALE (31-60)
            # ═══════════════════════════════════════════════════════════════════

            # Vérification si dans fenêtre de prédiction optimale
            if current_round is not None:
                if current_round < global_config.azr.prediction_start_round:
                    # Avant manche 31 - Pas de prédiction, seulement observation
                    return self._create_observation_mode_result(game_history, current_round,
                                                              "Observation - Avant fenêtre prédiction optimale")

                elif current_round > global_config.azr.prediction_end_round:
                    # Après manche 60 - Prédiction avec confiance réduite
                    return self._create_post_optimal_window_result(game_history, current_round,
                                                                 "Post-fenêtre optimale - Confiance réduite")

            # Vérification historique minimum
            if len(game_history) < global_config.azr.min_history_for_prediction:
                return self._create_insufficient_history_result(game_history, current_round)

            # ═══════════════════════════════════════════════════════════════════
            # TRAITEMENT FENÊTRE OPTIMALE (31-60)
            # ═══════════════════════════════════════════════════════════════════

            # Mise à jour historique avec fenêtre optimisée
            optimal_history_length = global_config.azr.max_history_length
            self.game_history.extend(game_history[-optimal_history_length:])

            # Fenêtre d'analyse adaptée à la position dans le jeu
            analysis_window = self._calculate_optimal_analysis_window(current_round, len(game_history))
            recent_history = list(self.game_history)[-analysis_window:]

            # 1. Proposition de nouveaux patterns avec calibration
            proposed_patterns = self.pattern_proposer.propose_patterns(recent_history)

            # 2. Combinaison avec patterns validés
            all_patterns = self._combine_patterns(proposed_patterns, self.successful_patterns)

            # 3. Validation et scoring des patterns
            validated_patterns = self._validate_and_score_patterns(all_patterns, recent_history)

            # 4. Calcul confiance adaptative
            confidence_scores = self._calculate_adaptive_confidence(validated_patterns)

            # 5. Prédiction finale par ensemble
            ensemble_prediction = self._make_ensemble_prediction(validated_patterns, confidence_scores)

            # 6. Calcul incertitude
            uncertainty = self._calculate_prediction_uncertainty(validated_patterns, confidence_scores)

            # 7. Génération recommandation
            recommendation = self._generate_recommendation(ensemble_prediction, confidence_scores, uncertainty)

            reasoning_time = time.time() - reasoning_start

            # Résultat final
            result = {
                'prediction': ensemble_prediction,
                'confidence': confidence_scores['overall'],
                'uncertainty': uncertainty,
                'recommendation': recommendation,
                'reasoning_details': {
                    'patterns_used': len(validated_patterns),
                    'pattern_types': list(set(p['type'] for p in validated_patterns)),
                    'reasoning_time': reasoning_time,
                    'session_number': self.reasoning_sessions,
                    'active_patterns': len(self.active_patterns)
                },
                'pattern_breakdown': self._create_pattern_breakdown(validated_patterns),
                'meta_info': {
                    'round': current_round,
                    'history_length': len(recent_history),
                    'total_predictions': self.total_predictions,
                    'session_accuracy': self._calculate_session_accuracy()
                }
            }

            # Mise à jour historique confiance
            self.confidence_history.append(confidence_scores['overall'])

            logger.info(f"Raisonnement AZR - Confiance: {confidence_scores['overall']:.3f}, "
                       f"Patterns: {len(validated_patterns)}, Temps: {reasoning_time:.3f}s")

            return result

        except Exception as e:
            logger.error(f"Erreur raisonnement adaptatif: {e}")
            return self._create_fallback_reasoning(game_history)

    def _combine_patterns(self, proposed_patterns: Dict[str, List[Dict]],
                         successful_patterns: List[Dict]) -> List[Dict]:
        """Combine patterns proposés avec patterns validés"""
        all_patterns = []

        # Ajout patterns proposés
        for pattern_type, patterns in proposed_patterns.items():
            all_patterns.extend(patterns)

        # Ajout patterns validés récents (avec decay)
        for pattern in successful_patterns[-10:]:  # 10 derniers patterns validés
            # Application decay temporel
            age_factor = self._calculate_pattern_age_factor(pattern)
            if age_factor > 0.3:  # Seuil de pertinence
                pattern_copy = pattern.copy()
                pattern_copy['confidence'] *= age_factor
                all_patterns.append(pattern_copy)

        return all_patterns

    def _validate_and_score_patterns(self, patterns: List[Dict],
                                   recent_history: List[int]) -> List[Dict]:
        """Valide et score les patterns"""
        validated_patterns = []

        for pattern in patterns:
            try:
                # Validation du pattern
                validation_result = self.pattern_validator.validate_pattern(
                    pattern, recent_history
                )

                # Ajout informations de validation
                pattern_with_validation = pattern.copy()
                pattern_with_validation.update({
                    'validation_result': validation_result,
                    'adjusted_confidence': validation_result.get('confidence_adjusted', pattern.get('confidence', 0.5)),
                    'reliability': validation_result.get('pattern_reliability', 0.5),
                    'reward': validation_result.get('reward', 0.0)
                })

                # Filtrage par confiance minimale
                if pattern_with_validation['adjusted_confidence'] > self.confidence_threshold:
                    validated_patterns.append(pattern_with_validation)

            except Exception as e:
                logger.warning(f"Erreur validation pattern {pattern.get('type', 'unknown')}: {e}")
                continue

        # Tri par confiance ajustée
        validated_patterns.sort(key=lambda x: x['adjusted_confidence'], reverse=True)

        # Limitation nombre de patterns
        max_patterns = global_config.azr.max_active_patterns
        return validated_patterns[:max_patterns]

    def _calculate_adaptive_confidence(self, validated_patterns: List[Dict]) -> Dict[str, float]:
        """Calcule confiance adaptative basée sur patterns validés"""
        if not validated_patterns:
            return {'overall': 0.5, 'pattern_consensus': 0.0, 'reliability_factor': 0.0}

        # Confiances individuelles
        individual_confidences = [p['adjusted_confidence'] for p in validated_patterns]

        # Consensus entre patterns
        predictions = []
        for pattern in validated_patterns:
            validation = pattern.get('validation_result', {})
            prediction_details = validation.get('prediction_details', {})
            predicted_outcome = prediction_details.get('predicted_outcome')
            if predicted_outcome is not None:
                predictions.append(predicted_outcome)

        # Calcul consensus
        if predictions:
            player_votes = predictions.count(0)
            banker_votes = predictions.count(1)
            total_votes = len(predictions)

            consensus_strength = abs(player_votes - banker_votes) / total_votes
            pattern_consensus = consensus_strength
        else:
            pattern_consensus = 0.0

        # Facteur fiabilité basé sur historique
        reliability_scores = [p.get('reliability', 0.5) for p in validated_patterns]
        reliability_factor = np.mean(reliability_scores) if reliability_scores else 0.5

        # Confiance globale avec formule unifiée
        base_confidence = np.mean(individual_confidences)

        # Utilisation formule unifiée pour calcul final (avec fallback)
        if global_confidence_calculator:
            unified_confidence = global_confidence_calculator.calculate_ensemble_confidence(
                model_confidences={'azr_patterns': base_confidence},
                consensus_factor=pattern_consensus,
                performance_history=[reliability_factor]
            )
        else:
            # Fallback: calcul confiance simple
            unified_confidence = base_confidence * (0.5 + 0.5 * pattern_consensus) * reliability_factor

        return {
            'overall': unified_confidence,
            'pattern_consensus': pattern_consensus,
            'reliability_factor': reliability_factor,
            'base_confidence': base_confidence,
            'individual_confidences': individual_confidences
        }

    def _make_ensemble_prediction(self, validated_patterns: List[Dict],
                                confidence_scores: Dict[str, float]) -> Dict[str, Any]:
        """Génère prédiction d'ensemble"""
        if not validated_patterns:
            return {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,  # Par défaut Player
                'prediction_strength': 0.0
            }

        # Collecte prédictions avec pondération
        weighted_predictions = []
        total_weight = 0.0

        for pattern in validated_patterns:
            validation = pattern.get('validation_result', {})
            prediction_details = validation.get('prediction_details', {})
            predicted_outcome = prediction_details.get('predicted_outcome')

            if predicted_outcome is not None:
                weight = pattern['adjusted_confidence'] * pattern.get('reliability', 0.5)
                weighted_predictions.append((predicted_outcome, weight))
                total_weight += weight

        if not weighted_predictions:
            return {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,
                'prediction_strength': 0.0
            }

        # Calcul probabilités pondérées
        player_weight = sum(weight for outcome, weight in weighted_predictions if outcome == 0)
        banker_weight = sum(weight for outcome, weight in weighted_predictions if outcome == 1)

        if total_weight > 0:
            player_prob = player_weight / total_weight
            banker_prob = banker_weight / total_weight
        else:
            player_prob = banker_prob = 0.5

        # Prédiction finale
        predicted_outcome = 0 if player_prob > banker_prob else 1
        prediction_strength = abs(player_prob - banker_prob)

        return {
            'player_probability': player_prob,
            'banker_probability': banker_prob,
            'predicted_outcome': predicted_outcome,
            'prediction_strength': prediction_strength,
            'total_patterns': len(weighted_predictions)
        }

    def _calculate_prediction_uncertainty(self, validated_patterns: List[Dict],
                                        confidence_scores: Dict[str, float]) -> float:
        """Calcule incertitude de la prédiction"""
        if not validated_patterns:
            return 0.8  # Incertitude élevée sans patterns

        # Facteurs d'incertitude
        pattern_disagreement = 1.0 - confidence_scores.get('pattern_consensus', 0.0)
        reliability_uncertainty = 1.0 - confidence_scores.get('reliability_factor', 0.5)
        confidence_uncertainty = 1.0 - confidence_scores.get('base_confidence', 0.5)

        # Incertitude basée sur variance des prédictions
        individual_confidences = confidence_scores.get('individual_confidences', [0.5])
        confidence_variance = np.var(individual_confidences) if len(individual_confidences) > 1 else 0.0

        # Utilisation formule unifiée pour incertitude (avec fallback)
        if global_uncertainty_calculator:
            unified_uncertainty = global_uncertainty_calculator.calculate_ensemble_uncertainty(
                model_uncertainties={'azr_patterns': confidence_uncertainty},
                disagreement_factor=pattern_disagreement,
                variance_factor=confidence_variance
            )
        else:
            # Fallback: calcul incertitude simple
            unified_uncertainty = (pattern_disagreement + reliability_uncertainty + confidence_uncertainty) / 3.0

        return unified_uncertainty

    def _generate_binary_prediction_result(self, prediction: Dict[str, Any],
                                         confidence_scores: Dict[str, float],
                                         uncertainty: float) -> str:
        """
        Génère résultat prédiction binaire PURE (suppression logique WAIT)

        OBJECTIF UNIQUE: Prédire 0 (Player) ou 1 (Banker) avec confiance maximale
        SUPPRESSION TOTALE: Logique WAIT/TIE/Attendre
        """
        # ═══════════════════════════════════════════════════════════════════
        # PRÉDICTION BINAIRE PURE - SUPPRESSION LOGIQUE WAIT/TIE
        # ═══════════════════════════════════════════════════════════════════

        predicted_outcome = prediction.get('predicted_outcome', 0)
        overall_confidence = confidence_scores['overall']

        # Retour TOUJOURS une prédiction binaire (jamais WAIT/TIE)
        if predicted_outcome == 0:
            confidence_level = "élevée" if overall_confidence > 0.7 else "modérée" if overall_confidence > 0.5 else "faible"
            return f"Player - Confiance {confidence_level}"
        else:
            confidence_level = "élevée" if overall_confidence > 0.7 else "modérée" if overall_confidence > 0.5 else "faible"
            return f"Banker - Confiance {confidence_level}"

    def _create_pattern_breakdown(self, validated_patterns: List[Dict]) -> Dict[str, Any]:
        """Crée breakdown détaillé des patterns"""
        breakdown = {
            'total_patterns': len(validated_patterns),
            'by_type': defaultdict(int),
            'top_patterns': [],
            'average_confidence': 0.0,
            'consensus_level': 0.0
        }

        if not validated_patterns:
            return breakdown

        # Comptage par type
        for pattern in validated_patterns:
            breakdown['by_type'][pattern['type']] += 1

        # Top 3 patterns
        breakdown['top_patterns'] = [
            {
                'type': p['type'],
                'confidence': p['adjusted_confidence'],
                'reliability': p.get('reliability', 0.5)
            }
            for p in validated_patterns[:3]
        ]

        # Statistiques
        confidences = [p['adjusted_confidence'] for p in validated_patterns]
        breakdown['average_confidence'] = np.mean(confidences)

        # Niveau de consensus
        predictions = []
        for pattern in validated_patterns:
            validation = pattern.get('validation_result', {})
            prediction_details = validation.get('prediction_details', {})
            predicted_outcome = prediction_details.get('predicted_outcome')
            if predicted_outcome is not None:
                predictions.append(predicted_outcome)

        if predictions:
            player_votes = predictions.count(0)
            banker_votes = predictions.count(1)
            total_votes = len(predictions)
            breakdown['consensus_level'] = abs(player_votes - banker_votes) / total_votes

        return breakdown

    def _calculate_pattern_age_factor(self, pattern: Dict) -> float:
        """Calcule facteur d'âge pour un pattern"""
        # Simulation âge basée sur position dans historique
        # En réalité, utiliserait timestamp
        base_factor = 1.0
        decay_per_session = self.pattern_decay_rate

        # Approximation: patterns plus anciens ont facteur plus faible
        age_sessions = max(0, self.reasoning_sessions - pattern.get('session_created', self.reasoning_sessions))
        age_factor = base_factor * (1.0 - decay_per_session) ** age_sessions

        return max(0.1, age_factor)

    def _calculate_session_accuracy(self) -> float:
        """Calcule précision de la session"""
        if self.total_predictions == 0:
            return 0.0
        return self.successful_predictions / self.total_predictions

    def _create_fallback_reasoning(self, game_history: List[int]) -> Dict[str, Any]:
        """Crée raisonnement de fallback en cas d'erreur"""
        return {
            'prediction': {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,
                'prediction_strength': 0.0
            },
            'confidence': 0.5,
            'uncertainty': 0.8,
            'recommendation': "Attendre - Erreur système",
            'reasoning_details': {
                'patterns_used': 0,
                'pattern_types': [],
                'reasoning_time': 0.0,
                'session_number': self.reasoning_sessions,
                'error': True
            },
            'pattern_breakdown': {'total_patterns': 0},
            'meta_info': {
                'round': None,
                'history_length': len(game_history),
                'total_predictions': self.total_predictions,
                'session_accuracy': 0.0
            }
        }

    def update_with_outcome(self, predicted_outcome: int, actual_outcome: int,
                          patterns_used: List[Dict]):
        """Met à jour avec résultat réel"""
        self.total_predictions += 1

        if predicted_outcome == actual_outcome:
            self.successful_predictions += 1

            # Mise à jour patterns réussis
            for pattern in patterns_used:
                pattern['session_created'] = self.reasoning_sessions
                self.successful_patterns.append(pattern)
                self.pattern_proposer.update_pattern_performance(pattern, True)
        else:
            # Mise à jour patterns échoués
            for pattern in patterns_used:
                self.failed_patterns.append(pattern)
                self.pattern_proposer.update_pattern_performance(pattern, False)

        # Nettoyage patterns anciens
        self._cleanup_old_patterns()

        # ═══════════════════════════════════════════════════════════════════
        # OPTIMISATION MÉTA-PARAMÈTRES EN TEMPS RÉEL
        # ═══════════════════════════════════════════════════════════════════

        # Enregistrement performance pour optimisation
        current_accuracy = self._calculate_session_accuracy()
        self._record_parameter_performance(current_accuracy)

        # Optimisation périodique des méta-paramètres
        if self.total_predictions - self.last_optimization_round >= self.optimization_frequency:
            self._optimize_meta_parameters()
            self.last_optimization_round = self.total_predictions

    def _cleanup_old_patterns(self):
        """Nettoie patterns anciens"""
        max_successful = global_config.azr.max_successful_patterns
        max_failed = global_config.azr.max_failed_patterns

        if len(self.successful_patterns) > max_successful:
            self.successful_patterns = self.successful_patterns[-max_successful:]

        if len(self.failed_patterns) > max_failed:
            self.failed_patterns = self.failed_patterns[-max_failed:]

    def _calculate_optimal_analysis_window(self, current_round: int, history_length: int) -> int:
        """Calcule fenêtre d'analyse optimale selon position dans jeu"""
        if current_round is None:
            return global_config.azr.pattern_detection_window

        # Fenêtre adaptative selon position
        if current_round <= 35:  # Début fenêtre optimale
            return min(global_config.azr.pattern_detection_window, history_length)
        elif current_round <= 50:  # Milieu fenêtre optimale
            return min(global_config.azr.confidence_calculation_window, history_length)
        else:  # Fin fenêtre optimale
            return min(global_config.azr.pattern_validation_window, history_length)

    def _create_observation_mode_result(self, game_history: List[int],
                                      current_round: int, message: str) -> Dict[str, Any]:
        """Crée résultat en mode observation (avant manche 31)"""
        return {
            'prediction': {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,
                'prediction_strength': 0.0
            },
            'confidence': 0.0,  # Pas de confiance en mode observation
            'uncertainty': 1.0,  # Incertitude maximale
            'recommendation': f"Observer - Manche {current_round}/30 (Fenêtre optimale: 31-60)",
            'reasoning_details': {
                'mode': 'observation',
                'current_round': current_round,
                'optimal_window_start': global_config.azr.prediction_start_round,
                'patterns_detected': 0,
                'message': message
            },
            'pattern_breakdown': {'total_patterns': 0, 'observation_mode': True},
            'meta_info': {
                'round': current_round,
                'history_length': len(game_history),
                'in_optimal_window': False,
                'mode': 'observation'
            }
        }

    def _create_post_optimal_window_result(self, game_history: List[int],
                                         current_round: int, message: str) -> Dict[str, Any]:
        """Crée résultat après fenêtre optimale (après manche 60)"""
        # Prédiction avec confiance réduite
        recent_history = game_history[-15:]  # Historique très récent
        basic_patterns = self.pattern_proposer.propose_patterns(recent_history)

        # Prédiction simple basée sur tendance récente
        if len(recent_history) >= 5:
            recent_player = recent_history[-5:].count(0)
            recent_banker = recent_history[-5:].count(1)

            if recent_player > recent_banker:
                player_prob, banker_prob = 0.55, 0.45
                predicted_outcome = 0
            elif recent_banker > recent_player:
                player_prob, banker_prob = 0.45, 0.55
                predicted_outcome = 1
            else:
                player_prob, banker_prob = 0.5, 0.5
                predicted_outcome = 0
        else:
            player_prob, banker_prob = 0.5, 0.5
            predicted_outcome = 0

        # Confiance réduite après fenêtre optimale
        base_confidence = 0.3
        uncertainty = 0.7

        return {
            'prediction': {
                'player_probability': player_prob,
                'banker_probability': banker_prob,
                'predicted_outcome': predicted_outcome,
                'prediction_strength': abs(player_prob - banker_prob)
            },
            'confidence': base_confidence,
            'uncertainty': uncertainty,
            'recommendation': f"Prudence - Post-fenêtre optimale (Manche {current_round})",
            'reasoning_details': {
                'mode': 'post_optimal',
                'current_round': current_round,
                'optimal_window_end': global_config.azr.prediction_end_round,
                'confidence_reduction': 'Applied',
                'message': message
            },
            'pattern_breakdown': {
                'total_patterns': len(basic_patterns.get('trend', [])),
                'post_optimal_mode': True
            },
            'meta_info': {
                'round': current_round,
                'history_length': len(game_history),
                'in_optimal_window': False,
                'mode': 'post_optimal'
            }
        }

    def _create_insufficient_history_result(self, game_history: List[int],
                                          current_round: int) -> Dict[str, Any]:
        """Crée résultat pour historique insuffisant"""
        required = global_config.azr.min_history_for_prediction
        current = len(game_history)

        return {
            'prediction': {
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'predicted_outcome': 0,
                'prediction_strength': 0.0
            },
            'confidence': 0.0,
            'uncertainty': 1.0,
            'recommendation': f"Attendre - Historique insuffisant ({current}/{required})",
            'reasoning_details': {
                'mode': 'insufficient_history',
                'current_round': current_round,
                'history_required': required,
                'history_available': current,
                'message': 'Historique insuffisant pour prédiction fiable'
            },
            'pattern_breakdown': {'total_patterns': 0, 'insufficient_history': True},
            'meta_info': {
                'round': current_round,
                'history_length': current,
                'in_optimal_window': False,
                'mode': 'insufficient_history'
            }
        }

    def get_reasoning_statistics(self) -> Dict[str, Any]:
        """Obtient statistiques de raisonnement"""
        return {
            'reasoning_sessions': self.reasoning_sessions,
            'total_predictions': self.total_predictions,
            'successful_predictions': self.successful_predictions,
            'session_accuracy': self._calculate_session_accuracy(),
            'successful_patterns_count': len(self.successful_patterns),
            'failed_patterns_count': len(self.failed_patterns),
            'average_confidence': np.mean(list(self.confidence_history)) if self.confidence_history else 0.5,
            'confidence_trend': list(self.confidence_history)[-10:],  # 10 dernières
            'validator_stats': self.pattern_validator.get_validation_statistics(),
            # Nouvelles statistiques fenêtre optimale
            'optimal_window_config': {
                'start_round': global_config.azr.prediction_start_round,
                'end_round': global_config.azr.prediction_end_round,
                'min_history': global_config.azr.min_history_for_prediction
            },
            # Statistiques optimisation méta-paramètres
            'meta_parameter_optimization': {
                'current_parameters': self.current_parameter_set.copy(),
                'parameter_performance_score': self.parameter_performance_score,
                'optimization_history_length': len(self.parameter_performance_history),
                'last_optimization_round': self.last_optimization_round
            }
        }

    def _record_parameter_performance(self, accuracy: float):
        """Enregistre performance des paramètres actuels"""
        performance_entry = {
            'timestamp': time.time(),
            'accuracy': accuracy,
            'parameters': self.current_parameter_set.copy(),
            'total_predictions': self.total_predictions
        }

        self.parameter_performance_history.append(performance_entry)

        # Limitation taille historique
        if len(self.parameter_performance_history) > self.parameter_optimization_window * 2:
            self.parameter_performance_history = self.parameter_performance_history[-self.parameter_optimization_window * 2:]

    def _optimize_meta_parameters(self):
        """Optimise méta-paramètres en temps réel"""
        try:
            if len(self.parameter_performance_history) < self.parameter_optimization_window:
                return  # Pas assez d'historique

            # Analyse performance récente
            recent_performance = self.parameter_performance_history[-self.parameter_optimization_window:]
            current_avg_accuracy = np.mean([p['accuracy'] for p in recent_performance])

            # Comparaison avec performance précédente
            if len(self.parameter_performance_history) >= self.parameter_optimization_window * 2:
                previous_performance = self.parameter_performance_history[-self.parameter_optimization_window * 2:-self.parameter_optimization_window]
                previous_avg_accuracy = np.mean([p['accuracy'] for p in previous_performance])

                performance_trend = current_avg_accuracy - previous_avg_accuracy
            else:
                performance_trend = 0.0

            # Optimisation adaptative basée sur performance
            if performance_trend < -0.05:  # Performance dégradée
                self._adjust_parameters_for_improvement()
                logger.info(f"Optimisation méta-paramètres: Performance dégradée ({performance_trend:.3f}), ajustement appliqué")
            elif performance_trend > 0.05:  # Performance améliorée
                self._reinforce_current_parameters()
                logger.info(f"Optimisation méta-paramètres: Performance améliorée ({performance_trend:.3f}), paramètres renforcés")
            else:  # Performance stable
                self._explore_parameter_space()
                logger.debug("Optimisation méta-paramètres: Performance stable, exploration légère")

            # Mise à jour score performance
            self.parameter_performance_score = current_avg_accuracy

        except Exception as e:
            logger.error(f"Erreur optimisation méta-paramètres: {e}")

    def _adjust_parameters_for_improvement(self):
        """Ajuste paramètres pour améliorer performance"""
        # Réduction learning rate si performance dégradée
        self.adaptive_learning_rate *= 0.9
        self.adaptive_learning_rate = max(self.adaptive_learning_rate, self.learning_rate_range[0])

        # Augmentation seuil confiance (plus conservateur)
        self.adaptive_confidence_threshold *= 1.1
        self.adaptive_confidence_threshold = min(self.adaptive_confidence_threshold, self.confidence_threshold_range[1])

        # Réduction exploration (plus exploitation)
        self.adaptive_exploration_rate *= 0.85
        self.adaptive_exploration_rate = max(self.adaptive_exploration_rate, self.exploration_rate_range[0])

        self._update_current_parameter_set()

    def _reinforce_current_parameters(self):
        """Renforce paramètres actuels (performance améliorée)"""
        # Légère augmentation learning rate
        self.adaptive_learning_rate *= 1.05
        self.adaptive_learning_rate = min(self.adaptive_learning_rate, self.learning_rate_range[1])

        # Légère réduction seuil confiance (moins conservateur)
        self.adaptive_confidence_threshold *= 0.95
        self.adaptive_confidence_threshold = max(self.adaptive_confidence_threshold, self.confidence_threshold_range[0])

        self._update_current_parameter_set()

    def _explore_parameter_space(self):
        """Exploration légère de l'espace des paramètres"""
        # Petites variations aléatoires pour exploration
        learning_rate_noise = np.random.normal(0, 0.01)
        confidence_threshold_noise = np.random.normal(0, 0.02)
        exploration_rate_noise = np.random.normal(0, 0.02)

        # Application avec contraintes
        self.adaptive_learning_rate += learning_rate_noise
        self.adaptive_learning_rate = np.clip(self.adaptive_learning_rate,
                                            self.learning_rate_range[0],
                                            self.learning_rate_range[1])

        self.adaptive_confidence_threshold += confidence_threshold_noise
        self.adaptive_confidence_threshold = np.clip(self.adaptive_confidence_threshold,
                                                   self.confidence_threshold_range[0],
                                                   self.confidence_threshold_range[1])

        self.adaptive_exploration_rate += exploration_rate_noise
        self.adaptive_exploration_rate = np.clip(self.adaptive_exploration_rate,
                                                self.exploration_rate_range[0],
                                                self.exploration_rate_range[1])

        self._update_current_parameter_set()

    def _update_current_parameter_set(self):
        """Met à jour ensemble de paramètres actuels"""
        self.current_parameter_set = {
            'learning_rate': self.adaptive_learning_rate,
            'confidence_threshold': self.adaptive_confidence_threshold,
            'exploration_rate': self.adaptive_exploration_rate
        }

        # Mise à jour paramètres utilisés dans le système
        self.confidence_threshold = self.adaptive_confidence_threshold

    # ═══════════════════════════════════════════════════════════════════
    # MÉTHODES AZR AUTHENTIQUE - SELF-PLAY OPTIMISÉ
    # ═══════════════════════════════════════════════════════════════════

    def execute_azr_self_play_iteration(self) -> Dict[str, Any]:
        """
        Exécute une itération de self-play AZR authentique

        Implémente le cycle complet Proposer → Environment → Solver
        selon le paradigme Absolute Zero Reasoner optimisé.

        Returns:
            Dict contenant résultats de l'itération self-play
        """
        try:
            self.self_play_sessions += 1

            # ÉTAPE 1: PROPOSER - Génération tâche d'apprentissage
            proposer_result = self.pattern_proposer.propose_learning_sequences(
                self.current_solver_performance
            )

            proposed_sequence = proposer_result['proposed_sequence']
            learnability_reward = proposer_result['learnability_reward']

            # ÉTAPE 2: ENVIRONMENT - Validation et transformation
            environment_result = self._azr_environment_validation(
                proposed_sequence, learnability_reward
            )

            # ÉTAPE 3: SOLVER - Résolution de la tâche proposée
            solver_result = self._azr_solver_execution(
                environment_result['validated_sequence']
            )

            solver_reward = solver_result['accuracy']

            # ÉTAPE 4: MISE À JOUR - Calcul récompenses combinées
            combined_reward = self._calculate_combined_azr_reward(
                learnability_reward, solver_reward
            )

            # Mise à jour métriques
            self.proposer_rewards.append(learnability_reward)
            self.solver_rewards.append(solver_reward)
            self.learnability_history.append(learnability_reward)

            # Mise à jour performance solver
            self._update_solver_performance(solver_reward)

            iteration_result = {
                'iteration': self.self_play_sessions,
                'proposer_reward': learnability_reward,
                'solver_reward': solver_reward,
                'combined_reward': combined_reward,
                'proposed_sequence': proposed_sequence,
                'solver_accuracy': solver_result['accuracy'],
                'is_optimal_task': proposer_result['is_optimal'],
                'environment_validation': environment_result['is_valid'],
                'performance_improvement': self._calculate_performance_improvement()
            }

            logger.debug(f"Self-play AZR iteration {self.self_play_sessions}: "
                        f"Proposer={learnability_reward:.3f}, Solver={solver_reward:.3f}, "
                        f"Combined={combined_reward:.3f}")

            return iteration_result

        except Exception as e:
            logger.error(f"Erreur self-play AZR iteration: {e}")
            return self._generate_default_self_play_result()

    def _azr_environment_validation(self, sequence: List[int],
                                   learnability_reward: float) -> Dict[str, Any]:
        """
        Validation environnementale selon paradigme AZR

        Simule l'environment qui valide et transforme les tâches proposées
        en problèmes de prédiction vérifiables.
        """
        try:
            # Validation séquence selon règles Baccarat
            is_valid_sequence = self._validate_baccarat_sequence(sequence)

            # Transformation en tâche de prédiction
            if len(sequence) > 1:
                # Tâche: prédire dernier élément basé sur séquence[:-1]
                input_sequence = sequence[:-1]
                target_outcome = sequence[-1]

                validated_task = {
                    'input_sequence': input_sequence,
                    'target_outcome': target_outcome,
                    'task_type': 'sequence_prediction',
                    'difficulty': learnability_reward
                }
            else:
                # Séquence trop courte, génération tâche par défaut
                validated_task = {
                    'input_sequence': [0],
                    'target_outcome': 1,
                    'task_type': 'default_prediction',
                    'difficulty': 0.5
                }
                is_valid_sequence = False

            return {
                'validated_sequence': sequence,
                'validated_task': validated_task,
                'is_valid': is_valid_sequence,
                'environment_feedback': 'valid' if is_valid_sequence else 'invalid'
            }

        except Exception as e:
            logger.error(f"Erreur validation environment AZR: {e}")
            return {
                'validated_sequence': sequence,
                'validated_task': {'input_sequence': [0], 'target_outcome': 1},
                'is_valid': False,
                'environment_feedback': 'error'
            }

    def _azr_solver_execution(self, sequence: List[int]) -> Dict[str, Any]:
        """
        Exécution solver selon paradigme AZR

        Le solver tente de résoudre la tâche proposée et reçoit
        une récompense basée sur la performance.
        """
        try:
            if len(sequence) < 2:
                return {'accuracy': 0.0, 'prediction': 0, 'confidence': 0.0}

            # Utilisation du raisonnement adaptatif pour résoudre
            input_sequence = sequence[:-1]
            target_outcome = sequence[-1]

            # Génération prédiction avec le reasoner
            reasoning_result = self.reason_next_outcome(input_sequence)
            predicted_outcome = reasoning_result['prediction']['predicted_outcome']
            prediction_confidence = reasoning_result['confidence']

            # Calcul accuracy (récompense solver)
            is_correct = (predicted_outcome == target_outcome)
            accuracy = 1.0 if is_correct else 0.0

            # Bonus pour confiance calibrée
            if is_correct and prediction_confidence > 0.6:
                accuracy += 0.2  # Bonus confiance élevée + correct
            elif not is_correct and prediction_confidence < 0.4:
                accuracy += 0.1  # Bonus confiance faible + incorrect (bien calibré)

            accuracy = min(1.0, accuracy)  # Cap à 1.0

            return {
                'accuracy': accuracy,
                'prediction': predicted_outcome,
                'target': target_outcome,
                'confidence': prediction_confidence,
                'is_correct': is_correct,
                'calibration_bonus': accuracy > (1.0 if is_correct else 0.0)
            }

        except Exception as e:
            logger.error(f"Erreur solver AZR: {e}")
            return {'accuracy': 0.0, 'prediction': 0, 'confidence': 0.0}

    def _calculate_combined_azr_reward(self, proposer_reward: float,
                                      solver_reward: float) -> float:
        """
        Calcule récompense combinée selon paradigme AZR

        Utilise le paramètre λ (proposer_solver_balance) pour équilibrer
        les récompenses du proposer et du solver.
        """
        # Formule AZR: R_total = R_propose + λ * R_solve
        combined_reward = proposer_reward + self.proposer_solver_balance * solver_reward
        return max(0.0, min(2.0, combined_reward))  # Normalisation [0, 2]

    def _update_solver_performance(self, solver_reward: float):
        """Met à jour performance actuelle du solver"""
        self.solver_performance_history.append(solver_reward)

        # Calcul performance moyenne récente
        if len(self.solver_performance_history) >= 10:
            recent_performance = list(self.solver_performance_history)[-10:]
            self.current_solver_performance = np.mean(recent_performance)
        else:
            self.current_solver_performance = np.mean(list(self.solver_performance_history))

    def _calculate_performance_improvement(self) -> float:
        """Calcule amélioration de performance par rapport à baseline"""
        if len(self.solver_performance_history) < 20:
            return 0.0

        recent_performance = np.mean(list(self.solver_performance_history)[-10:])
        baseline_performance = np.mean(list(self.solver_performance_history)[:10])

        return recent_performance - baseline_performance

    def _validate_baccarat_sequence(self, sequence: List[int]) -> bool:
        """Valide qu'une séquence respecte les règles Baccarat"""
        if not sequence:
            return False

        # Vérification valeurs valides (0=Player, 1=Banker)
        for outcome in sequence:
            if outcome not in [0, 1]:
                return False

        # Vérification longueur raisonnable
        if len(sequence) < 2 or len(sequence) > 60:
            return False

        return True

    def _generate_default_self_play_result(self) -> Dict[str, Any]:
        """Génère résultat self-play par défaut en cas d'erreur"""
        return {
            'iteration': self.self_play_sessions,
            'proposer_reward': 0.0,
            'solver_reward': 0.0,
            'combined_reward': 0.0,
            'proposed_sequence': [0, 1],
            'solver_accuracy': 0.0,
            'is_optimal_task': False,
            'environment_validation': False,
            'performance_improvement': 0.0,
            'error': True
        }

    def get_azr_self_play_metrics(self) -> Dict[str, Any]:
        """Obtient métriques du self-play AZR"""
        return {
            'self_play_sessions': self.self_play_sessions,
            'current_solver_performance': self.current_solver_performance,
            'avg_proposer_reward': np.mean(self.proposer_rewards) if self.proposer_rewards else 0.0,
            'avg_solver_reward': np.mean(self.solver_rewards) if self.solver_rewards else 0.0,
            'avg_learnability': np.mean(self.learnability_history) if self.learnability_history else 0.0,
            'proposer_solver_balance': self.proposer_solver_balance,
            'performance_improvement': self._calculate_performance_improvement(),
            'finite_state_exploitation': self.finite_state_exploitation,
            'self_play_iterations_target': self.self_play_iterations
        }
