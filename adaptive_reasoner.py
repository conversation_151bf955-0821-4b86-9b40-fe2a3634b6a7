"""
ADAPTIVE REASONER - AZR CORE
=============================

Raisonnement adaptatif pour Baccarat sans entraînement préalable.
Basé sur le paradigme Absolute Zero Reasoner.
"""

import numpy as np
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import time
from collections import defaultdict, deque

# Import modules AZR (maintenant dans le même dossier) - AVEC GESTION CIRCULAIRE
try:
    from pattern_proposer import BaccaratPatternProposer
    PATTERN_PROPOSER_AVAILABLE = True
except ImportError:
    PATTERN_PROPOSER_AVAILABLE = False

try:
    from pattern_validator import BaccaratPatternValidator
    PATTERN_VALIDATOR_AVAILABLE = True
except ImportError:
    PATTERN_VALIDATOR_AVAILABLE = False

# Import configuration centralisée (maintenant dans le même dossier)
from parameters import global_config

# Import calculs centralisés (avec gestion d'erreur)
try:
    from calculations import global_confidence_calculator, global_uncertainty_calculator, global_ensemble_calculator
except ImportError:
    # Fallback si modules calculs non disponibles
    global_confidence_calculator = None
    global_uncertainty_calculator = None
    global_ensemble_calculator = None

# Configuration logging
logger = logging.getLogger(__name__)

class BaccaratAdaptiveReasoner:
    """
    Raisonneur adaptatif pour Baccarat - Paradigme AZR

    Combine proposition et validation de patterns pour raisonnement
    adaptatif en temps réel sans entraînement préalable.
    """

    def __init__(self):
        """Initialisation du raisonneur adaptatif"""
        # Initialisation conditionnelle pour éviter dépendances circulaires
        if PATTERN_PROPOSER_AVAILABLE:
            self.pattern_proposer = BaccaratPatternProposer()
        else:
            self.pattern_proposer = None

        if PATTERN_VALIDATOR_AVAILABLE:
            self.pattern_validator = BaccaratPatternValidator()
        else:
            self.pattern_validator = None

        # Historique et état
        self.game_history = deque(maxlen=global_config.azr.max_game_history)
        self.successful_patterns = []
        self.failed_patterns = []
        self.active_patterns = {}

        # Métriques performance
        self.reasoning_sessions = 0
        self.successful_predictions = 0
        self.total_predictions = 0
        self.confidence_history = deque(maxlen=100)

        # Paramètres adaptatifs
        self.adaptation_rate = global_config.azr.adaptation_rate
        self.confidence_threshold = global_config.azr.confidence_threshold
        self.pattern_decay_rate = global_config.azr.pattern_decay_rate

        # Fenêtre d'analyse optimale
        self.optimal_analysis_window = global_config.azr.optimal_analysis_window
        self.min_patterns_for_prediction = global_config.azr.min_patterns_for_prediction

        # Méta-paramètres pour optimisation continue
        self.meta_parameters = {
            'pattern_weight_adjustment': 0.0,
            'confidence_calibration': 0.0,
            'uncertainty_sensitivity': 1.0,
            'exploration_factor': global_config.azr.exploration_factor
        }

        # Historique performance pour méta-apprentissage
        self.parameter_performance_history = deque(maxlen=50)
        self.current_parameter_set = self.meta_parameters.copy()
        self.parameter_exploration_sessions = 0
        self.parameter_performance_score = 0.5

        # Nouveaux paramètres AZR optimaux selon recherches
        self.proposer_solver_balance = global_config.azr.proposer_solver_balance
        self.self_play_iterations = global_config.azr.self_play_iterations
        self.finite_state_exploitation = global_config.azr.finite_state_exploitation

        # Métriques self-play AZR
        self.self_play_sessions = 0
        self.proposer_rewards = deque(maxlen=100)
        self.solver_rewards = deque(maxlen=100)
        self.learnability_history = deque(maxlen=100)

        # Performance tracking pour proposer
        self.current_solver_performance = 0.5  # Performance initiale
        self.solver_performance_history = deque(maxlen=50)

        logger.info("BaccaratAdaptiveReasoner initialisé avec paradigme AZR authentique + Self-play optimisé")


# Alias pour compatibilité
class AdaptiveReasoner(BaccaratAdaptiveReasoner):
    """Alias pour BaccaratAdaptiveReasoner pour compatibilité imports"""

    def predict_next_outcome(self, history: List[int]) -> Dict[str, Any]:
        """Interface simplifiée pour prédiction"""
        return self.reason_next_outcome(history, len(history) + 1)

    def reason_next_outcome(self, game_history: List[int],
                          current_round: int = None) -> Dict[str, Any]:
        """
        Raisonne sur prochaine issue sans modèle pré-entraîné
        CALIBRATION OPTIMALE: Fenêtre prédiction manches 31-60

        Args:
            game_history: Historique complet du jeu
            current_round: Numéro de manche actuel

        Returns:
            Dict contenant prédiction, confiance, incertitude et détails
        """
        try:
            start_time = time.time()
            self.reasoning_sessions += 1

            # Vérification si dans fenêtre de prédiction optimale
            if current_round is not None:
                if current_round < global_config.azr.prediction_start_round:
                    # Avant manche 31 - Pas de prédiction, seulement observation
                    return self._create_observation_mode_result(game_history, current_round,
                                                              "Observation - Avant fenêtre prédiction optimale")

                elif current_round > global_config.azr.prediction_end_round:
                    # Après manche 60 - Prédiction avec confiance réduite
                    return self._create_post_optimal_window_result(game_history, current_round,
                                                                 "Post-fenêtre optimale - Confiance réduite")

            # Vérification historique minimum
            if len(game_history) < global_config.azr.min_history_for_prediction:
                return self._create_insufficient_history_result(game_history, current_round)

            # Calcul fenêtre d'analyse optimale
            analysis_window = self._calculate_optimal_analysis_window(game_history, current_round)
            recent_history = game_history[-analysis_window:] if analysis_window > 0 else game_history

            # 1. Proposition de patterns
            proposed_patterns = self.pattern_proposer.propose_patterns(recent_history)

            # 2. Combinaison avec patterns validés
            all_patterns = self._combine_patterns(proposed_patterns, self.successful_patterns)

            # 3. Validation et scoring des patterns
            validated_patterns = self._validate_and_score_patterns(all_patterns, recent_history)

            # 4. Calcul confiance adaptative
            confidence_scores = self._calculate_adaptive_confidence(validated_patterns)

            # 5. Prédiction finale par ensemble
            ensemble_prediction = self._make_ensemble_prediction(validated_patterns, confidence_scores)

            # 6. Calcul incertitude
            uncertainty = self._calculate_prediction_uncertainty(validated_patterns, confidence_scores)

            # 7. Génération recommandation
            recommendation = self._generate_recommendation(ensemble_prediction, confidence_scores, uncertainty)

            # 8. Création breakdown patterns
            pattern_breakdown = self._create_pattern_breakdown(validated_patterns)

            reasoning_time = time.time() - start_time

            # Construction résultat final
            result = {
                'prediction': ensemble_prediction,
                'confidence': confidence_scores['overall'],
                'uncertainty': uncertainty,
                'recommendation': recommendation,
                'reasoning_details': {
                    'patterns_used': len(validated_patterns),
                    'pattern_types': list(set(p.get('type', 'unknown') for p in validated_patterns)),
                    'reasoning_time': reasoning_time,
                    'session_number': self.reasoning_sessions,
                    'analysis_window': analysis_window
                },
                'pattern_breakdown': pattern_breakdown,
                'meta_info': {
                    'round': current_round,
                    'history_length': len(game_history),
                    'total_predictions': self.total_predictions,
                    'session_accuracy': self._calculate_session_accuracy()
                }
            }

            # Mise à jour historique confiance
            self.confidence_history.append(confidence_scores['overall'])

            logger.info(f"Raisonnement AZR - Confiance: {confidence_scores['overall']:.3f}, "
                       f"Patterns: {len(validated_patterns)}, Temps: {reasoning_time:.3f}s")

            return result

        except Exception as e:
            logger.error(f"Erreur raisonnement adaptatif: {e}")
            return self._create_fallback_reasoning(game_history)

    def _combine_patterns(self, proposed_patterns: Dict[str, List[Dict]],
                         successful_patterns: List[Dict]) -> List[Dict]:
        """Combine patterns proposés avec patterns validés"""
        all_patterns = []

        # Ajout patterns proposés
        for pattern_type, patterns in proposed_patterns.items():
            all_patterns.extend(patterns)

        # Ajout patterns validés récents (avec decay)
        for pattern in successful_patterns[-10:]:  # 10 derniers patterns validés
            # Application decay temporel
            age_factor = self._calculate_pattern_age_factor(pattern)
            if age_factor > 0.3:  # Seuil de pertinence
                pattern_copy = pattern.copy()
                pattern_copy['confidence'] *= age_factor
                all_patterns.append(pattern_copy)

        return all_patterns

    def _validate_and_score_patterns(self, patterns: List[Dict], recent_history: List[int]) -> List[Dict]:
        """Valide et score les patterns"""
        validated_patterns = []

        for pattern in patterns:
            try:
                # Validation par le pattern validator
                validation_result = self.pattern_validator.validate_pattern(pattern, recent_history)

                if validation_result['is_valid']:
                    # Ajout informations validation
                    pattern['validation_result'] = validation_result
                    pattern['adjusted_confidence'] = validation_result['adjusted_confidence']
                    pattern['reliability'] = validation_result.get('reliability', 0.5)

                    validated_patterns.append(pattern)

            except Exception as e:
                logger.warning(f"Erreur validation pattern: {e}")
                continue

        return validated_patterns

    def _calculate_adaptive_confidence(self, validated_patterns: List[Dict]) -> Dict[str, float]:
        """Calcule confiance adaptative basée sur patterns validés"""
        if not validated_patterns:
            return {'overall': 0.5, 'pattern_consensus': 0.0, 'reliability_factor': 0.0}

        # Confiances individuelles
        individual_confidences = [p['adjusted_confidence'] for p in validated_patterns]

        # Consensus entre patterns
        predictions = []
        for pattern in validated_patterns:
            validation = pattern.get('validation_result', {})
            prediction_details = validation.get('prediction_details', {})
            predicted_outcome = prediction_details.get('predicted_outcome')
            if predicted_outcome is not None:
                predictions.append(predicted_outcome)

        # Calcul consensus
        if predictions:
            unique_predictions = set(predictions)
            if len(unique_predictions) == 1:
                pattern_consensus = 1.0  # Consensus parfait
            else:
                # Consensus partiel basé sur majorité
                most_common = max(set(predictions), key=predictions.count)
                consensus_ratio = predictions.count(most_common) / len(predictions)
                pattern_consensus = consensus_ratio
        else:
            pattern_consensus = 0.0

        # Facteur fiabilité basé sur historique
        reliability_scores = [p.get('reliability', 0.5) for p in validated_patterns]
        reliability_factor = np.mean(reliability_scores) if reliability_scores else 0.5

        # Confiance globale avec formule unifiée
        base_confidence = np.mean(individual_confidences)

        # Utilisation formule unifiée pour calcul final (avec fallback)
        if global_confidence_calculator:
            unified_confidence = global_confidence_calculator.calculate_ensemble_confidence(
                model_confidences={'azr_patterns': base_confidence},
                consensus_factor=pattern_consensus,
                performance_history=[reliability_factor]
            )
        else:
            # Fallback: calcul confiance simple
            unified_confidence = base_confidence * (0.5 + 0.5 * pattern_consensus) * reliability_factor

        return {
            'overall': max(0.1, min(0.9, unified_confidence)),
            'pattern_consensus': pattern_consensus,
            'reliability_factor': reliability_factor,
            'base_confidence': base_confidence
        }
