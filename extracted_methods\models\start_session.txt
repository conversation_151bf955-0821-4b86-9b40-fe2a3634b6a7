# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\models.py
# Lignes: 131 à 155
# Type: Méthode de la classe AZRBaccaratPredictor

    def start_session(self, initial_history: List[int] = None) -> Dict[str, Any]:
        """Démarre une session de prédiction AZR"""
        try:
            if self.is_active:
                return {'success': False, 'message': 'Session déjà active'}

            # Démarrage session self-play
            session_result = self.self_play_engine.start_self_play_session(initial_history)

            if session_result['success']:
                self.is_active = True
                self.current_session = session_result['session_id']

                logger.info(f"Session AZR démarrée - ID: {self.current_session}")
                return {
                    'success': True,
                    'session_id': self.current_session,
                    'message': 'Session AZR démarrée'
                }
            else:
                return session_result

        except Exception as e:
            logger.error(f"Erreur démarrage session AZR: {e}")
            return {'success': False, 'message': f'Erreur: {e}'}