# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\azr_core.py
# Lignes: 226 à 251
# Type: Méthode de la classe AZRProposer

    def _generate_pattern_task(self, context: Dict[str, Any]) -> AZRTask:
        """Génère tâche de prédiction de pattern"""
        recent_results = context.get('recent_results', [])

        # Crée séquence d'entraînement
        if len(recent_results) >= 8:
            sequence = recent_results[-8:]
        else:
            # Génère séquence aléatoire équilibrée
            sequence = [random.choice([0, 1]) for _ in range(8)]

        task_id = f"pattern_{len(self.task_history)}"
        difficulty = self._calculate_task_difficulty(sequence)

        return AZRTask(
            task_id=task_id,
            task_type='pattern_prediction',
            description=f"Prédire le prochain résultat basé sur pattern: {sequence}",
            input_data={
                'sequence': sequence,
                'pattern_length': len(sequence),
                'prediction_target': 'next_result'
            },
            difficulty=difficulty,
            complexity=0.6
        )