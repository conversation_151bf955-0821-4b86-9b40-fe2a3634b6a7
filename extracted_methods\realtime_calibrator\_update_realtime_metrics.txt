# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 248 à 264
# Type: Méthode de la classe RealtimeCalibrator

    def _update_realtime_metrics(self):
        """Met à jour métriques temps réel"""
        if len(self.performance_history) < 5:
            return

        # Calcul accuracy récente (10 dernières manches)
        recent_results = list(self.performance_history)[-10:]
        correct_predictions = sum(1 for r in recent_results if r['correct'])
        self.realtime_accuracy = correct_predictions / len(recent_results)

        # Calcul confiance moyenne récente
        recent_confidences = [r['confidence'] for r in recent_results]
        self.realtime_confidence = np.mean(recent_confidences)

        # Calcul stabilité (variance confiance)
        confidence_variance = np.var(recent_confidences)
        self.realtime_stability = max(0.0, 1.0 - confidence_variance)