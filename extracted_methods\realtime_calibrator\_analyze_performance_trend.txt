# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 580 à 621
# Type: Méthode de la classe RealtimeCalibrator

    def _analyze_performance_trend(self) -> Dict[str, Any]:
        """Analyse tendance performance récente"""
        try:
            if len(self.performance_history) < 10:
                return {'trend': 'insufficient_data', 'slope': 0.0}

            # Analyse sur 20 dernières manches
            recent_results = list(self.performance_history)[-20:]
            accuracies = []

            # Calcul accuracy par fenêtre glissante
            window_size = 5
            for i in range(len(recent_results) - window_size + 1):
                window = recent_results[i:i + window_size]
                accuracy = sum(1 for r in window if r['correct']) / len(window)
                accuracies.append(accuracy)

            if len(accuracies) < 2:
                return {'trend': 'insufficient_data', 'slope': 0.0}

            # Calcul tendance (régression linéaire simple)
            x = np.arange(len(accuracies))
            slope = np.polyfit(x, accuracies, 1)[0]

            # Classification tendance
            if slope > 0.02:
                trend = 'improving'
            elif slope < -0.02:
                trend = 'degrading'
            else:
                trend = 'stable'

            return {
                'trend': trend,
                'slope': slope,
                'recent_accuracy': accuracies[-1],
                'accuracy_variance': np.var(accuracies)
            }

        except Exception as e:
            logger.error(f"Erreur analyse tendance: {e}")
            return {'trend': 'error', 'slope': 0.0}