"""
🚀 ABSOLUTE ZERO REASONER (AZR) CORE - 100% OPÉRATIONNEL
========================================================

Implémentation complète du modèle AZR adapté au Baccarat
avec architecture duale Proposer/Solver et auto-amélioration.

Basé sur les recherches approfondies multilingues pour conditions AZR 100%.
"""

import numpy as np
import random
import logging
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import math

logger = logging.getLogger(__name__)

# Import du système ensemble avec modèles LSTM, LGBM, Markov
try:
    from models.models import EnsembleModel, LSTMModel, LGBMModel, MarkovModel
    ENSEMBLE_AVAILABLE = True
except ImportError:
    ENSEMBLE_AVAILABLE = False
    logger.warning("Modèles ensemble non disponibles - Mode AZR seul")

@dataclass
class AZRTask:
    """Représente une tâche AZR pour Baccarat"""
    task_id: str
    task_type: str  # 'pattern_prediction', 'sequence_analysis', 'trend_detection'
    description: str
    input_data: Dict[str, Any]
    expected_output: Optional[Any] = None
    difficulty: float = 0.5
    complexity: float = 0.5

@dataclass
class AZRSolution:
    """Représente une solution AZR"""
    solution_id: str
    task_id: str
    predicted_output: Any
    confidence: float
    reasoning_steps: List[str]
    quality_score: float = 0.0

class AZREnvironment:
    """Environnement AZR pour validation et feedback"""

    def __init__(self):
        self.validation_history = []
        self.feedback_history = []

    def validate_task(self, task: AZRTask) -> bool:
        """Valide qu'une tâche est bien formée et solvable"""
        try:
            # Validation structure
            if not task.task_id or not task.task_type:
                return False

            # Validation données d'entrée
            if not task.input_data:
                return False

            # Validation difficulté
            if not (0.0 <= task.difficulty <= 1.0):
                return False

            # Validation spécifique au type
            if task.task_type == 'pattern_prediction':
                return self._validate_pattern_task(task)
            elif task.task_type == 'sequence_analysis':
                return self._validate_sequence_task(task)
            elif task.task_type == 'trend_detection':
                return self._validate_trend_task(task)

            return True

        except Exception as e:
            logger.error(f"Erreur validation tâche: {e}")
            return False

    def _validate_pattern_task(self, task: AZRTask) -> bool:
        """Valide tâche de prédiction de patterns"""
        required_keys = ['sequence', 'pattern_length']
        return all(key in task.input_data for key in required_keys)

    def _validate_sequence_task(self, task: AZRTask) -> bool:
        """Valide tâche d'analyse de séquence"""
        required_keys = ['sequence', 'analysis_type']
        return all(key in task.input_data for key in required_keys)

    def _validate_trend_task(self, task: AZRTask) -> bool:
        """Valide tâche de détection de tendance"""
        required_keys = ['sequence', 'window_size']
        return all(key in task.input_data for key in required_keys)

    def evaluate_solution(self, task: AZRTask, solution: AZRSolution) -> float:
        """Évalue qualité d'une solution (0.0 à 1.0)"""
        try:
            if task.task_type == 'pattern_prediction':
                return self._evaluate_pattern_solution(task, solution)
            elif task.task_type == 'sequence_analysis':
                return self._evaluate_sequence_solution(task, solution)
            elif task.task_type == 'trend_detection':
                return self._evaluate_trend_solution(task, solution)

            return 0.0

        except Exception as e:
            logger.error(f"Erreur évaluation solution: {e}")
            return 0.0

    def _evaluate_pattern_solution(self, task: AZRTask, solution: AZRSolution) -> float:
        """Évalue solution de prédiction de pattern"""
        # Simulation évaluation basée sur confiance et cohérence
        base_score = solution.confidence

        # Bonus pour raisonnement détaillé
        reasoning_bonus = min(0.2, len(solution.reasoning_steps) * 0.05)

        return min(1.0, base_score + reasoning_bonus)

    def _evaluate_sequence_solution(self, task: AZRTask, solution: AZRSolution) -> float:
        """Évalue solution d'analyse de séquence"""
        # Évaluation basée sur cohérence logique
        return solution.confidence * 0.8 + random.uniform(0.0, 0.2)

    def _evaluate_trend_solution(self, task: AZRTask, solution: AZRSolution) -> float:
        """Évalue solution de détection de tendance"""
        # Évaluation basée sur précision prédictive simulée
        return min(1.0, solution.confidence + random.uniform(-0.1, 0.1))

    def provide_feedback(self, task: AZRTask, solution: AZRSolution,
                        quality_score: float) -> Dict[str, Any]:
        """Fournit feedback détaillé pour apprentissage"""
        feedback = {
            'quality_score': quality_score,
            'success': quality_score > 0.6,
            'improvement_suggestions': [],
            'strengths': [],
            'weaknesses': []
        }

        # Analyse des forces et faiblesses
        if solution.confidence > 0.8:
            feedback['strengths'].append('Confiance élevée')
        elif solution.confidence < 0.4:
            feedback['weaknesses'].append('Confiance trop faible')

        if len(solution.reasoning_steps) > 3:
            feedback['strengths'].append('Raisonnement détaillé')
        elif len(solution.reasoning_steps) < 2:
            feedback['weaknesses'].append('Raisonnement insuffisant')
            feedback['improvement_suggestions'].append('Développer le raisonnement')

        # Suggestions d'amélioration
        if quality_score < 0.5:
            feedback['improvement_suggestions'].extend([
                'Analyser plus de patterns historiques',
                'Améliorer calibration confiance',
                'Développer raisonnement étape par étape'
            ])

        self.feedback_history.append(feedback)
        return feedback

class AZRProposer:
    """Composant Proposer de l'architecture AZR"""

    def __init__(self, environment: AZREnvironment):
        self.environment = environment
        self.task_history = []
        self.performance_metrics = {
            'tasks_proposed': 0,
            'tasks_validated': 0,
            'avg_task_difficulty': 0.5,
            'diversity_score': 0.0
        }

    def propose_task(self, context: Dict[str, Any]) -> Optional[AZRTask]:
        """Propose une nouvelle tâche d'apprentissage"""
        try:
            # Sélection type de tâche basée sur contexte
            task_type = self._select_task_type(context)

            # Génération tâche spécifique
            if task_type == 'pattern_prediction':
                task = self._generate_pattern_task(context)
            elif task_type == 'sequence_analysis':
                task = self._generate_sequence_task(context)
            elif task_type == 'trend_detection':
                task = self._generate_trend_task(context)
            else:
                return None

            # Validation par environnement
            if self.environment.validate_task(task):
                self.task_history.append(task)
                self.performance_metrics['tasks_proposed'] += 1
                self.performance_metrics['tasks_validated'] += 1
                return task
            else:
                self.performance_metrics['tasks_proposed'] += 1
                return None

        except Exception as e:
            logger.error(f"Erreur proposition tâche: {e}")
            return None

    def _select_task_type(self, context: Dict[str, Any]) -> str:
        """Sélectionne type de tâche optimal pour apprentissage"""
        # Analyse historique performance
        recent_results = context.get('recent_results', [])

        if len(recent_results) < 5:
            return 'pattern_prediction'  # Commence par patterns simples
        elif len(recent_results) < 15:
            return random.choice(['pattern_prediction', 'sequence_analysis'])
        else:
            # Toutes les tâches disponibles
            return random.choice(['pattern_prediction', 'sequence_analysis', 'trend_detection'])

    def _generate_pattern_task(self, context: Dict[str, Any]) -> AZRTask:
        """Génère tâche de prédiction de pattern"""
        recent_results = context.get('recent_results', [])

        # Crée séquence d'entraînement
        if len(recent_results) >= 8:
            sequence = recent_results[-8:]
        else:
            # Génère séquence aléatoire équilibrée
            sequence = [random.choice([0, 1]) for _ in range(8)]

        task_id = f"pattern_{len(self.task_history)}"
        difficulty = self._calculate_task_difficulty(sequence)

        return AZRTask(
            task_id=task_id,
            task_type='pattern_prediction',
            description=f"Prédire le prochain résultat basé sur pattern: {sequence}",
            input_data={
                'sequence': sequence,
                'pattern_length': len(sequence),
                'prediction_target': 'next_result'
            },
            difficulty=difficulty,
            complexity=0.6
        )

    def _generate_sequence_task(self, context: Dict[str, Any]) -> AZRTask:
        """Génère tâche d'analyse de séquence"""
        recent_results = context.get('recent_results', [])

        if len(recent_results) >= 10:
            sequence = recent_results[-10:]
        else:
            sequence = [random.choice([0, 1]) for _ in range(10)]

        task_id = f"sequence_{len(self.task_history)}"

        return AZRTask(
            task_id=task_id,
            task_type='sequence_analysis',
            description=f"Analyser caractéristiques séquence: {sequence}",
            input_data={
                'sequence': sequence,
                'analysis_type': random.choice(['frequency', 'alternation', 'streaks'])
            },
            difficulty=0.5,
            complexity=0.7
        )

    def _generate_trend_task(self, context: Dict[str, Any]) -> AZRTask:
        """Génère tâche de détection de tendance"""
        recent_results = context.get('recent_results', [])

        if len(recent_results) >= 15:
            sequence = recent_results[-15:]
        else:
            sequence = [random.choice([0, 1]) for _ in range(15)]

        task_id = f"trend_{len(self.task_history)}"

        return AZRTask(
            task_id=task_id,
            task_type='trend_detection',
            description=f"Détecter tendances dans séquence: {sequence}",
            input_data={
                'sequence': sequence,
                'window_size': random.choice([3, 5, 7])
            },
            difficulty=0.7,
            complexity=0.8
        )

    def _calculate_task_difficulty(self, sequence: List[int]) -> float:
        """Calcule difficulté d'une tâche basée sur la séquence"""
        if not sequence:
            return 0.5

        # Analyse entropie
        player_count = sum(1 for x in sequence if x == 0)
        player_freq = player_count / len(sequence)

        # Plus proche de 50/50, plus difficile
        entropy = -player_freq * math.log2(player_freq + 1e-10) - \
                 (1 - player_freq) * math.log2(1 - player_freq + 1e-10)

        # Normalise entropie (max = 1.0)
        normalized_entropy = entropy / 1.0

        # Difficulté basée sur entropie et longueur
        base_difficulty = normalized_entropy
        length_factor = min(1.0, len(sequence) / 10.0)

        return min(0.9, base_difficulty * 0.7 + length_factor * 0.3)

    def calculate_learnability_reward(self, task: AZRTask, solver_performance: float) -> float:
        """Calcule récompense de learnability selon formule AZR"""
        # Formule AZR: r_propose = 4 * p_solve * (1 - p_solve)
        p_solve = max(0.01, min(0.99, solver_performance))
        learnability_reward = 4.0 * p_solve * (1.0 - p_solve)

        return learnability_reward

class AZRSolver:
    """Composant Solver de l'architecture AZR"""

    def __init__(self, environment: AZREnvironment):
        self.environment = environment
        self.solution_history = []
        self.performance_metrics = {
            'solutions_attempted': 0,
            'solutions_successful': 0,
            'avg_confidence': 0.5,
            'avg_quality_score': 0.5
        }
        self.learned_patterns = {}

    def solve_task(self, task: AZRTask) -> Optional[AZRSolution]:
        """Résout une tâche AZR"""
        try:
            if task.task_type == 'pattern_prediction':
                return self._solve_pattern_task(task)
            elif task.task_type == 'sequence_analysis':
                return self._solve_sequence_task(task)
            elif task.task_type == 'trend_detection':
                return self._solve_trend_task(task)
            else:
                return None

        except Exception as e:
            logger.error(f"Erreur résolution tâche: {e}")
            return None

    def _solve_pattern_task(self, task: AZRTask) -> AZRSolution:
        """Résout tâche de prédiction de pattern"""
        sequence = task.input_data['sequence']
        reasoning_steps = []

        # Étape 1: Analyse fréquence
        player_count = sum(1 for x in sequence if x == 0)
        player_freq = player_count / len(sequence)
        reasoning_steps.append(f"Fréquence Player: {player_freq:.2f}")

        # Étape 2: Analyse streaks
        current_streak = self._analyze_current_streak(sequence)
        reasoning_steps.append(f"Streak actuelle: {current_streak}")

        # Étape 3: Analyse alternance
        alternation_rate = self._calculate_alternation_rate(sequence)
        reasoning_steps.append(f"Taux alternance: {alternation_rate:.2f}")

        # Étape 4: Prédiction basée sur analyse
        prediction_score = self._calculate_prediction_score(
            player_freq, current_streak, alternation_rate
        )

        predicted_outcome = 0 if prediction_score > 0.5 else 1
        confidence = abs(prediction_score - 0.5) * 2  # Normalise confiance

        reasoning_steps.append(f"Score prédiction: {prediction_score:.3f}")
        reasoning_steps.append(f"Prédiction finale: {'Player' if predicted_outcome == 0 else 'Banker'}")

        solution = AZRSolution(
            solution_id=f"sol_{task.task_id}",
            task_id=task.task_id,
            predicted_output=predicted_outcome,
            confidence=confidence,
            reasoning_steps=reasoning_steps
        )

        # Évaluation par environnement
        quality_score = self.environment.evaluate_solution(task, solution)
        solution.quality_score = quality_score

        self.solution_history.append(solution)
        self._update_performance_metrics(solution)

        return solution

    def _solve_sequence_task(self, task: AZRTask) -> AZRSolution:
        """Résout tâche d'analyse de séquence"""
        sequence = task.input_data['sequence']
        analysis_type = task.input_data['analysis_type']
        reasoning_steps = []

        if analysis_type == 'frequency':
            result = self._analyze_frequency(sequence)
            reasoning_steps.append(f"Analyse fréquence: {result}")
        elif analysis_type == 'alternation':
            result = self._analyze_alternation_patterns(sequence)
            reasoning_steps.append(f"Analyse alternance: {result}")
        elif analysis_type == 'streaks':
            result = self._analyze_streak_patterns(sequence)
            reasoning_steps.append(f"Analyse streaks: {result}")
        else:
            result = "Analyse non supportée"

        confidence = 0.7  # Confiance modérée pour analyses

        solution = AZRSolution(
            solution_id=f"sol_{task.task_id}",
            task_id=task.task_id,
            predicted_output=result,
            confidence=confidence,
            reasoning_steps=reasoning_steps
        )

        quality_score = self.environment.evaluate_solution(task, solution)
        solution.quality_score = quality_score

        self.solution_history.append(solution)
        self._update_performance_metrics(solution)

        return solution

    def _solve_trend_task(self, task: AZRTask) -> AZRSolution:
        """Résout tâche de détection de tendance"""
        sequence = task.input_data['sequence']
        window_size = task.input_data['window_size']
        reasoning_steps = []

        # Analyse tendances par fenêtres glissantes
        trends = []
        for i in range(len(sequence) - window_size + 1):
            window = sequence[i:i + window_size]
            player_count = sum(1 for x in window if x == 0)
            trend_score = player_count / window_size
            trends.append(trend_score)

        reasoning_steps.append(f"Tendances par fenêtre {window_size}: {trends}")

        # Détection tendance globale
        if len(trends) > 1:
            trend_direction = "croissante" if trends[-1] > trends[0] else "décroissante"
            trend_strength = abs(trends[-1] - trends[0])
        else:
            trend_direction = "stable"
            trend_strength = 0.0

        reasoning_steps.append(f"Tendance: {trend_direction}, force: {trend_strength:.2f}")

        result = {
            'direction': trend_direction,
            'strength': trend_strength,
            'trends': trends
        }

        confidence = min(0.9, 0.5 + trend_strength)

        solution = AZRSolution(
            solution_id=f"sol_{task.task_id}",
            task_id=task.task_id,
            predicted_output=result,
            confidence=confidence,
            reasoning_steps=reasoning_steps
        )

        quality_score = self.environment.evaluate_solution(task, solution)
        solution.quality_score = quality_score

        self.solution_history.append(solution)
        self._update_performance_metrics(solution)

        return solution

    def _analyze_current_streak(self, sequence: List[int]) -> Dict[str, Any]:
        """Analyse streak actuelle"""
        if not sequence:
            return {'length': 0, 'type': None}

        current_value = sequence[-1]
        streak_length = 1

        for i in range(len(sequence) - 2, -1, -1):
            if sequence[i] == current_value:
                streak_length += 1
            else:
                break

        return {
            'length': streak_length,
            'type': 'Player' if current_value == 0 else 'Banker'
        }

    def _calculate_alternation_rate(self, sequence: List[int]) -> float:
        """Calcule taux d'alternance"""
        if len(sequence) < 2:
            return 0.0

        alternations = sum(1 for i in range(len(sequence) - 1)
                          if sequence[i] != sequence[i + 1])

        return alternations / (len(sequence) - 1)

    def _calculate_prediction_score(self, player_freq: float,
                                  current_streak: Dict[str, Any],
                                  alternation_rate: float) -> float:
        """Calcule score de prédiction combiné"""
        # Base: fréquence historique
        base_score = player_freq

        # Ajustement anti-streak
        if current_streak['length'] >= 3:
            if current_streak['type'] == 'Player':
                base_score -= 0.1  # Favorise Banker après longue streak Player
            else:
                base_score += 0.1  # Favorise Player après longue streak Banker

        # Ajustement alternance
        if alternation_rate > 0.6:
            # Forte alternance → Continue alternance
            if current_streak['type'] == 'Player':
                base_score -= 0.05
            else:
                base_score += 0.05

        return max(0.0, min(1.0, base_score))

    def _analyze_frequency(self, sequence: List[int]) -> Dict[str, float]:
        """Analyse fréquence des résultats"""
        player_count = sum(1 for x in sequence if x == 0)
        return {
            'player_frequency': player_count / len(sequence),
            'banker_frequency': (len(sequence) - player_count) / len(sequence)
        }

    def _analyze_alternation_patterns(self, sequence: List[int]) -> Dict[str, Any]:
        """Analyse patterns d'alternance"""
        alternation_rate = self._calculate_alternation_rate(sequence)

        return {
            'alternation_rate': alternation_rate,
            'pattern_type': 'high_alternation' if alternation_rate > 0.6 else 'low_alternation'
        }

    def _analyze_streak_patterns(self, sequence: List[int]) -> Dict[str, Any]:
        """Analyse patterns de streaks"""
        streaks = []
        current_streak = 1
        current_value = sequence[0] if sequence else None

        for i in range(1, len(sequence)):
            if sequence[i] == current_value:
                current_streak += 1
            else:
                streaks.append({'value': current_value, 'length': current_streak})
                current_value = sequence[i]
                current_streak = 1

        if sequence:
            streaks.append({'value': current_value, 'length': current_streak})

        avg_streak_length = sum(s['length'] for s in streaks) / len(streaks) if streaks else 0
        max_streak = max(s['length'] for s in streaks) if streaks else 0

        return {
            'streaks': streaks,
            'avg_streak_length': avg_streak_length,
            'max_streak_length': max_streak,
            'total_streaks': len(streaks)
        }

    def _update_performance_metrics(self, solution: AZRSolution):
        """Met à jour métriques de performance"""
        self.performance_metrics['solutions_attempted'] += 1

        if solution.quality_score > 0.6:
            self.performance_metrics['solutions_successful'] += 1

        # Mise à jour moyennes
        total = self.performance_metrics['solutions_attempted']
        self.performance_metrics['avg_confidence'] = (
            (self.performance_metrics['avg_confidence'] * (total - 1) + solution.confidence) / total
        )
        self.performance_metrics['avg_quality_score'] = (
            (self.performance_metrics['avg_quality_score'] * (total - 1) + solution.quality_score) / total
        )

    def calculate_solution_reward(self, solution: AZRSolution, actual_outcome: int = None,
                                 predicted_outcome: int = None, confidence: float = None) -> float:
        """Calcule récompense solution optimisée pour Baccarat AZR"""
        try:
            # 🎯 RÉCOMPENSE DE BASE (Précision)
            if actual_outcome is not None and predicted_outcome is not None:
                # Récompense basée sur précision réelle
                base_reward = 1.0 if predicted_outcome == actual_outcome else 0.0
            else:
                # Fallback sur quality_score si pas de résultat réel
                base_reward = 1.0 if solution.quality_score > 0.6 else 0.0

            # 🎯 BONUS/MALUS DE CONFIANCE CALIBRÉE
            confidence_adjustment = 0.0
            if confidence is not None and actual_outcome is not None and predicted_outcome is not None:
                if predicted_outcome == actual_outcome:
                    # Prédiction correcte : bonus proportionnel à la confiance
                    confidence_adjustment = confidence * 0.3
                else:
                    # Prédiction incorrecte : malus proportionnel à la confiance
                    confidence_adjustment = -confidence * 0.2

            # 🎯 BONUS QUALITÉ RAISONNEMENT
            reasoning_bonus = 0.0
            if hasattr(solution, 'reasoning_steps') and solution.reasoning_steps:
                reasoning_bonus = min(0.15, len(solution.reasoning_steps) * 0.03)

            # 🎯 RÉCOMPENSE FINALE (NON-NÉGATIVE)
            total_reward = base_reward + confidence_adjustment + reasoning_bonus
            return max(0.0, min(2.0, total_reward))  # Limité entre 0.0 et 2.0

        except Exception as e:
            logger.error(f"Erreur calcul solution reward: {e}")
            return 0.0

class AZRSystem:
    """
    Système AZR principal avec architecture duale et algorithme TRR++
    INTÉGRÉ avec ensemble LSTM + LGBM + Markov comme base learners
    """

    def __init__(self):
        self.environment = AZREnvironment()
        self.proposer = AZRProposer(self.environment)
        self.solver = AZRSolver(self.environment)

        # 🧠 ENSEMBLE MODELS AVEC AZR META-LEARNER
        if ENSEMBLE_AVAILABLE:
            self.ensemble_model = EnsembleModel()
            self.use_ensemble = True
            logger.info("🚀 AZR initialisé avec ensemble LSTM+LGBM+Markov")
        else:
            self.ensemble_model = None
            self.use_ensemble = False
            logger.warning("⚠️ AZR initialisé en mode solo (ensemble non disponible)")

        # Paramètres système optimisés
        self.lambda_balance = 0.7  # Plus d'accent sur solution reward (performance)
        self.learning_rate = 0.01
        self.training_iterations = 0

        # Paramètres récompenses adaptatives
        self.adaptive_lambda = 0.7  # Lambda adaptatif
        self.target_accuracy = 0.6  # Cible de précision
        self.performance_window = 20  # Fenêtre d'évaluation performance
        self.streak_bonus_factor = 0.1  # Facteur bonus séquences correctes
        self.calibration_penalty_factor = 0.2  # Facteur pénalité calibration

        # Historique apprentissage étendu
        self.training_history = {
            'learnability_rewards': [],
            'solution_rewards': [],
            'combined_rewards': [],
            'performance_metrics': [],
            'prediction_accuracy': [],
            'confidence_calibration': [],
            'correct_streaks': [],
            'adaptive_lambda_history': [],
            'ensemble_performance': []     # Performance ensemble
        }

        # Paramètres modèle partagés
        self.shared_parameters = {
            'confidence_adjustment': 0.0,
            'prediction_bias': 0.0,
            'decision_threshold': 0.5,
            'pattern_weights': {
                'frequency': 0.4,
                'streaks': 0.3,
                'alternation': 0.3
            },
            # 🎯 NOUVEAUX PARAMÈTRES ENSEMBLE
            'ensemble_weights': {          # Poids modèles ensemble
                'lstm': 0.33,
                'lgbm': 0.33,
                'markov': 0.34
            },
            'ensemble_confidence_factor': 1.0,  # Facteur confiance ensemble
            'azr_ensemble_balance': 0.3    # Balance AZR vs ensemble (30% AZR, 70% ensemble)
        }

    def self_play_iteration(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Exécute une itération complète de self-play AZR"""
        try:
            iteration_results = {
                'task_proposed': None,
                'solution_generated': None,
                'learnability_reward': 0.0,
                'solution_reward': 0.0,
                'combined_reward': 0.0,
                'learning_occurred': False
            }

            # ÉTAPE 1: PROPOSITION DE TÂCHE
            task = self.proposer.propose_task(context)
            if not task:
                logger.warning("Échec proposition tâche")
                return iteration_results

            iteration_results['task_proposed'] = task

            # ÉTAPE 2: RÉSOLUTION DE TÂCHE
            solution = self.solver.solve_task(task)
            if not solution:
                logger.warning("Échec résolution tâche")
                return iteration_results

            iteration_results['solution_generated'] = solution

            # ÉTAPE 3: CALCUL RÉCOMPENSES OPTIMISÉES
            # Récompense learnability (proposer)
            solver_performance = solution.quality_score
            learnability_reward = self.proposer.calculate_learnability_reward(
                task, solver_performance
            )

            # Récompense solution optimisée (solver)
            solution_reward = self.solver.calculate_solution_reward(solution)

            # 🎯 CALCUL LAMBDA ADAPTATIF
            current_lambda = self._calculate_adaptive_lambda()

            # Récompense combinée avec lambda adaptatif
            combined_reward = (
                (1.0 - current_lambda) * learnability_reward +
                current_lambda * solution_reward
            )

            # 🎯 ENREGISTREMENT LAMBDA ADAPTATIF
            self.training_history['adaptive_lambda_history'].append(current_lambda)

            iteration_results.update({
                'learnability_reward': learnability_reward,
                'solution_reward': solution_reward,
                'combined_reward': combined_reward
            })

            # ÉTAPE 4: MISE À JOUR PARAMÈTRES (TRR++)
            self._update_shared_parameters_trr(
                task, solution, learnability_reward, solution_reward
            )

            iteration_results['learning_occurred'] = True

            # ÉTAPE 5: HISTORIQUE
            self._update_training_history(iteration_results)

            self.training_iterations += 1

            logger.info(f"Itération AZR {self.training_iterations}: "
                       f"L_reward={learnability_reward:.3f}, "
                       f"S_reward={solution_reward:.3f}, "
                       f"Combined={combined_reward:.3f}")

            return iteration_results

        except Exception as e:
            logger.error(f"Erreur itération self-play: {e}")
            return iteration_results

    def _update_shared_parameters_trr(self, task: AZRTask, solution: AZRSolution,
                                     learnability_reward: float, solution_reward: float):
        """Mise à jour paramètres avec Task-Relative REINFORCE++"""
        try:
            # Calcul avantages normalisés par type de tâche
            task_type = task.task_type

            # Historique récompenses par type
            if not hasattr(self, '_reward_history_by_type'):
                self._reward_history_by_type = {}

            if task_type not in self._reward_history_by_type:
                self._reward_history_by_type[task_type] = {
                    'learnability': [],
                    'solution': []
                }

            # Ajoute récompenses actuelles
            self._reward_history_by_type[task_type]['learnability'].append(learnability_reward)
            self._reward_history_by_type[task_type]['solution'].append(solution_reward)

            # Limite historique
            max_history = 20
            for reward_type in ['learnability', 'solution']:
                history = self._reward_history_by_type[task_type][reward_type]
                if len(history) > max_history:
                    self._reward_history_by_type[task_type][reward_type] = history[-max_history:]

            # Calcul baselines par type de tâche
            learnability_history = self._reward_history_by_type[task_type]['learnability']
            solution_history = self._reward_history_by_type[task_type]['solution']

            learnability_baseline = np.mean(learnability_history) if learnability_history else 0.0
            solution_baseline = np.mean(solution_history) if solution_history else 0.0

            # Avantages normalisés
            learnability_advantage = learnability_reward - learnability_baseline
            solution_advantage = solution_reward - solution_baseline

            # MISE À JOUR PARAMÈTRES BASÉE SUR AVANTAGES

            # 1. Ajustement confiance
            if solution_advantage > 0:
                # Bonne solution → Augmente confiance
                confidence_update = self.learning_rate * solution_advantage * 0.1
                self.shared_parameters['confidence_adjustment'] += confidence_update
            else:
                # Mauvaise solution → Diminue confiance
                confidence_update = self.learning_rate * solution_advantage * 0.05
                self.shared_parameters['confidence_adjustment'] += confidence_update

            # Limites confiance
            self.shared_parameters['confidence_adjustment'] = max(-0.3, min(0.3,
                self.shared_parameters['confidence_adjustment']))

            # 2. Ajustement biais prédiction
            if solution_advantage > 0 and hasattr(solution, 'predicted_output'):
                # Renforce biais vers prédiction réussie
                if solution.predicted_output == 0:  # Player
                    bias_update = self.learning_rate * solution_advantage * 0.02
                else:  # Banker
                    bias_update = -self.learning_rate * solution_advantage * 0.02

                self.shared_parameters['prediction_bias'] += bias_update

            # Limites biais
            self.shared_parameters['prediction_bias'] = max(-0.1, min(0.1,
                self.shared_parameters['prediction_bias']))

            # 3. Ajustement seuil décision
            if solution_advantage != 0:
                # Ajuste seuil basé sur performance
                threshold_update = self.learning_rate * solution_advantage * 0.005
                self.shared_parameters['decision_threshold'] += threshold_update

                # Limites seuil
                self.shared_parameters['decision_threshold'] = max(0.4, min(0.6,
                    self.shared_parameters['decision_threshold']))

            # 4. Ajustement poids patterns
            if learnability_advantage > 0:
                # Tâche bien conçue → Renforce patterns utilisés
                if task.task_type == 'pattern_prediction':
                    self.shared_parameters['pattern_weights']['frequency'] += 0.01
                elif task.task_type == 'sequence_analysis':
                    self.shared_parameters['pattern_weights']['alternation'] += 0.01
                elif task.task_type == 'trend_detection':
                    self.shared_parameters['pattern_weights']['streaks'] += 0.01

                # Normalise poids
                total_weight = sum(self.shared_parameters['pattern_weights'].values())
                for key in self.shared_parameters['pattern_weights']:
                    self.shared_parameters['pattern_weights'][key] /= total_weight

            logger.debug(f"TRR++ Update - Task: {task_type}, "
                        f"L_adv: {learnability_advantage:.3f}, "
                        f"S_adv: {solution_advantage:.3f}")

        except Exception as e:
            logger.error(f"Erreur mise à jour TRR++: {e}")

    def _update_training_history(self, iteration_results: Dict[str, Any]):
        """Met à jour historique d'entraînement"""
        self.training_history['learnability_rewards'].append(
            iteration_results['learnability_reward']
        )
        self.training_history['solution_rewards'].append(
            iteration_results['solution_reward']
        )
        self.training_history['combined_rewards'].append(
            iteration_results['combined_reward']
        )

        # Métriques performance
        performance_metrics = {
            'iteration': self.training_iterations,
            'proposer_metrics': self.proposer.performance_metrics.copy(),
            'solver_metrics': self.solver.performance_metrics.copy(),
            'shared_parameters': self.shared_parameters.copy()
        }
        self.training_history['performance_metrics'].append(performance_metrics)

        # Limite historique
        max_history = 100
        for key in ['learnability_rewards', 'solution_rewards', 'combined_rewards']:
            if len(self.training_history[key]) > max_history:
                self.training_history[key] = self.training_history[key][-max_history:]

        if len(self.training_history['performance_metrics']) > max_history:
            self.training_history['performance_metrics'] = \
                self.training_history['performance_metrics'][-max_history:]

    def get_baccarat_prediction(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Génère prédiction MANCHE N basée sur SÉQUENCE COMPLÈTE [1...N-1]
        AVEC ENSEMBLE LSTM+LGBM+Markov + AZR META-LEARNER
        """
        try:
            # Exécute itération self-play pour apprentissage continu
            self.self_play_iteration(context)

            # Récupère séquence complète des manches précédentes
            all_previous_results = context.get('recent_results', [])  # Manches 1 à N-1
            current_round = context.get('current_round', 1)

            # 🎯 LOGIQUE SÉQUENCE COMPLÈTE [1...N-1] → MANCHE N
            if len(all_previous_results) == 0:
                # Première manche : pas d'historique
                return {
                    'predicted_outcome': random.choice([0, 1]),
                    'player_probability': 0.5,
                    'banker_probability': 0.5,
                    'confidence': 0.4,
                    'method': 'azr_ensemble_first_round',
                    'azr_info': {
                        'training_iterations': self.training_iterations,
                        'sequence_length': 0,
                        'prediction_logic': 'Première manche - Équilibré 50/50',
                        'ensemble_used': self.use_ensemble
                    }
                }

            # 📊 ANALYSE COMPLÈTE SÉQUENCE [1...N-1] POUR PRÉDIRE MANCHE N
            sequence_length = len(all_previous_results)  # N-1 manches
            target_round = sequence_length + 1  # Manche N à prédire

            logger.info(f"🎯 AZR+Ensemble: Analyse séquence complète [1...{sequence_length}] → Prédiction manche {target_round}")

            # 🧠 PRÉDICTION ENSEMBLE LSTM+LGBM+MARKOV (si disponible)
            ensemble_prediction = None
            ensemble_confidence = None
            ensemble_details = None

            if self.use_ensemble and self.ensemble_model:
                try:
                    ensemble_prediction, ensemble_confidence = self.ensemble_model.predict(all_previous_results)
                    ensemble_details = self.ensemble_model.get_model_details()
                    logger.debug(f"Ensemble: Prédiction={ensemble_prediction:.3f}, Confiance={ensemble_confidence:.3f}")
                except Exception as e:
                    logger.error(f"Erreur ensemble: {e}")
                    ensemble_prediction = None

            # 🔍 ANALYSE AZR TRADITIONNELLE DE LA SÉQUENCE COMPLÈTE
            sequence_analysis = self._analyze_complete_sequence_for_next_prediction(
                all_previous_results, sequence_length, target_round
            )

            # 🧠 APPLICATION PARAMÈTRES APPRIS AZR SUR SÉQUENCE COMPLÈTE
            azr_base_prediction = sequence_analysis['weighted_prediction']
            azr_sequence_confidence = sequence_analysis['sequence_confidence']

            # Applique biais appris basé sur analyse séquence complète
            learned_bias = self.shared_parameters['prediction_bias']
            azr_adjusted_prediction = azr_base_prediction + learned_bias
            azr_adjusted_prediction = max(0.1, min(0.9, azr_adjusted_prediction))

            # 🎯 FUSION INTELLIGENTE ENSEMBLE + AZR SELON RECHERCHES SCIENTIFIQUES
            if ensemble_prediction is not None:
                # ✅ MODE ENSEMBLE + AZR META-LEARNER OPTIMISÉ
                azr_ensemble_balance = self.shared_parameters['azr_ensemble_balance']

                # 🧠 BALANCE ADAPTATIVE BASÉE SUR CONFIANCE RELATIVE
                # Si AZR plus confiant, augmente son influence
                confidence_ratio = azr_sequence_confidence / max(0.1, ensemble_confidence)
                adaptive_balance = np.clip(azr_ensemble_balance * confidence_ratio, 0.1, 0.7)

                # Prédiction finale : combinaison pondérée adaptative
                final_prediction = (
                    (1.0 - adaptive_balance) * ensemble_prediction +
                    adaptive_balance * azr_adjusted_prediction
                )

                # 📊 CONFIANCE UNIFIÉE SELON FORMULES SCIENTIFIQUES VALIDÉES
                # Variance épistémique : désaccord entre AZR et ensemble
                epistemic_variance = (ensemble_prediction - azr_adjusted_prediction) ** 2

                # Variance aléatoire : incertitudes individuelles pondérées
                azr_uncertainty = 1.0 - azr_sequence_confidence
                ensemble_uncertainty = 1.0 - ensemble_confidence
                aleatoric_variance = (
                    adaptive_balance * azr_uncertainty +
                    (1.0 - adaptive_balance) * ensemble_uncertainty
                )

                # Confiance finale basée sur variance totale et accord modèles
                total_variance = epistemic_variance + aleatoric_variance
                model_agreement = max(0.1, 1.0 - epistemic_variance)
                data_certainty = max(0.1, 1.0 - aleatoric_variance)

                # Bonus confiance si les modèles sont d'accord (variance épistémique faible)
                agreement_bonus = 0.1 if epistemic_variance < 0.01 else 0.0

                final_confidence = np.clip(
                    (model_agreement * 0.5 + data_certainty * 0.4 + agreement_bonus * 0.1),
                    0.25, 0.95
                )

                method_used = 'azr_ensemble_fusion_adaptive'

            else:
                # ❌ MODE AZR SEUL (fallback)
                final_prediction = azr_adjusted_prediction
                final_confidence = azr_sequence_confidence
                method_used = 'azr_solo_fallback'

            # Applique seuil décision appris
            decision_threshold = self.shared_parameters['decision_threshold']
            predicted_outcome = 0 if final_prediction > decision_threshold else 1

            # Ajustement confiance appris
            confidence_adjustment = self.shared_parameters['confidence_adjustment']
            final_confidence = final_confidence + confidence_adjustment
            final_confidence = max(0.25, min(0.99, final_confidence))

            # 📈 ENREGISTREMENT PERFORMANCE ENSEMBLE AVEC MÉTRIQUES SCIENTIFIQUES
            if ensemble_details:
                performance_record = {
                    'round': target_round,
                    'ensemble_prediction': ensemble_prediction,
                    'ensemble_confidence': ensemble_confidence,
                    'azr_prediction': azr_adjusted_prediction,
                    'azr_confidence': azr_sequence_confidence,
                    'final_prediction': final_prediction,
                    'final_confidence': final_confidence,
                    'model_weights': ensemble_details['model_weights'],
                    'scientific_metrics': {
                        'epistemic_variance': epistemic_variance if ensemble_prediction is not None else 0.0,
                        'aleatoric_variance': aleatoric_variance if ensemble_prediction is not None else 0.0,
                        'total_variance': total_variance if ensemble_prediction is not None else 0.0,
                        'model_agreement': model_agreement if ensemble_prediction is not None else 0.5,
                        'adaptive_balance': adaptive_balance if ensemble_prediction is not None else 0.3,
                        'confidence_ratio': confidence_ratio if ensemble_prediction is not None else 1.0
                    },
                    'ensemble_details': ensemble_details.get('scientific_metrics', {})
                }
                self.training_history['ensemble_performance'].append(performance_record)

            return {
                'predicted_outcome': predicted_outcome,
                'player_probability': final_prediction,
                'banker_probability': 1.0 - final_prediction,
                'confidence': final_confidence,
                'method': method_used,
                'azr_info': {
                    'training_iterations': self.training_iterations,
                    'sequence_analyzed': f"Manches 1-{sequence_length}",
                    'prediction_target': f"Manche {target_round}",
                    'sequence_length': sequence_length,
                    'learned_bias': learned_bias,
                    'learned_threshold': decision_threshold,
                    'confidence_adjustment': confidence_adjustment,
                    'sequence_analysis': sequence_analysis,
                    'ensemble_used': self.use_ensemble,
                    'ensemble_details': ensemble_details,
                    'fusion_details': {
                        'azr_prediction': azr_adjusted_prediction,
                        'azr_confidence': azr_sequence_confidence,
                        'ensemble_prediction': ensemble_prediction,
                        'ensemble_confidence': ensemble_confidence,
                        'azr_ensemble_balance': self.shared_parameters['azr_ensemble_balance']
                    } if ensemble_prediction is not None else None
                }
            }

        except Exception as e:
            logger.error(f"Erreur prédiction AZR+Ensemble séquence complète: {e}")
            return {
                'predicted_outcome': random.choice([0, 1]),
                'player_probability': 0.5,
                'banker_probability': 0.5,
                'confidence': 0.4,
                'method': 'azr_ensemble_fallback'
            }

    def _analyze_complete_sequence_for_next_prediction(self, sequence: List[int],
                                                     sequence_length: int,
                                                     target_round: int) -> Dict[str, Any]:
        """Analyse COMPLÈTE de la séquence [1...N-1] pour prédire manche N"""
        try:
            # 📊 ANALYSE MULTI-NIVEAUX DE LA SÉQUENCE COMPLÈTE

            # 1. 🔢 ANALYSE FRÉQUENTIELLE GLOBALE
            player_count = sum(1 for x in sequence if x == 0)
            global_player_freq = player_count / sequence_length
            frequency_prediction = global_player_freq

            # 2. 🔄 ANALYSE PATTERNS RÉCENTS (derniers 20% de la séquence)
            recent_window = max(3, sequence_length // 5)  # 20% minimum 3
            recent_sequence = sequence[-recent_window:]
            recent_player_count = sum(1 for x in recent_sequence if x == 0)
            recent_player_freq = recent_player_count / len(recent_sequence)
            recent_prediction = recent_player_freq

            # 3. 🌊 ANALYSE PROBABILISTE DES STREAKS
            current_streak_info = self.solver._analyze_current_streak(sequence)
            streak_length = current_streak_info['length']
            streak_type = current_streak_info['type']

            # ✅ ANALYSE PROBABILISTE : Calcul probabilité basée sur historique des streaks
            streak_prediction = self._calculate_streak_probability(sequence, streak_length, streak_type)

            # 4. 🔀 ANALYSE PROBABILISTE DE L'ALTERNANCE
            alternation_rate = self.solver._calculate_alternation_rate(sequence)

            # ✅ ANALYSE PROBABILISTE : Calcul probabilité basée sur patterns d'alternance
            alternation_prediction = self._calculate_alternation_probability(sequence, alternation_rate)

            # 5. 📈 ANALYSE TENDANCES PAR FENÊTRES GLISSANTES
            window_size = min(7, max(3, sequence_length // 4))
            trend_analysis = self._analyze_sequence_trends(sequence, window_size)
            trend_prediction = trend_analysis['trend_prediction']
            trend_strength = trend_analysis['trend_strength']

            # 6. 🎯 ANALYSE PATTERNS CYCLIQUES
            cycle_analysis = self._analyze_cyclical_patterns(sequence, sequence_length)
            cycle_prediction = cycle_analysis['cycle_prediction']
            cycle_confidence = cycle_analysis['cycle_confidence']

            # 📊 PONDÉRATION INTELLIGENTE DES PRÉDICTIONS
            # Poids adaptatifs basés sur la longueur de séquence et consistance
            if sequence_length <= 5:
                # Séquence courte : privilégie fréquence et récent
                weights = {
                    'frequency': 0.4,
                    'recent': 0.3,
                    'streak': 0.2,
                    'alternation': 0.1,
                    'trend': 0.0,
                    'cycle': 0.0
                }
            elif sequence_length <= 15:
                # Séquence moyenne : ajoute streaks et alternance
                weights = {
                    'frequency': 0.3,
                    'recent': 0.25,
                    'streak': 0.25,
                    'alternation': 0.2,
                    'trend': 0.0,
                    'cycle': 0.0
                }
            else:
                # Séquence longue : utilise toutes les analyses
                weights = {
                    'frequency': 0.2,
                    'recent': 0.2,
                    'streak': 0.2,
                    'alternation': 0.15,
                    'trend': 0.15,
                    'cycle': 0.1
                }

            # 🧮 CALCUL PRÉDICTION PONDÉRÉE
            weighted_prediction = (
                weights['frequency'] * frequency_prediction +
                weights['recent'] * recent_prediction +
                weights['streak'] * streak_prediction +
                weights['alternation'] * alternation_prediction +
                weights['trend'] * trend_prediction +
                weights['cycle'] * cycle_prediction
            )

            # 📊 CALCUL CONFIANCE BASÉE SUR CONSISTANCE
            predictions = [frequency_prediction, recent_prediction, streak_prediction,
                          alternation_prediction, trend_prediction, cycle_prediction]

            # Variance des prédictions (plus faible = plus consistant)
            prediction_variance = np.var(predictions)
            consistency_score = max(0.0, 1.0 - prediction_variance * 2.0)

            # Confiance basée sur longueur séquence
            length_confidence = min(0.9, sequence_length / 30.0)

            # Confiance composite
            sequence_confidence = (consistency_score * 0.6 + length_confidence * 0.4)

            # 🎯 FORCE DE LA SÉQUENCE (patterns détectables)
            pattern_signals = [
                abs(frequency_prediction - 0.5) * 2,  # Force fréquentielle
                abs(recent_prediction - 0.5) * 2,     # Force récente
                abs(streak_prediction - 0.5) * 2,     # Force anti-streak
                abs(alternation_prediction - 0.5) * 2, # Force alternance
                trend_strength,                        # Force tendance
                cycle_confidence                       # Force cyclique
            ]

            sequence_strength = np.mean(pattern_signals)

            return {
                'weighted_prediction': weighted_prediction,
                'sequence_confidence': sequence_confidence,
                'sequence_strength': sequence_strength,
                'pattern_consistency': consistency_score,
                'analysis_details': {
                    'global_frequency': global_player_freq,
                    'recent_frequency': recent_player_freq,
                    'streak_info': current_streak_info,
                    'alternation_rate': alternation_rate,
                    'trend_analysis': trend_analysis,
                    'cycle_analysis': cycle_analysis,
                    'weights_used': weights,
                    'individual_predictions': {
                        'frequency': frequency_prediction,
                        'recent': recent_prediction,
                        'streak': streak_prediction,
                        'alternation': alternation_prediction,
                        'trend': trend_prediction,
                        'cycle': cycle_prediction
                    }
                }
            }

        except Exception as e:
            logger.error(f"Erreur analyse séquence complète: {e}")
            return {
                'weighted_prediction': 0.5,
                'sequence_confidence': 0.4,
                'sequence_strength': 0.3,
                'pattern_consistency': 0.3,
                'analysis_details': {'error': str(e)}
            }

    def _analyze_sequence_trends(self, sequence: List[int], window_size: int) -> Dict[str, Any]:
        """Analyse tendances par fenêtres glissantes"""
        try:
            if len(sequence) < window_size:
                return {'trend_prediction': 0.5, 'trend_strength': 0.0}

            # Calcul tendances par fenêtres
            trends = []
            for i in range(len(sequence) - window_size + 1):
                window = sequence[i:i + window_size]
                player_ratio = sum(1 for x in window if x == 0) / window_size
                trends.append(player_ratio)

            if len(trends) < 2:
                return {'trend_prediction': 0.5, 'trend_strength': 0.0}

            # Analyse évolution tendance
            recent_trend = trends[-1]
            trend_evolution = recent_trend - trends[0]
            trend_strength = abs(trend_evolution)

            # Prédiction basée sur continuation tendance
            if trend_strength > 0.2:  # Tendance significative
                if trend_evolution > 0:  # Tendance croissante vers Player
                    trend_prediction = min(0.8, 0.5 + trend_strength)
                else:  # Tendance décroissante vers Banker
                    trend_prediction = max(0.2, 0.5 - trend_strength)
            else:
                trend_prediction = 0.5  # Pas de tendance claire

            return {
                'trend_prediction': trend_prediction,
                'trend_strength': trend_strength,
                'trend_evolution': trend_evolution,
                'recent_trend': recent_trend,
                'trends': trends
            }

        except Exception as e:
            logger.error(f"Erreur analyse tendances: {e}")
            return {'trend_prediction': 0.5, 'trend_strength': 0.0}

    def _analyze_cyclical_patterns(self, sequence: List[int], sequence_length: int) -> Dict[str, Any]:
        """Analyse patterns cycliques dans la séquence"""
        try:
            if sequence_length < 6:
                return {'cycle_prediction': 0.5, 'cycle_confidence': 0.0}

            # Recherche cycles de longueur 2 à 8
            best_cycle = None
            best_confidence = 0.0

            for cycle_length in range(2, min(9, sequence_length // 2 + 1)):
                cycle_confidence = self._detect_cycle_pattern(sequence, cycle_length)
                if cycle_confidence > best_confidence:
                    best_confidence = cycle_confidence
                    best_cycle = cycle_length

            # Prédiction basée sur meilleur cycle détecté
            if best_cycle and best_confidence > 0.3:
                # Position dans le cycle
                position_in_cycle = sequence_length % best_cycle

                # Recherche pattern du cycle
                cycle_pattern = []
                for i in range(0, sequence_length - best_cycle + 1, best_cycle):
                    if i + best_cycle <= sequence_length:
                        cycle_pattern.append(sequence[i:i + best_cycle])

                if cycle_pattern:
                    # Prédiction basée sur position dans cycle
                    if position_in_cycle < best_cycle:
                        # Moyenne des valeurs à cette position dans les cycles précédents
                        position_values = [cycle[position_in_cycle] for cycle in cycle_pattern
                                         if len(cycle) > position_in_cycle]
                        if position_values:
                            cycle_prediction = sum(position_values) / len(position_values)
                            # Ajuste vers 0 (Player) ou 1 (Banker)
                            cycle_prediction = 1.0 - cycle_prediction if cycle_prediction < 0.5 else cycle_prediction
                        else:
                            cycle_prediction = 0.5
                    else:
                        cycle_prediction = 0.5
                else:
                    cycle_prediction = 0.5
            else:
                cycle_prediction = 0.5
                best_confidence = 0.0

            return {
                'cycle_prediction': cycle_prediction,
                'cycle_confidence': best_confidence,
                'best_cycle_length': best_cycle,
                'cycle_strength': best_confidence
            }

        except Exception as e:
            logger.error(f"Erreur analyse cyclique: {e}")
            return {'cycle_prediction': 0.5, 'cycle_confidence': 0.0}

    def _detect_cycle_pattern(self, sequence: List[int], cycle_length: int) -> float:
        """Détecte la force d'un pattern cyclique de longueur donnée"""
        try:
            if len(sequence) < cycle_length * 2:
                return 0.0

            # Compare segments de même longueur
            matches = 0
            comparisons = 0

            for i in range(len(sequence) - cycle_length):
                if i + cycle_length * 2 <= len(sequence):
                    segment1 = sequence[i:i + cycle_length]
                    segment2 = sequence[i + cycle_length:i + cycle_length * 2]

                    # Compare éléments
                    for j in range(cycle_length):
                        if segment1[j] == segment2[j]:
                            matches += 1
                        comparisons += 1

            if comparisons == 0:
                return 0.0

            # Confiance = ratio de correspondances
            confidence = matches / comparisons
            return confidence

        except Exception as e:
            logger.error(f"Erreur détection cycle: {e}")
            return 0.0

    def _calculate_streak_probability(self, sequence: List[int], streak_length: int, streak_type: str) -> float:
        """Calcule probabilité basée sur analyse probabiliste des streaks"""
        try:
            if len(sequence) < 3:
                return 0.5

            # 📊 ANALYSE HISTORIQUE DES STREAKS
            # Compte toutes les streaks dans l'historique
            streak_stats = {'Player': [], 'Banker': []}
            current_type = 'Player' if sequence[0] == 0 else 'Banker'
            current_length = 1

            for i in range(1, len(sequence)):
                if sequence[i] == sequence[i-1]:
                    current_length += 1
                else:
                    streak_stats[current_type].append(current_length)
                    current_type = 'Player' if sequence[i] == 0 else 'Banker'
                    current_length = 1

            # Ajoute la streak actuelle
            streak_stats[current_type].append(current_length)

            # 🎯 CALCUL PROBABILISTE
            if streak_length >= 2:
                # Probabilité qu'une streak continue vs se termine
                all_streaks = streak_stats[streak_type]
                if len(all_streaks) >= 2:
                    # Streaks de même longueur ou plus dans l'historique
                    longer_streaks = [s for s in all_streaks if s > streak_length]
                    continuation_rate = len(longer_streaks) / len(all_streaks)

                    # Ajuste selon longueur actuelle (plus c'est long, moins probable de continuer)
                    probability_factor = max(0.1, 1.0 - (streak_length * 0.15))
                    final_continuation_prob = continuation_rate * probability_factor

                    # Retourne probabilité Player (0) ou Banker (1)
                    if streak_type == 'Player':
                        return max(0.2, min(0.8, final_continuation_prob))  # Probabilité Player continue
                    else:
                        return max(0.2, min(0.8, 1.0 - final_continuation_prob))  # Probabilité Player (opposé)

            # Fréquence globale comme fallback
            player_freq = sum(1 for x in sequence if x == 0) / len(sequence)
            return player_freq

        except Exception as e:
            logger.error(f"Erreur calcul probabilité streak: {e}")
            return 0.5

    def _calculate_alternation_probability(self, sequence: List[int], alternation_rate: float) -> float:
        """Calcule probabilité basée sur analyse probabiliste de l'alternance"""
        try:
            if len(sequence) < 3:
                return 0.5

            # 📊 ANALYSE PATTERNS D'ALTERNANCE
            # Compte les transitions dans l'historique
            transitions = {'0->1': 0, '1->0': 0, '0->0': 0, '1->1': 0}

            for i in range(len(sequence) - 1):
                current = str(sequence[i])
                next_val = str(sequence[i + 1])
                key = f"{current}->{next_val}"
                transitions[key] += 1

            total_transitions = sum(transitions.values())
            if total_transitions == 0:
                return 0.5

            # 🎯 CALCUL PROBABILISTE BASÉ SUR DERNIER RÉSULTAT
            last_result = sequence[-1]

            if last_result == 0:  # Dernier était Player
                # Probabilité de transition Player -> Player vs Player -> Banker
                prob_stay_player = transitions['0->0'] / max(1, transitions['0->0'] + transitions['0->1'])
                prob_to_banker = transitions['0->1'] / max(1, transitions['0->0'] + transitions['0->1'])

                # Pondère selon taux d'alternance global
                if alternation_rate > 0.6:  # Forte alternance historique
                    return max(0.2, min(0.8, prob_to_banker * 1.2))  # Favorise transition vers Banker
                elif alternation_rate < 0.4:  # Faible alternance historique
                    return max(0.2, min(0.8, prob_stay_player * 1.2))  # Favorise rester Player
                else:
                    # Équilibré : utilise probabilités historiques pures
                    return max(0.2, min(0.8, prob_stay_player))

            else:  # Dernier était Banker
                # Probabilité de transition Banker -> Player vs Banker -> Banker
                prob_to_player = transitions['1->0'] / max(1, transitions['1->0'] + transitions['1->1'])
                prob_stay_banker = transitions['1->1'] / max(1, transitions['1->0'] + transitions['1->1'])

                # Pondère selon taux d'alternance global
                if alternation_rate > 0.6:  # Forte alternance historique
                    return max(0.2, min(0.8, prob_to_player * 1.2))  # Favorise transition vers Player
                elif alternation_rate < 0.4:  # Faible alternance historique
                    return max(0.2, min(0.8, 1.0 - prob_stay_banker * 1.2))  # Favorise rester Banker (retourne prob Player)
                else:
                    # Équilibré : utilise probabilités historiques pures
                    return max(0.2, min(0.8, prob_to_player))

        except Exception as e:
            logger.error(f"Erreur calcul probabilité alternance: {e}")
            return 0.5

    def learn_from_result(self, predicted_outcome: int, actual_outcome: int,
                         confidence: float):
        """Apprentissage optimisé à partir du résultat réel avec récompenses adaptées"""
        try:
            # 🎯 CALCUL MÉTRIQUES DE PERFORMANCE
            prediction_correct = predicted_outcome == actual_outcome
            prediction_accuracy = 1.0 if prediction_correct else 0.0

            # Enregistrement historique
            self.training_history['prediction_accuracy'].append(prediction_accuracy)

            # 🎯 CALCUL RÉCOMPENSE BACCARAT OPTIMISÉE
            baccarat_reward = self._calculate_baccarat_reward(
                predicted_outcome, actual_outcome, confidence
            )

            # 🎯 CALCUL RÉCOMPENSE PROGRESSIVE
            progressive_reward = self._calculate_progressive_reward()

            # 🎯 CALCUL RÉCOMPENSE D'AMÉLIORATION
            improvement_reward = self._calculate_improvement_reward()

            # 🎯 RÉCOMPENSE TOTALE COMPOSITE
            total_reward = (0.5 * baccarat_reward +
                           0.3 * progressive_reward +
                           0.2 * improvement_reward)

            # 🎯 AJUSTEMENT LEARNING RATE ADAPTATIF BASÉ SUR RÉCOMPENSE
            if total_reward > 0.7:
                # Haute performance → Learning rate modéré
                adaptive_lr = self.learning_rate * 1.2
            elif total_reward > 0.4:
                # Performance moyenne → Learning rate standard
                adaptive_lr = self.learning_rate
            else:
                # Faible performance → Learning rate élevé
                adaptive_lr = self.learning_rate * 2.0

            # 🎯 MISE À JOUR PARAMÈTRES BASÉE SUR RÉCOMPENSE TOTALE (PEUT ÊTRE NÉGATIVE)
            if prediction_correct:
                # Prédiction correcte : renforcement proportionnel à la récompense
                self.shared_parameters['confidence_adjustment'] += adaptive_lr * 0.05 * total_reward

                # Ajustement biais conservateur
                bias_adjustment = adaptive_lr * 0.02 * total_reward
                if actual_outcome == 0:  # Player correct
                    self.shared_parameters['prediction_bias'] += bias_adjustment
                else:  # Banker correct
                    self.shared_parameters['prediction_bias'] -= bias_adjustment
            else:
                # Prédiction incorrecte : correction FORTE avec récompenses négatives
                # Plus la récompense est négative, plus la correction est forte
                correction_factor = abs(total_reward) * adaptive_lr * 2.0  # Amplification pour récompenses négatives

                # Réduit confiance FORTEMENT en cas de récompense négative
                self.shared_parameters['confidence_adjustment'] -= correction_factor * 0.15

                # Ajuste biais FORTEMENT vers résultat correct
                bias_correction = correction_factor * 0.08
                if actual_outcome == 0:  # Player était correct
                    self.shared_parameters['prediction_bias'] += bias_correction
                else:  # Banker était correct
                    self.shared_parameters['prediction_bias'] -= bias_correction

                # 🚨 PÉNALITÉ ESCALADANTE pour séquences d'échecs
                failure_streak = self._get_current_failure_streak()
                if failure_streak >= 3:
                    # Pénalité escaladante : plus d'échecs = correction plus forte
                    escalation_factor = min(3.0, 1.0 + (failure_streak - 2) * 0.5)
                    self.shared_parameters['confidence_adjustment'] -= 0.1 * escalation_factor
                    logger.warning(f"🚨 Pénalité escaladante appliquée: streak={failure_streak}, "
                                 f"factor={escalation_factor:.2f}")

            # 🎯 APPLIQUE LIMITES OPTIMISÉES
            self.shared_parameters['prediction_bias'] = max(-0.15, min(0.15,
                self.shared_parameters['prediction_bias']))
            self.shared_parameters['confidence_adjustment'] = max(-0.4, min(0.4,
                self.shared_parameters['confidence_adjustment']))

            logger.info(f"🎯 AZR apprentissage optimisé: correct={prediction_correct}, "
                       f"reward_total={total_reward:.3f}, "
                       f"biais={self.shared_parameters['prediction_bias']:.3f}, "
                       f"conf_adj={self.shared_parameters['confidence_adjustment']:.3f}")

        except Exception as e:
            logger.error(f"Erreur apprentissage résultat optimisé: {e}")

    def _calculate_adaptive_lambda(self) -> float:
        """Calcule lambda adaptatif basé sur performance récente"""
        try:
            if len(self.training_history['prediction_accuracy']) < 5:
                return self.adaptive_lambda

            # Performance récente sur fenêtre
            recent_accuracy = np.mean(self.training_history['prediction_accuracy'][-self.performance_window:])

            # Ajustement lambda basé sur performance vs cible
            if recent_accuracy < self.target_accuracy:
                # Performance faible → Plus d'accent sur apprentissage
                return max(0.3, self.adaptive_lambda - 0.1)
            else:
                # Performance bonne → Plus d'accent sur exploitation
                return min(0.9, self.adaptive_lambda + 0.05)

        except Exception as e:
            logger.error(f"Erreur calcul lambda adaptatif: {e}")
            return self.adaptive_lambda

    def _calculate_baccarat_reward(self, predicted_outcome: int, actual_outcome: int,
                                  confidence: float) -> float:
        """Calcule récompense optimisée pour Baccarat avec pénalités négatives"""
        try:
            # Récompense de base : +1.0 pour succès, -0.5 pour échec
            base_reward = 1.0 if predicted_outcome == actual_outcome else -0.5

            # Bonus/malus de confiance calibrée
            if predicted_outcome == actual_outcome:
                # Succès : bonus proportionnel à la confiance
                confidence_bonus = confidence * 0.4
            else:
                # Échec : pénalité proportionnelle à la confiance (surconfiance punie)
                confidence_bonus = -confidence * 0.6

            # Récompense finale (PEUT ÊTRE NÉGATIVE)
            total_reward = base_reward + confidence_bonus
            return max(-1.5, min(1.5, total_reward))  # ✅ PERMET RÉCOMPENSES NÉGATIVES

        except Exception as e:
            logger.error(f"Erreur calcul récompense Baccarat: {e}")
            return -0.5  # Pénalité par défaut en cas d'erreur

    def _calculate_progressive_reward(self) -> float:
        """Calcule récompense progressive avec pénalités pour séquences d'échecs"""
        try:
            if len(self.training_history['prediction_accuracy']) < 2:
                return 0.0

            # Calcul streak actuelle (correcte)
            current_correct_streak = 0
            for i in range(len(self.training_history['prediction_accuracy']) - 1, -1, -1):
                if self.training_history['prediction_accuracy'][i] == 1.0:
                    current_correct_streak += 1
                else:
                    break

            # Calcul streak d'échecs actuelle
            current_failure_streak = 0
            for i in range(len(self.training_history['prediction_accuracy']) - 1, -1, -1):
                if self.training_history['prediction_accuracy'][i] == 0.0:
                    current_failure_streak += 1
                else:
                    break

            # Bonus pour séquences correctes
            streak_bonus = min(1.0, current_correct_streak * self.streak_bonus_factor)

            # PÉNALITÉ FORTE pour séquences d'échecs (escalade)
            failure_penalty = min(1.5, current_failure_streak * 0.3)

            # Malus pour mauvaise calibration récente
            recent_window = min(10, len(self.training_history['prediction_accuracy']))
            recent_accuracy = np.mean(self.training_history['prediction_accuracy'][-recent_window:])
            calibration_penalty = abs(recent_accuracy - 0.6) * self.calibration_penalty_factor

            progressive_reward = streak_bonus - failure_penalty - calibration_penalty
            return max(-1.5, min(1.5, progressive_reward))  # ✅ PERMET RÉCOMPENSES NÉGATIVES

        except Exception as e:
            logger.error(f"Erreur calcul récompense progressive: {e}")
            return -0.2

    def _calculate_improvement_reward(self) -> float:
        """Calcule récompense basée sur amélioration/dégradation des performances"""
        try:
            if len(self.training_history['prediction_accuracy']) < self.performance_window:
                return 0.0

            # Performance actuelle vs précédente
            current_window = self.training_history['prediction_accuracy'][-self.performance_window:]
            previous_window = self.training_history['prediction_accuracy'][-2*self.performance_window:-self.performance_window]

            if len(previous_window) < self.performance_window:
                return 0.0

            current_accuracy = np.mean(current_window)
            previous_accuracy = np.mean(previous_window)
            improvement = current_accuracy - previous_accuracy

            # Récompense/pénalise l'amélioration/dégradation
            if improvement > 0:
                # Amélioration : récompense proportionnelle
                return min(1.5, improvement * 3.0)
            else:
                # Dégradation : pénalité proportionnelle (NÉGATIVE)
                return max(-1.5, improvement * 2.0)  # ✅ PERMET RÉCOMPENSES NÉGATIVES

        except Exception as e:
            logger.error(f"Erreur calcul récompense amélioration: {e}")
            return -0.2

    def _get_current_failure_streak(self) -> int:
        """Calcule la séquence d'échecs actuelle"""
        try:
            if not self.training_history['prediction_accuracy']:
                return 0

            failure_streak = 0
            for i in range(len(self.training_history['prediction_accuracy']) - 1, -1, -1):
                if self.training_history['prediction_accuracy'][i] == 0.0:
                    failure_streak += 1
                else:
                    break
            return failure_streak
        except Exception as e:
            logger.error(f"Erreur calcul failure streak: {e}")
            return 0

    def reset_system(self):
        """Réinitialise complètement le système AZR pour une nouvelle partie"""
        try:
            logger.info("🔄 Début réinitialisation complète système AZR")

            # 1. Reset compteurs principaux
            self.training_iterations = 0

            # 2. Reset paramètres partagés aux valeurs par défaut
            self.shared_parameters = {
                'confidence_adjustment': 0.0,
                'prediction_bias': 0.0,
                'decision_threshold': 0.5,
                'pattern_weights': {
                    'frequency': 0.4,
                    'streaks': 0.3,
                    'alternation': 0.3
                }
            }

            # 3. Reset historique d'apprentissage complet
            self.training_history = {
                'learnability_rewards': [],
                'solution_rewards': [],
                'combined_rewards': [],
                'performance_metrics': [],
                'prediction_accuracy': [],
                'confidence_calibration': [],
                'correct_streaks': [],
                'adaptive_lambda_history': []
            }

            # 4. Reset métriques des composants
            if hasattr(self.proposer, 'performance_metrics'):
                self.proposer.performance_metrics = {
                    'tasks_proposed': 0,
                    'tasks_validated': 0,
                    'avg_task_difficulty': 0.5,
                    'diversity_score': 0.0
                }
                self.proposer.task_history.clear()

            if hasattr(self.solver, 'performance_metrics'):
                self.solver.performance_metrics = {
                    'solutions_attempted': 0,
                    'solutions_successful': 0,
                    'avg_confidence': 0.5,
                    'avg_quality_score': 0.5
                }
                self.solver.solution_history.clear()
                if hasattr(self.solver, 'learned_patterns'):
                    self.solver.learned_patterns.clear()

            # 5. Reset historiques spécialisés
            if hasattr(self, '_reward_history_by_type'):
                self._reward_history_by_type.clear()

            # 6. Reset lambda adaptatif
            self.adaptive_lambda = 0.7

            # 7. Nettoyage mémoire
            import gc
            gc.collect()

            logger.info("✅ Système AZR réinitialisé complètement")
            return True

        except Exception as e:
            logger.error(f"Erreur réinitialisation AZR: {e}")
            return False

    def get_system_status(self) -> Dict[str, Any]:
        """Retourne statut complet du système AZR avec métriques optimisées"""
        try:
            # Calcul métriques récentes
            recent_accuracy = (np.mean(self.training_history['prediction_accuracy'][-10:])
                             if self.training_history['prediction_accuracy'] else 0.0)

            current_lambda = (self.training_history['adaptive_lambda_history'][-1]
                            if self.training_history['adaptive_lambda_history'] else self.adaptive_lambda)

            return {
                'training_iterations': self.training_iterations,
                'shared_parameters': self.shared_parameters.copy(),
                'proposer_metrics': self.proposer.performance_metrics.copy(),
                'solver_metrics': self.solver.performance_metrics.copy(),
                'recent_performance': {
                    'avg_learnability_reward': np.mean(self.training_history['learnability_rewards'][-10:])
                        if self.training_history['learnability_rewards'] else 0.0,
                    'avg_solution_reward': np.mean(self.training_history['solution_rewards'][-10:])
                        if self.training_history['solution_rewards'] else 0.0,
                    'avg_combined_reward': np.mean(self.training_history['combined_rewards'][-10:])
                        if self.training_history['combined_rewards'] else 0.0,
                    'recent_accuracy': recent_accuracy,
                    'current_adaptive_lambda': current_lambda,
                    'target_accuracy': self.target_accuracy
                },
                'reward_system_status': {
                    'adaptive_lambda_enabled': True,
                    'progressive_rewards_enabled': True,
                    'improvement_tracking_enabled': True,
                    'confidence_calibration_enabled': True
                }
            }
        except Exception as e:
            logger.error(f"Erreur statut système: {e}")
            return {'error': str(e)}
