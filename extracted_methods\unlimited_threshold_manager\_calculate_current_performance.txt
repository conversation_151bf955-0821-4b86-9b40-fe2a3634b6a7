# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\unlimited_threshold_manager.py
# Lignes: 324 à 344
# Type: Méthode de la classe UnlimitedThresholdManager

    def _calculate_current_performance(self) -> float:
        """Calcule performance actuelle"""
        try:
            if len(self.performance_history) < 5:
                return 0.5
            
            recent_results = list(self.performance_history)[-20:]
            accuracy = sum(1 for r in recent_results if r['correct']) / len(recent_results)
            
            # Score composite (accuracy + confiance + stabilité)
            confidences = [r['confidence'] for r in recent_results]
            avg_confidence = np.mean(confidences)
            confidence_stability = 1.0 - np.var(confidences)
            
            performance_score = (accuracy * 0.6 + avg_confidence * 0.3 + confidence_stability * 0.1)
            
            return np.clip(performance_score, 0.0, 1.0)
            
        except Exception as e:
            logger.error(f"Erreur calcul performance: {e}")
            return 0.5