# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 545 à 584
# Type: Méthode de la classe BaccaratPatternProposer

    def update_pattern_performance(self, pattern: Dict, success: bool):
        """
        Met à jour performance d'un pattern + OPTIMISATION LONGUEURS
        Enregistre performance par longueur pour auto-découverte optimums
        """
        pattern_key = self._get_pattern_key(pattern)

        if success:
            if pattern_key not in self.successful_patterns:
                self.successful_patterns[pattern_key] = 0
            self.successful_patterns[pattern_key] += 1
        else:
            if pattern_key not in self.failed_patterns:
                self.failed_patterns[pattern_key] = 0
            self.failed_patterns[pattern_key] += 1

        # ═══════════════════════════════════════════════════════════════════
        # MISE À JOUR PERFORMANCE PAR LONGUEUR POUR AUTO-OPTIMISATION
        # ═══════════════════════════════════════════════════════════════════

        pattern_type = pattern.get('type')

        # Mise à jour performance selon type de pattern
        if pattern_type == 'sequence':
            length = pattern.get('length')
            if length:
                self._update_sequence_length_performance(length, success)
        elif pattern_type == 'cycle':
            cycle_length = pattern.get('cycle_length')
            if cycle_length:
                self._update_cycle_length_performance(cycle_length, success)
        elif pattern_type == 'streak':
            streak_length = pattern.get('current_streak', {}).get('length')
            if streak_length:
                self._update_streak_length_performance(streak_length, success)

        # Optimisation périodique des longueurs
        self.pattern_attempts_count += 1
        if self.pattern_attempts_count % self.optimization_frequency == 0:
            self._optimize_sequence_lengths()