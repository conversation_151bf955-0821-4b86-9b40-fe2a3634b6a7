# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 490 à 494
# Type: Méthode de la classe AdaptiveReasoner

    def _calculate_session_accuracy(self) -> float:
        """Calcule précision de la session"""
        if self.total_predictions == 0:
            return 0.0
        return self.successful_predictions / self.total_predictions