# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\adaptive_reasoner.py
# Lignes: 744 à 778
# Type: Méthode de la classe AdaptiveReasoner

    def _optimize_meta_parameters(self):
        """Optimise méta-paramètres en temps réel"""
        try:
            if len(self.parameter_performance_history) < self.parameter_optimization_window:
                return  # Pas assez d'historique

            # Analyse performance récente
            recent_performance = self.parameter_performance_history[-self.parameter_optimization_window:]
            current_avg_accuracy = np.mean([p['accuracy'] for p in recent_performance])

            # Comparaison avec performance précédente
            if len(self.parameter_performance_history) >= self.parameter_optimization_window * 2:
                previous_performance = self.parameter_performance_history[-self.parameter_optimization_window * 2:-self.parameter_optimization_window]
                previous_avg_accuracy = np.mean([p['accuracy'] for p in previous_performance])

                performance_trend = current_avg_accuracy - previous_avg_accuracy
            else:
                performance_trend = 0.0

            # Optimisation adaptative basée sur performance
            if performance_trend < -0.05:  # Performance dégradée
                self._adjust_parameters_for_improvement()
                logger.info(f"Optimisation méta-paramètres: Performance dégradée ({performance_trend:.3f}), ajustement appliqué")
            elif performance_trend > 0.05:  # Performance améliorée
                self._reinforce_current_parameters()
                logger.info(f"Optimisation méta-paramètres: Performance améliorée ({performance_trend:.3f}), paramètres renforcés")
            else:  # Performance stable
                self._explore_parameter_space()
                logger.debug("Optimisation méta-paramètres: Performance stable, exploration légère")

            # Mise à jour score performance
            self.parameter_performance_score = current_avg_accuracy

        except Exception as e:
            logger.error(f"Erreur optimisation méta-paramètres: {e}")