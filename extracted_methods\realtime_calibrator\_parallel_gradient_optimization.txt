# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\realtime_calibrator.py
# Lignes: 623 à 664
# Type: Méthode de la classe RealtimeCalibrator

    def _parallel_gradient_optimization(self) -> Dict[str, float]:
        """Optimisation gradient parallélisée"""
        try:
            current_performance = self._calculate_current_performance()
            gradients = {}

            # Calcul gradient pour chaque paramètre en parallèle
            def calculate_parameter_gradient(param_name: str) -> Tuple[str, float]:
                current_value = self.calibrated_parameters[param_name]
                min_val, max_val = self.parameter_ranges[param_name]

                # Perturbation pour calcul gradient
                delta = (max_val - min_val) * 0.01  # 1% de la plage

                # Test valeur augmentée
                test_params = self.calibrated_parameters.copy()
                test_params[param_name] = min(current_value + delta, max_val)
                performance_plus = self._simulate_performance_with_parameters(test_params)

                # Test valeur diminuée
                test_params[param_name] = max(current_value - delta, min_val)
                performance_minus = self._simulate_performance_with_parameters(test_params)

                # Calcul gradient
                gradient = (performance_plus - performance_minus) / (2 * delta)

                return param_name, gradient

            # Calcul parallèle gradients
            with ThreadPoolExecutor(max_workers=len(self.calibrated_parameters)) as executor:
                futures = [executor.submit(calculate_parameter_gradient, param)
                          for param in self.calibrated_parameters.keys()]

                for future in futures:
                    param_name, gradient = future.result()
                    gradients[param_name] = gradient

            return gradients

        except Exception as e:
            logger.error(f"Erreur optimisation gradient: {e}")
            return {}