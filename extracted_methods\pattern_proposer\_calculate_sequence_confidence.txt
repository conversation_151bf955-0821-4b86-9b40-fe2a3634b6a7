# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_proposer.py
# Lignes: 328 à 362
# Type: Méthode de la classe BaccaratPatternProposer

    def _calculate_sequence_confidence(self, sequence: List[int], results: List[int]) -> float:
        """
        Calcule confiance d'une séquence - LOGIQUE BACCARAT PURE
        SUPPRESSION fréquences relatives statistiques inadaptées au Baccarat
        """
        try:
            # ═══════════════════════════════════════════════════════════════════
            # LOGIQUE BACCARAT SPÉCIFIQUE (pas de fréquences relatives)
            # ═══════════════════════════════════════════════════════════════════

            if len(sequence) > len(results):
                return 0.0

            # Vérification séquence récente (plus important que fréquence globale)
            recent_window = min(20, len(results))  # Fenêtre récente Baccarat
            recent_results = results[-recent_window:]

            # Bonus majeur si séquence apparaît en fin
            if len(results) >= len(sequence):
                if results[-len(sequence):] == sequence:
                    return 0.8  # Confiance élevée pour pattern immédiat

            # Recherche dans fenêtre récente seulement
            recent_count = self._count_sequence_frequency(sequence, recent_results)

            # Logique Baccarat: patterns récents plus significatifs
            if recent_count >= 2:
                return 0.6  # Pattern récurrent récent
            elif recent_count == 1:
                return 0.4  # Pattern présent récemment
            else:
                return 0.1  # Pattern absent récemment

        except Exception:
            return 0.0