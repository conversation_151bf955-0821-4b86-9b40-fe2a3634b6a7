# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\travail\azr_baccarat_programme\pattern_validator.py
# Lignes: 432 à 452
# Type: Méthode de la classe BaccaratPatternValidator

    def _update_validation_history(self, pattern: Dict, validation_result: Dict, reward: float):
        """Met à jour historique de validation"""
        validation_entry = {
            'timestamp': time.time(),
            'pattern_type': pattern['type'],
            'pattern': pattern,
            'validation_result': validation_result,
            'reward': reward
        }

        self.validation_history.append(validation_entry)

        # Limitation taille historique
        max_history = global_config.azr.max_validation_history
        if len(self.validation_history) > max_history:
            self.validation_history = self.validation_history[-max_history:]

        # Mise à jour performance par type
        if not validation_result.get('awaiting_outcome'):
            success = validation_result.get('success', False)
            self.pattern_performance[pattern['type']].append(1.0 if success else 0.0)